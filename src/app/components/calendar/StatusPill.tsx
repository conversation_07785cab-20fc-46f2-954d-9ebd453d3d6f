import { BlockedType, CalendarLeasedType } from "@/types/calendar";

import classNames from "classnames";
import { twMerge } from "tailwind-merge";

type Props = {
  title: string;
  type: BlockedType;
  className?: string;
};

const StatusPill = ({ title, type, className = "" }: Props) => {
  return (
    <div
      className={twMerge(
        "text-[10px] md:text-xs flex items-center gap-2",
        className
      )}
    >
      <div
        className={classNames("w-3 md:w-4 h-3 md:h-4 rounded-full", {
          "bg-leased": type === BlockedType.LEASED,
          "bg-owner-time": type === BlockedType.OWNER_TIME,
          "bg-other": type === BlockedType.OTHER,
        })}
      />
      {title}
    </div>
  );
};

export default StatusPill;
