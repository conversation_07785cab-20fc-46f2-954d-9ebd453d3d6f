import { CalendarDaysIcon } from '@heroicons/react/24/outline';

import dayjs from 'dayjs';

type Props = {
  selectionStart?: string;
  selectionEnd?: string;
};

const ManageCalendarSelectedDates = ({ selectionStart, selectionEnd }: Props) => {
  return (
    <div className='text-xs rounded-2xl font-normal w-full px-8 md:px-2 py-2 md:py-4 border border-black'>
      <div className='w-full'>
        <div className='flex-center-between md:px-4'>
          <p className='text-xs md:hidden'>Start Date</p>
          <CalendarDaysIcon className='w-6 h-6 hidden md:block' />
          <p className='text-xs md:hidden'>End Date</p>
          <CalendarDaysIcon className='w-6 h-6 hidden md:block' />
        </div>
        <div className='m-auto font-bold w-[10px] h-[1px] bg-black md:my-1' />
        <div className='flex-center-between'>
          <p className='text-sm md:text-xs font-bold'>
            {dayjs(selectionStart).format(`MMM D, YYYY`)}
          </p>
          <p className='text-sm md:text-xs font-bold'>
            {selectionEnd ? dayjs(selectionEnd).format(`MMM D, YYYY`) : `End Date`}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ManageCalendarSelectedDates;
