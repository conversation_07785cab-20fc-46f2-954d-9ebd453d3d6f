import { UpdateLogType } from "@/types/calendar";
import { formatUpdateLogContentString } from "@/utils/calendar";
import { isJsonString } from "@/utils/common";
import { memo } from "react";

type Props = {
  type: UpdateLogType;
  value: string | boolean;
  contentType?: string;
};

const LogContentItem = ({ type, value, contentType }: Props) => {
  if (
    type === UpdateLogType.RATES &&
    typeof value === "string" &&
    isJsonString(value)
  ) {
    const json = JSON.parse(value);
    return (
      <span className="font-medium ml-2">
        {json["weekly_amount"]
          ? formatUpdateLogContentString(json["weekly_amount"], type)
          : "N/A"}
      </span>
    );
  }
  return (
    <span className="font-medium ml-2">
      {formatUpdateLogContentString(value, type, contentType)}
    </span>
  );
};

export default memo(LogContentItem);
