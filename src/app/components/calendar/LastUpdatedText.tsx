import { getCalendarUpdateLogs } from "@/app/actions/calendar";
import { UpdateLog } from "@/types/calendar";
import dayjs from "dayjs";

type Props = {
  propertyId: number;
};
const LastUpdatedText = async ({ propertyId }: Props) => {
  const { results } = await getCalendarUpdateLogs<{
    count: number;
    next: string;
    results: UpdateLog[];
  }>(propertyId, 1, 0);

  return (
    <p className="text-[10px] md:text-sm">
      Last Updated:{" "}
      <span className="font-bold">
        {dayjs(results?.[0]?.created_at).format("MM/DD/YYYY")} by{" "}
        {results?.[0]?.user_name}
      </span>
    </p>
  );
};

export default LastUpdatedText;
