import LogoutButton from "@/clients/views/common/LogoutButton";
import { UserProfile } from "@/types/profile";
import { getUserFullName } from "@/utils/profile";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import Image from "next/image";

type Props = {
  profile: UserProfile;
};
const UserAvatar = ({ profile }: Props) => {
  return (
    <div className="dropdown dropdown-end">
      <div
        tabIndex={0}
        role="button"
        className="flex items-center text-[#8BABB6]"
      >
        <p className="text-sm p-4">{getUserFullName(profile)}</p>
        {profile?.avatar ? (
          <Image
            alt="user avatar"
            src={profile.avatar}
            width={0}
            height={0}
            className="w-10 h-10 rounded-full"
            sizes="40px"
          />
        ) : (
          <div className="w-10 h-10 rounded-full bg-roman-silver text-white text-sm font-bold flex-center-center">
            M
          </div>
        )}

        <ChevronDownIcon className="ml-4 w-4 h-4" />
      </div>
      <ul
        tabIndex={0}
        className="dropdown-content menu bg-base-100 rounded-lg z-[1] w-52 p-0 py-2 shadow-dropdown"
      >
        <li className="hover:bg-carolina-blue-60">
          <a className="rounded-none">Profile</a>
        </li>
        <li className="hover:bg-carolina-blue-60">
          <a className="rounded-none">Change Password</a>
        </li>
        <li className="hover:bg-carolina-blue-60">
          <LogoutButton />
        </li>
      </ul>
    </div>
  );
};

export default UserAvatar;
