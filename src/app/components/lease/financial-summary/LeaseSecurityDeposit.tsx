import { Payments, SecurityDeposit } from '@/types/lease';
import {
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import SecurityDepositReturnForm from '@/clients/views/lease/financial-summary/SecurityDepositReturnForm';
import { Nullable } from '@/types/common';

type Props = {
  securityDeposit: Nullable<SecurityDeposit>;
  lastPayment: Payments;
  leaseId: number;
};
const LeaseSecurityDeposit = ({
  securityDeposit,
  leaseId,
  lastPayment,
}: Props) => {
  return (
    <div className="p-4 bg-white rounded-md">
      <p className="m-0 text-sm font-semibold flex items-center">
        {securityDeposit?.status === 'Paid' ? (
          <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500 mr-4" />
        ) : (
          <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500 mr-4" />
        )}
        Security Deposit
      </p>

      <SecurityDepositReturnForm
        leaseId={leaseId}
        securityDeposit={securityDeposit}
        lastPayment={lastPayment}
      />
    </div>
  );
};

export default LeaseSecurityDeposit;
