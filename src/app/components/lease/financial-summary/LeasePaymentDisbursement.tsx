import Button from '@/clients/ui/button';
import DisbursementFormItem from '@/clients/views/lease/financial-summary/DisbursementFormItem';
import { LeaseQbInfo, Payments } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';
import dayjs from 'dayjs';

type Props = {
  payment: Payments;
  isAdmin: boolean;
};

const LeasePaymentDisbursement = ({ payment, isAdmin }: Props) => {
  const showDisbursementForm = !!payment.disbursement_form.submit_at;
  return (
    <>
      <table className="w-full text-xs overflow-x-auto">
        <thead className="text-black/60">
          <tr className="border-b border-outline">
            <th className="py-2 text-left font-normal w-[20%]">Description</th>
            <th className="py-2 text-left font-normal w-[30%]">Date</th>
            <th className="py-2 text-left font-normal w-[35%]">Reference</th>
            <th className="py-2 font-normal w-[15%] text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Occupancy Tax</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.Occupancy_Tax_Date}
                    refName={LeaseQbInfo.Occupancy_Tax_Ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.Occupancy_Tax_Date]
                          ? payment.qb_info[LeaseQbInfo.Occupancy_Tax_Date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.Occupancy_Tax_Ref]
                          ? payment.qb_info[LeaseQbInfo.Occupancy_Tax_Ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.tax ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Booking Fee</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.Processing_fee_Date}
                    refName={LeaseQbInfo.Processing_fee_Ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.Processing_fee_Date]
                          ? payment.qb_info[LeaseQbInfo.Processing_fee_Date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.Processing_fee_Ref]
                          ? payment.qb_info[LeaseQbInfo.Processing_fee_Ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.processing_fee ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Rent to Owner</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.Rent_to_Owner_Date}
                    refName={LeaseQbInfo.Rent_to_Owner_Ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.Rent_to_Owner_Date]
                          ? payment.qb_info[LeaseQbInfo.Rent_to_Owner_Date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.Rent_to_Owner_Ref]
                          ? payment.qb_info[LeaseQbInfo.Rent_to_Owner_Ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.rent_to_owner ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Fees to Owner</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.Other_fee_Date}
                    refName={LeaseQbInfo.Other_fee_Ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.Other_fee_Date]
                          ? payment.qb_info[LeaseQbInfo.Other_fee_Date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.Other_fee_Ref]
                          ? payment.qb_info[LeaseQbInfo.Other_fee_Ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.other_fee ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Agent Comm</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.Agent_Commission_Date}
                    refName={LeaseQbInfo.Agent_Commission_Ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.Agent_Commission_Date]
                          ? payment.qb_info[LeaseQbInfo.Agent_Commission_Date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.Agent_Commission_Ref]
                          ? payment.qb_info[LeaseQbInfo.Agent_Commission_Ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.agent_commission ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">CoBroke Comm</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.co_broke_commission_date}
                    refName={LeaseQbInfo.co_broke_commission_ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.co_broke_commission_date]
                          ? payment.qb_info[
                              LeaseQbInfo.co_broke_commission_date
                            ]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.co_broke_commission_ref]
                          ? payment.qb_info[LeaseQbInfo.co_broke_commission_ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.co_broke_commission ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">C&C Comm</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <DisbursementFormItem
                    qbInfo={payment.qb_info}
                    dateName={LeaseQbInfo.office_commission_date}
                    refName={LeaseQbInfo.office_commission_ref}
                    paymendId={payment.payment_uuid}
                    leaseId={payment.lease}
                  />
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.office_commission_date]
                          ? payment.qb_info[LeaseQbInfo.office_commission_date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.office_commission_ref]
                          ? payment.qb_info[LeaseQbInfo.office_commission_ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.office_commission ?? 0)
              )}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">To Sec. Deposit</td>
            {!showDisbursementForm ? (
              <>
                <td className="py-2 text-left font-normal" />
                <td className="py-2 text-left font-normal" />
              </>
            ) : (
              <>
                {isAdmin ? (
                  <>
                    <DisbursementFormItem
                      qbInfo={payment.qb_info}
                      dateName={LeaseQbInfo.security_deposit_date}
                      refName={LeaseQbInfo.security_deposit_ref}
                      paymendId={payment.payment_uuid}
                      leaseId={payment.lease}
                    />
                  </>
                ) : (
                  <>
                    <td className="py-2 text-left font-normal">
                      <p className="p-2">
                        {payment.qb_info[LeaseQbInfo.security_deposit_date]
                          ? payment.qb_info[LeaseQbInfo.security_deposit_date]
                          : 'N/A'}
                      </p>
                    </td>
                    <td className="py-2 text-left font-normal">
                      <p>
                        {payment.qb_info[LeaseQbInfo.security_deposit_ref]
                          ? payment.qb_info[LeaseQbInfo.security_deposit_ref]
                          : 'N/A'}
                      </p>
                    </td>
                  </>
                )}
              </>
            )}
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(
                Number(payment?.disbursement_form?.security_deposit ?? 0)
              )}
            </td>
          </tr>
        </tbody>
      </table>
      <div className="flex items-center justify-between my-2 text-xs">
        Total Payments{' '}
        <span>
          {currencyFormatter.format(Number(payment.total_amount ?? 0))}
        </span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs">
        Total Received{' '}
        <span>
          {currencyFormatter.format(Number(payment.amount_received ?? 0))}
        </span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs font-semibold">
        Balance{' '}
        <span>{currencyFormatter.format(Number(payment.balance ?? 0))}</span>
      </div>
      <Button
        disabled
        className="rounded-md bg-success hover:bg-success/70 text-xs min-h-[26px] flex justify-self-end min-w-[220px]"
      >
        {payment?.disbursement_form?.submit_at
          ? `Submitted by Agent on ${dayjs(
              payment.disbursement_form.submit_at
            ).format('MM/DD/YYYY')}`
          : 'Submit Payout Request'}
      </Button>
    </>
  );
};

export default LeasePaymentDisbursement;
