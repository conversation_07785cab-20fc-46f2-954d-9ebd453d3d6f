import { DamageClaim } from '@/types/lease';
import {
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import DamagePaymentsForm from '@/clients/views/lease/financial-summary/DamagePaymentsForm';
import { Nullable } from '@/types/common';
import { currencyFormatter } from '@/utils/common';

type Props = {
  damageClaim: Nullable<DamageClaim>;
  leaseId: number;
  isAdmin?: boolean;
  paidAllPayments?: boolean;
  departureDate: string;
};
const LeaseDamagePayments = ({
  damageClaim,
  leaseId,
  isAdmin,
  paidAllPayments,
  departureDate,
}: Props) => {
  return (
    <div className="p-4 bg-white rounded-md">
      <p className="m-0 text-sm font-semibold flex items-center">
        {damageClaim?.status === 'paid' ? (
          <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500 mr-4" />
        ) : (
          <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500 mr-4" />
        )}
        Damage Payments
      </p>
      <p className="text-xs my-1">
        No Security Deposit was collected for this rental. To submit a damage
        claim please enter the amount here and add supporting documents below.
      </p>
      <table className="w-full text-xs overflow-x-auto">
        <thead className="text-black/60">
          <tr className="border-b border-outline">
            <th className="py-4 text-left font-normal w-[20%]">Date</th>
            <th className="py-4 text-center font-normal w-[55%]">Reference</th>
            <th className="py-4 font-normal w-[25%] text-right">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">
              {damageClaim?.payment_date}
            </td>
            <td className="text-center">{damageClaim?.reference}</td>
            <td className="py-2 font-normal text-right">
              {damageClaim?.amount &&
                currencyFormatter.format(Number(damageClaim?.amount))}
            </td>
          </tr>
        </tbody>
      </table>
      <DamagePaymentsForm
        leaseId={leaseId}
        damageClaim={damageClaim}
        isAdmin={isAdmin}
        departureDate={departureDate}
        paidAllPayments={paidAllPayments}
      />
    </div>
  );
};

export default LeaseDamagePayments;
