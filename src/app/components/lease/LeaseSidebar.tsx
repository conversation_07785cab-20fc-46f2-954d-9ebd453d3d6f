import { getLeaseDetails } from '@/app/actions/lease';
import LeaseSidebarNav from '@/clients/views/lease/LeaseSidebarNav';
import LeaseMobileTabSelector from '@/clients/views/lease/LeaseMobileTabSelector';
import { Lease } from '@/types/lease';
import classNames from 'classnames';

type Props = {
  leaseId?: number;
};

const LeaseSidebar = async ({ leaseId }: Props) => {
  let lease;
  if (leaseId) {
    lease = await getLeaseDetails<Lease>(leaseId);
  }

  return (
    <aside className="fixed inset-0 min-h-max md:h-full md:inset-y-0 md:left-0 z-10 w-full md:w-[176px] pt-[64px] md:pt-[72px] bg-white md:bg-[#DEE3E9]">
      <div className="bg-[#2C3E50] py-4 px-2.5 flex flex-col gap-y-2.5 md:max-w-48 md:bg-[#DEE3E9] md:pr-0 md:gap-y-4">
        <div className="flex justify-between items-center flex-row md:flex-col md:gap-y-2 md:bg-[#2C3E50] md:p-4 md:rounded-l-2xl">
          <h1 className="text-lg text-white font-bold">
            {leaseId ? `Lease #${leaseId}` : 'New Lease'}
          </h1>
          {lease && (
            <span
              className={classNames(
                'rounded-md py-2 px-4 md:w-full md:text-center',
                {
                  'bg-[#FBD0DA] text-[#EB1648]': lease?.status === 'Draft',
                  'bg-yellow-500 text-white':
                    lease?.status !== 'Draft' &&
                    lease?.status !== 'Paid in Full',
                  'bg-success text-white': lease?.status === 'Paid in Full',
                }
              )}
            >
              {lease?.status ?? ''}
            </span>
          )}
        </div>
        <div className="md:hidden">
          <LeaseMobileTabSelector leaseId={leaseId} />
        </div>
        <LeaseSidebarNav lease={lease} />
      </div>
    </aside>
  );
};

export default LeaseSidebar;
