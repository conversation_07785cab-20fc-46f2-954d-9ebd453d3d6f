import { Lease } from '@/types/lease';
import { parseDateString } from '@/utils/common';
import dayjs from 'dayjs';
import { format } from 'date-fns';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

type Props = {
  lease?: Lease;
};
const LeaseDetailsSection = ({ lease }: Props) => {
  return (
    <div className="p-4 bg-navy rounded-[10px] grid md:grid-cols-2 xl:grid-cols-3 gap-2 mt-[76px] md:mt-0">
      <div className="rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Address:</p>
        <p className="font-semibold  text-xs w-full">
          {lease?.listing_address ?? ''}
        </p>
      </div>
      <div className="border rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Tenant:</p>
        <p className="font-semibold text-xs w-full">
          {lease?.tenant_first_name + ' ' + lease?.tenant_last_name}
        </p>
      </div>
      <div className="border rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Leasing Agent:</p>
        <p className="font-semibold text-xs w-full">
          {lease?.user_first_name + ' ' + lease?.user_last_name}
        </p>
      </div>
      <div className="border rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Landlord:</p>
        <p className="font-semibold text-xs">
          {lease?.owner_first_name + ' ' + lease?.owner_last_name}
        </p>
      </div>
      <div className="border rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Dates:</p>
        <div className="flex items-center justify-between w-full text-xs font-semibold">
          <p>
            {lease?.arrival_date
              ? format(parseDateString(lease?.arrival_date), 'LLL d, yyyy')
              : 'Start date'}
          </p>
          <ArrowRightIcon className="w-4 h-4" />
          <p>
            {lease?.departure_date
              ? format(parseDateString(lease?.departure_date), 'LLL d, yyyy')
              : 'End date'}
          </p>
        </div>
      </div>
      <div className="border rounded-md px-4 py-2 flex items-center gap-x-2 bg-white h-10">
        <p className="text-xs md:w-[120px] xl:w-[90px]">Lease Created:</p>
        <p className="font-semibold text-xs">
          {lease ? dayjs(lease.created_at).format('MM/DD/YYYY') : 'N/A'}
        </p>
      </div>
    </div>
  );
};

export default LeaseDetailsSection;
