import { ChevronDownIcon } from "@heroicons/react/24/outline";

type Props = {
  title: React.ReactNode;
  children?: React.ReactNode;
};
const DropdownMenuItem = ({ title, children }: Props) => {
  return (
    <div className="dropdown">
      <div
        tabIndex={0}
        role="button"
        className="flex items-center text-[#8BABB6] hover:text-white transition-colors"
      >
        <div className="flex items-center gap-2 p-4">{title}</div>
        <ChevronDownIcon className="w-4 h-4" />
      </div>
      <ul
        tabIndex={0}
        className="dropdown-content menu bg-base-100 rounded-lg z-[1] w-52 p-0 py-2 shadow"
      >
        {children}
      </ul>
    </div>
  );
};

export default DropdownMenuItem;
