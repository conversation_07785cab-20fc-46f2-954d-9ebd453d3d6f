import { getSearchParams } from '@/utils/common';
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

type PaginationProps = {
  prefix: string;
  queryParams: {
    offset: number;
    limit?: number; // Optional limit parameter
    status?: string;
    ordering?: string;
  };
  total: number;
  pageSize?: number; // New prop for page size
};

export const Pagination = ({
  queryParams,
  prefix,
  total,
  pageSize = 10, // Default page size is 10
}: PaginationProps) => {
  // Use provided limit from queryParams if available, otherwise use pageSize
  const currentPageSize = queryParams?.limit || pageSize;

  const pageCount = Math.ceil(total / currentPageSize);
  const remainders = total % currentPageSize;
  const page =
    queryParams?.offset > 0
      ? Math.floor(queryParams?.offset / currentPageSize) + 1
      : 1;

  let left = 0;
  let right = 0;
  if (pageCount >= 7) {
    if (page > 5 && page < pageCount - 4) {
      left = Number(page) - 2;
      right = Number(page) + 2;
    } else if (page <= 5) {
      left = 1;
      right = 7;
    } else {
      right = pageCount;
      left = pageCount - 6;
    }
  }

  const shouldShowLeftEllipsis = () => {
    if (pageCount <= 7) {
      return false;
    }
    return page > 5;
  };

  const shouldShowRightEllipsis = () => {
    if (pageCount <= 7) {
      return false;
    }
    return left !== pageCount && right !== pageCount;
  };

  const ellipsis = (
    <span className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200">
      ...
    </span>
  );

  const ltOneToLast = page - 1 < pageCount;
  const isLast = page === pageCount;
  const selectedClass = 'bg-cyan-900 border-cyan-900 text-white';

  // Helper function to create pagination URLs
  const createPaginationUrl = (
    pageNum: number,
    isLastPage: boolean = false
  ) => {
    return (
      `${prefix}/?` +
      getSearchParams({
        ...queryParams,
        offset: currentPageSize * (pageNum - 1),
        limit: isLastPage && remainders > 0 ? 10 : currentPageSize,
      })
    );
  };

  return (
    <div className="mb-4 flex flex-wrap gap-1.5">
      {pageCount > 1 && (
        <Link
          href={createPaginationUrl(page > 1 ? page - 1 : 1)}
          scroll={false}
          aria-label="move back one page"
          className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200 ${
            page === 1 ? 'opacity-50 pointer-events-none' : ''
          }`}
        >
          <ArrowLeftIcon className="w-4 h-4" />
        </Link>
      )}
      {page > 5 && (
        <Link
          href={createPaginationUrl(1)}
          scroll={false}
          aria-label="move back to the first page"
          className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200"
        >
          1
        </Link>
      )}
      {shouldShowLeftEllipsis() && ellipsis}
      {Array(pageCount)
        .fill(null)
        .map((_, i) => {
          i++; // Adjust index to start from 1
          if (left === 0 && right === 0) {
            // Show all pages when there are few pages
            const className =
              page == i ? selectedClass : 'border-zinc-200 text-navy-blue';
            const isLastPage = i === pageCount;

            return (
              <Link
                key={`page-${i}`}
                href={createPaginationUrl(i, isLastPage)}
                aria-label={`move to page ${i}`}
                scroll={false}
                className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded border ${className}`}
              >
                <span className="text-center font-medium text-inherit">
                  {i}
                </span>
              </Link>
            );
          } else if (i >= left && i < right) {
            // Show pages within the calculated range
            const className =
              page == i ? selectedClass : 'border-zinc-200 text-navy-blue';
            const isLastPage = i === pageCount;

            return (
              <Link
                key={`page-${i}`}
                href={createPaginationUrl(i, isLastPage)}
                aria-label={`move to page ${i}`}
                scroll={false}
                className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded border ${className}`}
              >
                <span className="text-center font-medium text-inherit">
                  {i}
                </span>
              </Link>
            );
          }
          return null;
        })}
      {shouldShowRightEllipsis() && ellipsis}
      {page > 4 && ltOneToLast && (
        <Link
          href={createPaginationUrl(pageCount, true)}
          scroll={false}
          aria-label="move to the last page"
          className={`flex h-9 w-9 items-center justify-center rounded border ${
            isLast ? selectedClass : 'border-zinc-200 '
          }`}
        >
          <span className="text-center font-medium text-inherit">
            {pageCount}
          </span>
        </Link>
      )}
      {pageCount > 1 && (
        <Link
          href={createPaginationUrl(
            page < pageCount ? page + 1 : pageCount,
            page + 1 === pageCount
          )}
          scroll={false}
          aria-label="move to the next page"
          className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200 ${
            page === pageCount ? 'opacity-50 pointer-events-none' : ''
          }`}
        >
          <ArrowRightIcon className="w-4 h-4" />
        </Link>
      )}
    </div>
  );
};
