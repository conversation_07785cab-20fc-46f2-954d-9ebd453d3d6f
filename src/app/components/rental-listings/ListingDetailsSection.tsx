import Link from 'next/link';
import ListingDetailsCard from './ListingDetailsCard';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { getPropertyDetails } from '@/app/actions/property';
import { notFound } from 'next/navigation';
import { Property } from '@/types/property';
import { Suspense } from 'react';

type Props = {
  id: number;
};
const ListingDetailsSection = async ({ id }: Props) => {
  const data = await getPropertyDetails<Property>(id);
  if (!data) {
    notFound();
  }
  return (
    <div className="my-2 lg:my-4 flex flex-col md:flex-row gap-4 mb-2 xl:mb-4">
      <Suspense>
        <ListingDetailsCard property={data} />
      </Suspense>
      <div className="w-full xl:w-[50%] 2xl:w-[60%] shadow-card p-2 xl:py-6 xl:px-4 rounded-[10px]">
        <div className="grid grid-cols-3 gap-2">
          <Link
            href={`/property/${id}/bedroomsandbathrooms`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Bedrooms:
              <span className="font-bold ml-4">
                {data?.bedroom_number ?? 0}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/amenities`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Air Con:
              <span className="font-bold ml-4">
                {data?.ac_types.map((_ac) => _ac.name).join(', ') ?? 'N/A'}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/location`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Walk to beach:
              <span className="font-bold ml-4">
                {data?.walk_to_beach ? `Yes` : `No`}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/bedroomsandbathrooms`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Bathrooms:
              <span className="font-bold ml-4">
                {data?.bathroom_number ?? 0} full,{' '}
                {data?.half_bathroom_number ?? 0} half
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/general`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Pets:
              <span className="font-bold ml-4">
                {data.requirement?.pet_allow_label ?? 'N/A'}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/location`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Waterfront:
              <span className="font-bold ml-4">
                {data?.waterfront ? `Yes` : `No`}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/general`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Capacity:<span className="font-bold ml-4">{data.capacity}</span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/amenities`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Pool:
              <span className="font-bold ml-4">
                {data?.pool_type?.name ?? 'N/A'}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
          <Link
            href={`/property/${id}/location`}
            className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between"
          >
            <p className="text-[10px] md:text-xs flex-grow">
              Waterview:
              <span className="font-bold ml-4">
                {data?.water_view ? `Yes` : `No`}
              </span>
            </p>
            <ChevronRightIcon className="h-4 w-1.25" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ListingDetailsSection;
