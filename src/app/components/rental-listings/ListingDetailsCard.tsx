import EmailHomeownerButton from '@/clients/views/common/EmailHomeownerButton';
import ShareListingButton from '@/clients/views/common/ShareListingButton';
import SwitchPropertyButton from '@/clients/views/common/SwitchPropertyButton';
import { Property } from '@/types/property';
import { getPeakSeasonRate, getPeakSeasonText } from '@/utils/rates';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import Link from 'next/link';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type Props = { property: Property };

const ListingDetailsCard = ({ property }: Props) => {
  return (
    <div className="md:rounded-[10px] grid grid-cols-5 10xl:grid-cols-3 gap-2 xl:gap-4 items-start bg-[#2C3E50] p-2 xl:px-4 xl:py-2 shadow-card w-full xl:w-[50%] 2xl:w-[40%]">
      <div className="text-white flex gap-x-4 w-full h-full col-span-2 row-span-1 lg:row-span-2">
        <Image
          alt="Listing photo"
          src={
            property.images[0]?.small_url ??
            'https://placehold.co/600x400.png?text=Listing+Photo'
          }
          width={0}
          height={0}
          sizes="10vw"
          className="w-full h-[80px] lg:h-[132px] xl:h-[172px] rounded-[10px] object-cover my-auto"
        />
        <div className="md:hidden flex-grow">
          <p className="text-[19px] font-bold flex items-center truncate w-[220px]">
            {property.address}
          </p>
          <Link
            href={`/property/${property.listing_id}/general`}
            className="flex items-center justify-between gap-x-4 my-2 lg:my-1 w-full"
          >
            <p className="text-xs">Turnover Day:</p>
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-bold">
                {property?.requirement?.turnover_day ?? 'N/A'}
              </span>
              <ChevronRightIcon className="h-3 w-auto" />
            </div>
          </Link>
          <div className="flex items-center gap-x-4 my-2 lg:my-1">
            <p className="text-xs">High Season:</p>
            <span className="text-xs">
              {getPeakSeasonText(getPeakSeasonRate(property.rates))}
            </span>
          </div>
          <Link
            href={`https://nantucketrentals.com/${property.nr_listing_id}`}
            target="_blank"
            className="flex items-center gap-x-4 my-2 lg:my-1 text-xs underline"
          >
            NR Listing ID : {property.nr_listing_id}
          </Link>
        </div>
      </div>
      <div className="text-white flex-grow w-full flex flex-row-reverse gap-x-4 md:flex-col col-span-3">
        <div className="flex-center-between">
          <p className="hidden md:flex items-center text-[19px] font-bold">
            {property.address}
          </p>
          <div className="flex items-center flex-col md:flex-row gap-2">
            <SwitchPropertyButton />
            <ShareListingButton propertyId={Number(property.listing_id)} />
          </div>
        </div>

        <div className="my-[5px] hidden md:flex flex-col md:flex-row lg:flex-col items-center lg:items-start gap-x-10">
          <Link
            href={`/property/${property.listing_id}/general`}
            className="flex items-center gap-x-4 my-2 lg:my-1"
          >
            <p className="text-xs">Turnover Day:</p>
            <div className="flex items-center gap-x-2">
              <span className="text-xs font-bold">
                {property?.requirement?.turnover_day ?? 'N/A'}
              </span>
              <ChevronRightIcon className="h-3 w-auto" />
            </div>
          </Link>
          <div className="flex items-center gap-x-4 my-2 lg:my-1">
            <p className="text-xs">High Season:</p>
            <span className="text-xs">
              {getPeakSeasonText(getPeakSeasonRate(property.rates))}
            </span>
          </div>
          <Link
            href={`https://nantucketrentals.com/${property.nr_listing_id}`}
            target="_blank"
            className="flex items-center gap-x-4 my-2 lg:my-1 text-xs underline"
          >
            NR Listing ID : {property.nr_listing_id}
          </Link>
        </div>
      </div>
      <div className="text-white border rounded-md border-white text-xs px-2 xl:px-4 py-1 xl:py-2 flex-grow md:flex-grow-0 col-span-5 lg:col-span-3">
        {property?.homeowner?.contact_id ? (
          <Link
            target="_blank"
            href={`${BASE_URL}/contact/${property?.homeowner?.contact_id}`}
            className="flex items-center justify-between cursor-pointer mb-2"
          >
            <p>Owner:</p>
            <div className="flex items-center gap-x-2">
              <span className="font-bold truncate max-w-[160px]">
                {property?.homeowner?.name ?? ''}
              </span>
              <ChevronRightIcon className="h-[14px] w-auto" />
            </div>
          </Link>
        ) : (
          <div className="flex items-center justify-between mb-2">
            <p>Owner:</p>
            <p>N/A</p>
          </div>
        )}
        {property?.homeowner?.cell_phone ? (
          <a
            className="flex items-center justify-between cursor-pointer mb-2"
            href={`tel:${property?.homeowner?.cell_phone}`}
          >
            <p>Phone:</p>
            <div className="flex items-center gap-x-2">
              <span className="font-bold truncate max-w-[160px]">
                {property?.homeowner?.cell_phone}
              </span>
              <ChevronRightIcon className="h-[14px] w-auto" />
            </div>
          </a>
        ) : (
          <div className="flex items-center justify-between mb-2">
            <p>Phone:</p>
            <p>N/A</p>
          </div>
        )}
        {property?.homeowner?.email1 ? (
          <EmailHomeownerButton property={property}>
            <p>Email:</p>
            <div className="flex items-center gap-x-2">
              <span className="font-bold truncate max-w-[160px]">
                {property?.homeowner?.email1}
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="14"
                viewBox="0 0 15 14"
                fill="none"
              >
                <path
                  d="M7.14286 6.6579L1.42857 3.23684V10.0789H7.85714V11.4474H1.42857C1.03571 11.4474 0.699405 11.3134 0.419643 11.0454C0.139881 10.7774 0 10.4553 0 10.0789V1.86842C0 1.49211 0.139881 1.16996 0.419643 0.901974C0.699405 0.633991 1.03571 0.5 1.42857 0.5H12.8571C13.25 0.5 13.5863 0.633991 13.8661 0.901974C14.1458 1.16996 14.2857 1.49211 14.2857 1.86842V6.6579H12.8571V3.23684L7.14286 6.6579ZM7.14286 5.28947L12.8571 1.86842H1.42857L7.14286 5.28947ZM12.1429 13.5L11.1429 12.5421L12.2679 11.4474H9.28572V10.0789H12.2679L11.125 8.98421L12.1429 8.02632L15 10.7632L12.1429 13.5ZM1.42857 3.23684V10.7632V6.6579V6.70921V1.86842V3.23684Z"
                  fill="white"
                />
              </svg>
            </div>
          </EmailHomeownerButton>
        ) : (
          <div className="flex items-center justify-between mb-2">
            <p>Email:</p>
            <p>N/A</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ListingDetailsCard;
