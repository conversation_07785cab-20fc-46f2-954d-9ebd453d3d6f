import MobileTabSelector from '@/clients/views/common/MobileTabSelector';
import SidebarNavItem from '@/clients/views/rental-listings/SidebarNavItem';

type Props = {
  propertyId: number;
};

const Sidebar = ({ propertyId }: Props) => {
  return (
    <aside className="fixed inset-0 h-max lg:h-full lg:inset-y-0 lg:left-0 z-10 w-full lg:w-[176px] pt-[64px] lg:pt-[72px] bg-white lg:bg-[#DEE3E9]">
      <div className="block lg:hidden p-2">
        <MobileTabSelector propertyId={propertyId} />
      </div>
      <div className="pl-2 py-4 hidden lg:flex flex-col gap-3">
        <SidebarNavItem
          href={`/property/${propertyId}/overview`}
          title="Overview"
          pagePath="overview"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/location`}
          title="Location"
          pagePath="location"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/descriptions`}
          title="Descriptions"
          pagePath="descriptions"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/general`}
          title="General Info"
          pagePath="general"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/amenities`}
          title="Amenities"
          pagePath="amenities"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/bedroomsandbathrooms`}
          title="Rooms"
          pagePath="bedroomsandbathrooms"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/calendar`}
          title="Calendar"
          pagePath="calendar"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/photo`}
          title="Photos"
          pagePath="photo"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/service`}
          title="Service Providers"
          pagePath="service"
        />
        <SidebarNavItem
          href={`/property/${propertyId}/payment-information`}
          title="Payment Info"
          pagePath="payment-information"
        />
      </div>
    </aside>
  );
};

export default Sidebar;
