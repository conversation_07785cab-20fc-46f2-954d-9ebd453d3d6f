import { getPropertySeasonalAvailability } from "@/app/actions/property";
import { AvailabilityType } from "@/types/calendar";
import { PropertySeasonalPolicy } from "@/types/property";
import { currencyFormatter } from "@/utils/common";
import { getAvailabilityText } from "@/utils/listings";
import classNames from "classnames";
import dayjs from "dayjs";

type Props = {
  propertyId: number;
};

const BookingsTable = async ({ propertyId }: Props) => {
  const data = await getPropertySeasonalAvailability<PropertySeasonalPolicy[]>(
    propertyId,
    dayjs().startOf("year").format("YYYY-MM-DD")
  );
  return (
    <>
      <div className="overflow-x-auto">
        <table className="table text-[10px] md:text-xs text-center">
          <thead className="bg-white">
            <tr>
              <th className="text-black-60 font-normal w-[120px]">Week</th>
              <th className="text-black-60 font-normal w-[120px]">Status</th>
              <th className="text-black-60 font-normal w-[120px]">
                Avg. Weekly Rate
              </th>
            </tr>
          </thead>
          <tbody>
            {(data ?? []).map((_data, index) => (
              <tr key={index}>
                <td>
                  {dayjs(_data.start_date, "YYYY-MM-DD").format("MM/DD")}
                  <span className="px-1">-</span>
                  {dayjs(_data.end_date, "YYYY-MM-DD").format("MM/DD/YYYY")}
                </td>
                <td
                  className={classNames("font-bold", {
                    "text-success": _data.status === AvailabilityType.AVAILABLE,
                    "text-pending-dark":
                      _data.status === AvailabilityType.PENDING,
                  })}
                >
                  {getAvailabilityText(_data.status)}
                </td>
                <td>{currencyFormatter.format(_data.rate)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default BookingsTable;
