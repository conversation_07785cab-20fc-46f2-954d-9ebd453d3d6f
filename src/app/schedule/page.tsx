'use client';

import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import dayjs from 'dayjs';
import SetShiftTimeDialog from './components/SetShiftTimeDialog';
import SelectPMRotationDialog from './components/SelectPMRotationDialog';
import DeleteConfirmationDialog from './components/DeleteConfirmationDialog';
import ScheduleCalendar from './components/ScheduleCalendar';
import MobileScheduleView from './components/MobileScheduleView';

interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  user_name: string;
}

interface ShiftTime {
  shift_time_id: number;
  name: string;
  start_time: string | null;
  end_time: string | null;
}

interface ScheduleEvent {
  date: string;
  shift_time: ShiftTime;
  users: User[];
}

interface CalendarEvent {
  start: string;
  allDay: boolean;
  title: string;
  data: ScheduleEvent;
}

export default function SchedulePage() {
  const [currentMonth, setCurrentMonth] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [shiftTimes, setShiftTimes] = useState<ShiftTime[]>([]);
  const [schedules, setSchedules] = useState<ScheduleEvent[]>([]);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [mobileData, setMobileData] = useState<any[]>([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [deleteData, setDeleteData] = useState<any>({});
  const [showSetShiftTime, setShowSetShiftTime] = useState(false);
  const [showPMRotation, setShowPMRotation] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const defaultDate = dayjs().add(currentMonth, 'month').format('YYYY-MM');

  // Mock API functions - replace with actual API calls
  const apiGetUsers = async () => {
    // Mock data - replace with actual API call
    return {
      results: [
        { user_id: 1, first_name: 'John', last_name: 'Doe', user_name: 'John Doe' },
        { user_id: 2, first_name: 'Jane', last_name: 'Smith', user_name: 'Jane Smith' },
        { user_id: 3, first_name: 'Bob', last_name: 'Johnson', user_name: 'Bob Johnson' },
      ]
    };
  };

  const apiGetShiftTime = async () => {
    // Mock data - replace with actual API call
    return {
      results: [
        { shift_time_id: 1, name: 'Morning', start_time: '08:00:00', end_time: '16:00:00' },
        { shift_time_id: 2, name: 'Afternoon', start_time: '16:00:00', end_time: '00:00:00' },
        { shift_time_id: 3, name: 'Night', start_time: '00:00:00', end_time: '08:00:00' },
      ]
    };
  };

  const apiGetSchedule = async (params: { year: string; month: string; limit: number }) => {
    // Mock data - replace with actual API call
    return {
      results: [
        {
          date: '2024-01-15',
          shift_time: { shift_time_id: 1, name: 'Morning', start_time: '08:00:00', end_time: '16:00:00' },
          users: [{ user_id: 1, first_name: 'John', last_name: 'Doe', user_name: 'John Doe' }]
        },
        {
          date: '2024-01-15',
          shift_time: { shift_time_id: 2, name: 'Afternoon', start_time: '16:00:00', end_time: '00:00:00' },
          users: [{ user_id: 2, first_name: 'Jane', last_name: 'Smith', user_name: 'Jane Smith' }]
        }
      ]
    };
  };

  const apiCreateSchedule = async (data: { schedules: any[] }) => {
    // Mock API call - replace with actual implementation
    console.log('Creating schedule:', data);
    return { success: true };
  };

  const getSchedule = async () => {
    try {
      const response = await apiGetShiftTime();
      setShiftTimes(response.results);
    } catch (error) {
      console.error('Error fetching shift times:', error);
    }
  };

  const getSchedulePerson = async () => {
    try {
      const response = await apiGetSchedule({
        year: dayjs(defaultDate).format('YYYY'),
        month: dayjs(defaultDate).format('MM'),
        limit: 93
      });
      changeData(response.results);
    } catch (error) {
      console.error('Error fetching schedules:', error);
    }
  };

  const changeData = (results: ScheduleEvent[]) => {
    const newCalendarEvents: CalendarEvent[] = [];
    const newMobileData: any[] = [];
    const objData: any = {};

    results.forEach((item) => {
      let name = '';
      if (item.users.length > 0) {
        item.users.forEach((user, i) => {
          if (i + 1 === item.users.length) {
            name += user.user_name;
          } else {
            name += user.user_name + ', ';
          }
        });
      }

      // Calendar events
      const startTime = item.shift_time.start_time || '';
      const endTime = item.shift_time.end_time || '';
      const time = startTime && endTime ? `${startTime} - ${endTime}` : '';
      
      if (name || time) {
        const calendarEvent: CalendarEvent = {
          start: `${item.date}T${startTime}`,
          allDay: false,
          title: `${name}\n${time}`,
          data: item
        };
        newCalendarEvents.push(calendarEvent);
      }

      // Mobile data
      const mobileEvent = { start: item.date, allDay: true, className: 'blue' };
      newMobileData.push(mobileEvent);

      // Group by date for mobile view
      const key = item.date;
      if (objData[key]) {
        objData[key].push({ shift_time: item.shift_time, users: item.users, date: item.date });
      } else {
        objData[key] = [{ shift_time: item.shift_time, users: item.users, date: item.date }];
      }
    });

    // Remove duplicate mobile events
    const uniqueMobileData = newMobileData.reduce((cur: any[], next: any) => {
      const exists = cur.find(item => item.start === next.start);
      if (!exists) {
        cur.push(next);
      }
      return cur;
    }, []);

    // Format mobile data
    const formattedMobileData = Object.keys(objData).map(date => ({
      date,
      arr: objData[date]
    }));

    setCalendarEvents(newCalendarEvents);
    setMobileData(formattedMobileData);
  };

  const handleDayClick = (date: string) => {
    setSelectedDate(dayjs(date).format('YYYY-MM-DD'));
    setShowPMRotation(true);
  };

  const handleEventClick = (event: any) => {
    if (event.data) {
      setDeleteData({
        users: [],
        date: event.data.date,
        shift_time: event.data.shift_time.shift_time_id
      });
      setShowDeleteDialog(true);
    }
  };

  const handleDelete = async () => {
    try {
      await apiCreateSchedule({ schedules: [deleteData] });
      await getSchedulePerson();
      setShowDeleteDialog(false);
      // Show success message
    } catch (error) {
      console.error('Error deleting schedule:', error);
      // Show error message
    }
  };

  const handleSmallDelete = (item: any) => {
    setDeleteData({
      users: [],
      date: item.date,
      shift_time: item.shift_time.shift_time_id
    });
    setShowDeleteDialog(true);
  };

  useEffect(() => {
    getSchedule();
    getSchedulePerson();
    
    const fetchUsers = async () => {
      try {
        const response = await apiGetUsers();
        setUsers(response.results);
      } catch (error) {
        console.error('Error fetching users:', error);
      }
    };
    
    fetchUsers();
  }, []);

  useEffect(() => {
    getSchedulePerson();
  }, [defaultDate]);

  return (
    <div className="schedule p-4">
      <div className="schedule-top flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold text-gray-800">Agent Schedule</h1>
        <button 
          className="primary-button px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => setShowSetShiftTime(true)}
        >
          Set Shift Time
        </button>
      </div>

      {/* Desktop Calendar View */}
      <div className="calendar-box hidden lg:block">
        <div className="relative">
          <div className="flex justify-between items-center mb-4">
            <button 
              className="prev-month absolute top-0 left-1/2 transform -translate-x-32 text-gray-600 hover:text-gray-800"
              onClick={() => setCurrentMonth(prev => prev - 1)}
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-semibold">
              {dayjs(defaultDate).format('MMMM YYYY')}
            </h2>
            <button 
              className="next-month absolute top-0 left-1/2 transform translate-x-32 text-gray-600 hover:text-gray-800"
              onClick={() => setCurrentMonth(prev => prev + 1)}
            >
              <ChevronRightIcon className="w-5 h-5" />
            </button>
          </div>
          
          <ScheduleCalendar 
            events={calendarEvents}
            defaultDate={defaultDate}
            onDayClick={handleDayClick}
            onEventClick={handleEventClick}
          />
        </div>
      </div>

      {/* Mobile Calendar View */}
      <div className="calendar-box lg:hidden">
        <div className="flex justify-between items-center mb-4">
          <button 
            className="prev-month text-gray-600 hover:text-gray-800"
            onClick={() => setCurrentMonth(prev => prev - 1)}
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <h2 className="text-xl font-semibold">
            {dayjs(defaultDate).format('MMMM YYYY')}
          </h2>
          <button 
            className="next-month text-gray-600 hover:text-gray-800"
            onClick={() => setCurrentMonth(prev => prev + 1)}
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>
        
        <MobileScheduleView 
          data={mobileData}
          onDelete={handleSmallDelete}
        />
      </div>

      {/* Dialogs */}
      <SetShiftTimeDialog
        show={showSetShiftTime}
        onClose={() => setShowSetShiftTime(false)}
        users={users}
        shiftTimes={shiftTimes}
        onSettingShiftTime={() => {
          getSchedule();
          setShowSetShiftTime(false);
        }}
      />

      <SelectPMRotationDialog
        show={showPMRotation}
        onClose={() => setShowPMRotation(false)}
        users={users}
        shiftTimes={shiftTimes}
        date={selectedDate}
        onSubmit={async (data) => {
          try {
            await apiCreateSchedule({ schedules: [data] });
            await getSchedulePerson();
            setShowPMRotation(false);
            // Show success message
          } catch (error) {
            console.error('Error creating schedule:', error);
            // Show error message
          }
        }}
      />

      <DeleteConfirmationDialog
        show={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        title="Are you sure to delete this shift?"
      />
    </div>
  );
}


