# Schedule Page - Next.js Implementation

This is a Next.js recreation of the Vue.js schedule page from the NVProject. The page allows property managers to set shift schedules, manage shift times, and view schedules in both desktop and mobile views.

## Features

- **Desktop Calendar View**: Full calendar interface for managing schedules
- **Mobile Schedule View**: Collapsible list view for mobile devices
- **Shift Time Management**: Set start and end times for different shifts
- **PM Assignment**: Assign property managers to specific dates and shifts
- **Schedule Deletion**: Remove scheduled shifts with confirmation
- **Responsive Design**: Works on both desktop and mobile devices

## Components

### Main Components

1. **`page.tsx`** - Main schedule page component
2. **`SetShiftTimeDialog.tsx`** - Dialog for setting shift times
3. **`SelectPMRotationDialog.tsx`** - Dialog for assigning PMs to shifts
4. **`DeleteConfirmationDialog.tsx`** - Confirmation dialog for deletions
5. **`ScheduleCalendar.tsx`** - Calendar component (placeholder)
6. **`MobileScheduleView.tsx`** - Mobile-friendly schedule view

## Setup Instructions

### 1. FullCalendar Installation ✅

FullCalendar has been successfully installed and is now active in the schedule page!

**Installed packages:**
- `@fullcalendar/react` - React wrapper
- `@fullcalendar/daygrid` - Month/day grid view  
- `@fullcalendar/interaction` - Click/drag interactions
- `@fullcalendar/timegrid` - Time-based views (optional)

The `ScheduleCalendar.tsx` component is now using the full FullCalendar implementation with professional calendar features.

```tsx
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';

export default function ScheduleCalendar({ events, defaultDate, onDayClick, onEventClick }: Props) {
  return (
    <FullCalendar
      plugins={[dayGridPlugin, interactionPlugin]}
      initialView="dayGridMonth"
      initialDate={defaultDate}
      events={events}
      dateClick={({ dateStr }) => onDayClick(dateStr)}
      eventClick={({ event }) => onEventClick(event)}
      headerToolbar={false}
      height="auto"
      eventDisplay="block"
      eventClassNames="bg-blue-100 text-blue-800 border-l-4 border-blue-600 p-1 rounded text-xs"
    />
  );
}
```

### 2. API Integration

Replace the mock API functions in `page.tsx` with actual API calls:

```tsx
// Replace these mock functions with actual API calls
const apiGetUsers = async () => {
  const response = await fetch('/api/users?user_type=pm&limit=50');
  return response.json();
};

const apiGetShiftTime = async () => {
  const response = await fetch('/api/shift-time');
  return response.json();
};

const apiGetSchedule = async (params: { year: string; month: string; limit: number }) => {
  const response = await fetch(`/api/schedule?year=${params.year}&month=${params.month}&limit=${params.limit}`);
  return response.json();
};

const apiCreateSchedule = async (data: { schedules: any[] }) => {
  const response = await fetch('/api/schedule', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  return response.json();
};
```

### 3. Styling

The components use Tailwind CSS classes. You can customize the styling by modifying the className props or adding custom CSS.

## API Endpoints

The schedule system expects the following API endpoints:

- `GET /api/users` - Get property managers
- `GET /api/shift-time` - Get shift time configurations
- `GET /api/schedule` - Get schedules for a month
- `POST /api/schedule` - Create/update schedules
- `POST /api/shift-time` - Update shift time configurations

## Data Structure

### User
```typescript
interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  user_name: string;
}
```

### ShiftTime
```typescript
interface ShiftTime {
  shift_time_id: number;
  name: string;
  start_time: string | null;
  end_time: string | null;
}
```

### ScheduleEvent
```typescript
interface ScheduleEvent {
  date: string;
  shift_time: ShiftTime;
  users: User[];
}
```

## Usage

1. Navigate to `/schedule` to view the schedule page
2. Click "Set Shift Time" to configure shift time periods
3. Click on any date in the calendar to assign a PM to that date
4. Click on events to delete them
5. Use the mobile view on smaller screens

## Notes

- The current implementation uses mock data for demonstration
- The calendar component is a placeholder - install FullCalendar for full functionality
- All dialogs are implemented as modal overlays
- The mobile view uses collapsible sections for better UX
- Error handling and loading states should be added for production use
