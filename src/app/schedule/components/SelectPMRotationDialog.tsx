'use client';

import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  user_name: string;
}

interface ShiftTime {
  shift_time_id: number;
  name: string;
  start_time: string | null;
  end_time: string | null;
}

interface Props {
  show: boolean;
  onClose: () => void;
  users: User[];
  shiftTimes: ShiftTime[];
  date: string;
  onSubmit: (data: any) => void;
}

export default function SelectPMRotationDialog({ show, onClose, users, shiftTimes, date, onSubmit }: Props) {
  const [selectedShiftTime, setSelectedShiftTime] = useState<number | ''>('');
  const [selectedUser, setSelectedUser] = useState<number | ''>('');

  const handleSubmit = () => {
    if (selectedShiftTime && selectedUser) {
      const data = {
        users: [selectedUser],
        shift_time: selectedShiftTime,
        date: date
      };
      onSubmit(data);
      setSelectedShiftTime('');
      setSelectedUser('');
    }
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Select the PM Rotation</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shift Time
            </label>
            <select
              value={selectedShiftTime}
              onChange={(e) => setSelectedShiftTime(Number(e.target.value) || '')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select shift time</option>
              {shiftTimes.map((shiftTime) => (
                <option key={shiftTime.shift_time_id} value={shiftTime.shift_time_id}>
                  {shiftTime.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              PM
            </label>
            <select
              value={selectedUser}
              onChange={(e) => setSelectedUser(Number(e.target.value) || '')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select PM</option>
              {users.map((user) => (
                <option key={user.user_id} value={user.user_id}>
                  {user.first_name} {user.last_name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!selectedShiftTime || !selectedUser}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


