'use client';

import React from 'react';

interface MobileScheduleViewProps {
  data: any[];
  onDelete: (item: any) => void;
}

const MobileScheduleView: React.FC<MobileScheduleViewProps> = ({ data, onDelete }) => {
  return (
    <div className="md:hidden">
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold text-gray-900">{item.title || 'Schedule Item'}</h3>
            <p className="text-sm text-gray-600">{item.date || 'No date'}</p>
            <button
              onClick={() => onDelete(item)}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MobileScheduleView;
