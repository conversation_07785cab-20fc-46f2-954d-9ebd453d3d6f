'use client';

import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';

interface CalendarEvent {
  start: string;
  allDay: boolean;
  title: string;
  data: any;
}

interface Props {
  events: CalendarEvent[];
  defaultDate: string;
  onDayClick: (date: string) => void;
  onEventClick: (event: any) => void;
}

export default function ScheduleCalendar({ events, defaultDate, onDayClick, onEventClick }: Props) {
  return (
    <FullCalendar
      plugins={[dayGridPlugin, interactionPlugin]}
      initialView="dayGridMonth"
      initialDate={defaultDate}
      events={events}
      dateClick={({ dateStr }) => onDayClick(dateStr)}
      eventClick={({ event }) => onEventClick(event)}
      headerToolbar={false}
      height="auto"
      eventDisplay="block"
      eventClassNames="bg-blue-100 text-blue-800 border-l-4 border-blue-600 p-1 rounded text-xs"
      dayCellClassNames="hover:bg-gray-50 cursor-pointer"
      eventContent={(arg) => (
        <div className="text-xs p-1">
          <div className="font-medium">{arg.event.title.split('\n')[0]}</div>
          {arg.event.title.includes('\n') && (
            <div className="text-blue-600">{arg.event.title.split('\n')[1]}</div>
          )}
        </div>
      )}
    />
  );
}
