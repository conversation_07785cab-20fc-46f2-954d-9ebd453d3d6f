'use client';

import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  user_name: string;
}

interface ShiftTime {
  shift_time_id: number;
  name: string;
  start_time: string | null;
  end_time: string | null;
}

interface Props {
  show: boolean;
  onClose: () => void;
  users: User[];
  shiftTimes: ShiftTime[];
  onSettingShiftTime: () => void;
}

export default function SetShiftTimeDialog({ show, onClose, users, shiftTimes, onSettingShiftTime }: Props) {
  const [localShiftTimes, setLocalShiftTimes] = useState<ShiftTime[]>(shiftTimes);

  const handleTimeChange = (index: number, field: 'start_time' | 'end_time', value: string) => {
    const updated = [...localShiftTimes];
    updated[index] = { ...updated[index], [field]: value };
    setLocalShiftTimes(updated);
  };

  const handleSubmit = async () => {
    try {
      // Mock API call - replace with actual implementation
      console.log('Updating shift times:', localShiftTimes);
      // await apiCreateShiftTime({ shift_times: localShiftTimes });
      onSettingShiftTime();
      onClose();
    } catch (error) {
      console.error('Error updating shift times:', error);
    }
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Set Shift Times</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {localShiftTimes.map((shiftTime, index) => (
              <div key={shiftTime.shift_time_id} className="flex items-end space-x-4">
                <div className="w-20">
                  <span className="text-sm font-medium text-gray-700">{shiftTime.name}</span>
                </div>
                
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Shift Start
                  </label>
                  <input
                    type="time"
                    value={shiftTime.start_time || ''}
                    onChange={(e) => handleTimeChange(index, 'start_time', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Shift End
                  </label>
                  <input
                    type="time"
                    value={shiftTime.end_time || ''}
                    onChange={(e) => handleTimeChange(index, 'end_time', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


