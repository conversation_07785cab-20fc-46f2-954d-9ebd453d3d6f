'use server';
import { Listing, Property } from '@/types/property';
import { getAllListings } from '../actions/property';
import { getSearchParams } from '@/utils/common';
import Image from 'next/image';
import PropertiesFilter from '@/clients/views/properties/PropertiesFilter';
import PropertiesTable from '@/clients/views/properties/PropertiesTable';
import Button from '@/clients/ui/button';

export default async function Properties({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) {
  const params = getSearchParams({ ...searchParams, show_rates: 'true' });

  const data = await getAllListings<{
    results: Listing[];
    count: number;
  }>(params);

  console.log('data', params);

  return (
    <main className="container py-5">
      <div className="flex items-center space-x-3 mb-8">
        <Image
          alt="listings icon"
          src="/images/icons/rental-list.svg"
          width={30}
          height={30}
        />
        <p className="text-xl font-medium text-gray-900">Rental Listings</p>
      </div>
      <PropertiesFilter />

      <PropertiesTable data={data?.results ?? []} total={data?.count ?? 0} />
    </main>
  );
}
