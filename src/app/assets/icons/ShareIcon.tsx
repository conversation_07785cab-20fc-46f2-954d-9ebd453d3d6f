import { SVGProps } from 'react';

const ShareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 15' fill='none' {...props}>
    <path
      fill='#000'
      d='M7.5 8.125 2.5 5v6.25h5.625v1.25H2.5c-.344 0-.638-.122-.883-.367a1.204 1.204 0 0 1-.367-.883v-7.5c0-.344.122-.638.367-.883s.54-.367.883-.367h10c.344 0 .638.122.883.367s.367.54.367.883v4.375H12.5V5l-5 3.125Zm0-1.25 5-3.125h-10l5 3.125Zm4.375 7.5L11 13.5l.984-1H9.375v-1.25h2.61l-1-1 .89-.875 2.5 2.5-2.5 2.5ZM2.5 5v6.875-3.75.047V3.75 5Z'
    />
  </svg>
);
export default ShareIcon;
