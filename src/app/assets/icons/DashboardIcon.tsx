import { SVGProps } from 'react';

const DashboardIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns='http://www.w3.org/2000/svg' width={33} height={32} fill='none' {...props}>
    <path
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth={2.5}
      d='M5.3 6.2a1.4 1.4 0 0 1 1.4-1.4h19.6a1.4 1.4 0 0 1 1.4 1.4V9a1.4 1.4 0 0 1-1.4 1.4H6.7A1.4 1.4 0 0 1 5.3 9V6.2ZM5.3 17.4A1.4 1.4 0 0 1 6.7 16h8.4a1.4 1.4 0 0 1 1.4 1.4v8.4a1.4 1.4 0 0 1-1.4 1.4H6.7a1.4 1.4 0 0 1-1.4-1.4v-8.4ZM22.1 17.4a1.4 1.4 0 0 1 1.4-1.4h2.8a1.4 1.4 0 0 1 1.4 1.4v8.4a1.4 1.4 0 0 1-1.4 1.4h-2.8a1.4 1.4 0 0 1-1.4-1.4v-8.4Z'
    />
  </svg>
);
export default DashboardIcon;
