import { LeaseContextContainer } from '@/clients/contexts/LeaseContext';
import FormikWrapper from '@/clients/views/lease/lease-details/FormikWrapper';

import { Suspense } from 'react';
import LeaseSidebar from '../components/lease/LeaseSidebar';
import { UserProfile } from '@/types/profile';
import { getUserProfile } from '../actions/profile';
import { redirect } from 'next/navigation';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export default async function CreateLeasePage() {
  const data = await getUserProfile<UserProfile>();

  if (!data.user_id) {
    redirect(BASE_URL);
  }
  return (
    <LeaseContextContainer>
      <LeaseSidebar />
      <div className="p-0 md:p-4 md:pl-[192px] pt-[52px] md:pt-5 pb-4 mb-4">
        <main className="min-h-screen">
          <Suspense fallback={<div className="p-8 py-[60px]">Loading...</div>}>
            <FormikWrapper userData={data} />
          </Suspense>
        </main>
      </div>
    </LeaseContextContainer>
  );
}
