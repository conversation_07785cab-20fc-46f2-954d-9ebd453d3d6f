import { getLeaseComments, getLeaseDetails } from '@/app/actions/lease';
import { notFound } from 'next/navigation';
import {
  Lease,
  LeaseComment,
  NOT_AVAILABLE_RETURNING_TENANT_INFO,
} from '@/types/lease';
import LeaseSidebar from '@/app/components/lease/LeaseSidebar';
import LeaseSecurityDeposit from '@/app/components/lease/financial-summary/LeaseSecurityDeposit';
import LeasePaymentItem from '@/app/components/lease/financial-summary/LeasePaymentItem';
import LeaseFinancialSummaryNotes from '@/clients/views/lease/financial-summary/LeaseFinancialSummaryNotes';
import LeaseAttachedFiles from '@/app/components/lease/financial-summary/LeaseAttachedFiles';
import LeaseDetailsSection from '@/app/components/lease/LeaseDetailsSection';
import LeaseDamagePayments from '@/app/components/lease/financial-summary/LeaseDamagePayments';
import { cookies } from 'next/headers';
import CancelLeaseButton from '@/clients/views/lease/cancel-lease/CancelLeaseButton';
import UnblockDatesButton from '@/clients/views/lease/unblock-dates/UnblockDatesButton';

type PageProps = {
  params: { id: string | string[] };
};

const PaymentManagement = async ({ params: { id } }: PageProps) => {
  const leaseId = Array.isArray(id) ? Number(id[0]) : Number(id);
  const lease = await getLeaseDetails<Lease>(leaseId);
  const commentPayload = await getLeaseComments<{
    results: LeaseComment[];
  }>(leaseId, lease?.damage_claim ? 'damage-claim' : 'security-deposit');

  if (!lease) {
    notFound();
  }

  const isLegacyBookingRule =
    lease?.returning_tenant_info !== NOT_AVAILABLE_RETURNING_TENANT_INFO;

  const showTwoCols = lease?.payments?.length === 1;

  const cookieStore = cookies();
  const isAdmin = cookieStore.get('isType')?.value === 'true';

  return (
    <>
      <LeaseSidebar leaseId={leaseId} />

      <div className="p-0 md:p-4 md:pl-[192px] pt-[52px] md:pt-4 pb-4 mb-4">
        <LeaseDetailsSection lease={lease} />
        {lease && (
          <div className="flex items-center justify-end my-4 gap-x-4">
            {isAdmin && <UnblockDatesButton lease={lease} />}
            {lease.status !== 'Cancelled' &&
              lease.status !== 'Paid in Full' && (
                <CancelLeaseButton lease={lease} />
              )}
          </div>
        )}
        <div
          className={`grid grid-cols-1 md:grid-cols-2 mt-2 ${
            !showTwoCols ? 'xl:grid-cols-3' : '2xl:grid-cols-3'
          } gap-4`}
        >
          {lease.payments.map((_p, index) => (
            <LeasePaymentItem
              key={_p.payment_uuid}
              payment={_p}
              isLast={index === lease.payments.length - 1}
              isAdmin={isAdmin}
            />
          ))}
          <div className="p-2 rounded-md bg-[#F7A2B6] h-min">
            {isLegacyBookingRule ? (
              <>
                <LeaseSecurityDeposit
                  leaseId={leaseId}
                  securityDeposit={lease.security_deposit}
                  lastPayment={lease.payments[lease.payments.length - 1]}
                />
              </>
            ) : (
              <>
                <LeaseDamagePayments
                  departureDate={lease.departure_date}
                  leaseId={leaseId}
                  damageClaim={lease.damage_claim}
                  isAdmin={isAdmin}
                  paidAllPayments={lease?.payments.every(
                    (payment) => payment.status === 'Paid'
                  )}
                />
              </>
            )}
            <LeaseFinancialSummaryNotes
              comments={commentPayload?.results ?? []}
              leaseId={leaseId}
              disabled={
                !isLegacyBookingRule
                  ? !lease?.damage_claim?.created_at
                  : !lease?.security_deposit?.created_at
              }
              isDamageClaim={!isLegacyBookingRule}
            />
            <LeaseAttachedFiles
              leaseId={leaseId}
              securityDeposit={lease.security_deposit}
              disabled={
                !isLegacyBookingRule
                  ? !lease?.damage_claim?.created_at
                  : !lease?.security_deposit?.created_at
              }
              damageClaim={lease.damage_claim}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PaymentManagement;
