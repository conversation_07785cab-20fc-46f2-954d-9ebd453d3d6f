import classNames from 'classnames';
import { getLeaseDetails } from '../actions/lease';
import { Lease } from '@/types/lease';
import { FinancialFormValues } from '@/clients/views/lease/lease-details/FinancialForm';

export const calculateGrandTotal = (values: any): number => {
  const rent = parseFloat(values?.bill?.rent as string) || 0;
  const processingFee = parseFloat(values?.bill?.processing_fee as string) || 0;

  let otherFees = 0;
  if (Array.isArray(values.bill?.other_fee)) {
    otherFees = values.bill?.other_fee.reduce(
      (sum: number, fee: any) => sum + (parseFloat(fee.amount as string) || 0),
      0
    );
  } else {
    otherFees = parseFloat(values.bill?.other_fee as string) || 0;
  }

  const occupancyTax = parseFloat(values.occupancyTax as string) || 0;

  const commission =
    'commission' in values ? parseFloat(values.commission as string) || 0 : 0;
  const commissionAmount = commission ? (rent * commission) / 100 : 0;

  return rent + commissionAmount + processingFee + otherFees + occupancyTax;
};

export const getFormFieldValidatedClassNames = (
  error: any,
  width: string = 'w-32'
) =>
  classNames(width, {
    'border-[#F37391]': !!error,
  });

export const ensureArray = (candidateArr?: any[]) => {
  if (!candidateArr || !Array.isArray(candidateArr)) {
    return [];
  }

  return candidateArr;
};

const useLeaseDetails = async (leaseId: number) => {
  const lease = await getLeaseDetails<Lease>(leaseId);

  if (lease) {
    return lease;
  }

  return {
    listing_address: '',
    owner_full_name: '',
    arrival_departure: '',
    tenant_full_name: '',
  };
};

export const calculateGrandTotalFinancialInfo = (
  values: FinancialFormValues
): number => {
  const rent = Number(values?.rent ?? 0);
  const processingFee = Number(values?.processingFee ?? 0);

  let otherFees = 0;
  if (Array.isArray(values.otherFees)) {
    otherFees = values.otherFees.reduce(
      (sum: number, fee: any) => sum + Number(fee.amount ?? 0),
      0
    );
  } else {
    otherFees = Number(values.otherFees ?? 0);
  }

  const occupancyTax = values?.occupancyTax?.exempt
    ? 0
    : Number(values.occupancyTax?.amount ?? 0);

  const commission = Number(values.commission ?? 0);
  const commissionAmount = commission ? (rent * commission) / 100 : 0;

  return rent + commissionAmount + processingFee + otherFees + occupancyTax;
};
