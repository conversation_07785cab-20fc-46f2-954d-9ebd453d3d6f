"use server";

import { cookies } from "next/headers";

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export const getAcTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-ac-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-ac-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to AC types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to AC types");
  }
};

export const getHeatingTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-heating-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-heating-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to Heating types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to Heating types");
  }
};

export const getCoffeeMakerTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-coffee-maker-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-coffee-maker-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to Coffee maker types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to Coffee maker types");
  }
};

export const getStoveTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-stove-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-stove-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to Stove types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to Stove types");
  }
};

export const getToasterTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-toaster-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-toaster-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to get Toaster types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to get Toaster types");
  }
};

export const getGrillTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-grill-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-grill-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to get Grill types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to get Grill types");
  }
};

export const getPoolTypes = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-pool-type`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-pool-type`] },
    });

    if (!res.ok) {
      throw new Error("Failed to get Grill types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to get Grill types");
  }
};

export const getTVServices = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const res = await fetch(`${BASE_URL}/enum-tv-service`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`enum-tv-service`] },
    });

    if (!res.ok) {
      throw new Error("Failed to get TV service types");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to get TV service types");
  }
};
