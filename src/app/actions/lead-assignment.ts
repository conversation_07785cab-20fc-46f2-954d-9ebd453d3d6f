"use server";

import { cookies } from "next/headers";

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export interface AgentLeadStats {
  agent_id: number;
  agent_name: string;
  leads_declined: number;
  leads_timed_out: number;
  leads_accepted: number;
  leads_booked: number;
  total_leads: number;
  acceptance_rate: number;
  booking_rate: number;
}

export const getAgentLeadStats = async (startDate?: string, endDate?: string): Promise<AgentLeadStats[]> => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  // If no BASE_URL is configured, return mock data for development
  if (!BASE_URL) {
    console.log("No API base URL configured, returning mock lead assignment data");
    return [
      {
        agent_id: 1,
        agent_name: '<PERSON>',
        leads_declined: 8,
        leads_timed_out: 12,
        leads_accepted: 25,
        leads_booked: 18,
        total_leads: 45,
        acceptance_rate: 55.6,
        booking_rate: 72.0,
      },
      {
        agent_id: 2,
        agent_name: '<PERSON>',
        leads_declined: 5,
        leads_timed_out: 8,
        leads_accepted: 25,
        leads_booked: 20,
        total_leads: 38,
        acceptance_rate: 65.8,
        booking_rate: 80.0,
      },
      {
        agent_id: 3,
        agent_name: '<PERSON>',
        leads_declined: 12,
        leads_timed_out: 15,
        leads_accepted: 25,
        leads_booked: 22,
        total_leads: 52,
        acceptance_rate: 48.1,
        booking_rate: 88.0,
      },
    ];
  }

  try {
    // Build query parameters
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const res = await fetch(`${BASE_URL}/booking/lead-assignment/agent_stats/?${params.toString()}`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 30, tags: ["lead-assignment-stats"] },
    });

    if (!res.ok) {
      console.log("API response not ok:", res.status, res.statusText);
      throw new Error("Failed to fetch agent lead statistics");
    }

    const data = await res.json();
    
    // Process the data to calculate rates correctly
    const processedData = data.map((agent: any) => ({
      agent_id: agent.agent_id,
      agent_name: agent.agent_name,
      leads_declined: agent.leads_declined,
      leads_timed_out: agent.leads_timed_out,
      leads_accepted: agent.leads_accepted,
      leads_booked: agent.leads_booked,
      total_leads: agent.total_leads,
      acceptance_rate: agent.acceptance_rate,
      booking_rate: agent.leads_accepted > 0 ? (agent.leads_booked / agent.leads_accepted) * 100 : 0,
    }));

    return processedData;
  } catch (error) {
    console.log("Error fetching agent lead stats:", error);
    throw new Error("Failed to fetch agent lead statistics");
  }
};

export const checkLeadTimeouts = async (): Promise<number> => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  // If no BASE_URL is configured, return mock result for development
  if (!BASE_URL) {
    console.log("No API base URL configured, returning mock timeout check result");
    return 3; // Mock number of timed out leads
  }

  try {
    const res = await fetch(`${BASE_URL}/booking/lead-assignment/check_timeouts/`, {
      method: 'POST',
      headers: {
        Authorization: `JWT ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!res.ok) {
      throw new Error("Failed to check lead timeouts");
    }

    const data = await res.json();
    return data.timed_out_count || 0;
  } catch (error) {
    console.log("Error checking lead timeouts:", error);
    throw new Error("Failed to check lead timeouts");
  }
};
