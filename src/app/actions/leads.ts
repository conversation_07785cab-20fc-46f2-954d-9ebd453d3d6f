'use server';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getLeads = async <T>(
  offset: number,
  limit: number,
  ordering: string,
  status: string,
  user_name?: string
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(
      `${BASE_URL}/rental-opportunity?ordering=${ordering}&offset=${offset}&limit=${limit}&status=${status}&user_name=${user_name}`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: { revalidate: 0, tags: [`leads-${offset}-${limit}`] },
      }
    );

    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch leads');
  }
};

export const updateLead = async <T>(id: number, payload: any) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/rental-opportunity/${id}`, {
    method: 'PATCH',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log('Error', res, res.headers);
    throw new Error('Failed to update Lead');
  }

  return res.json() as T;
};
