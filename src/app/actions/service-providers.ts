"use server";

import {
  ServiceProviderCompanyPayload,
  ServiceProviderPayload,
} from "@/types/service-providers";
import { cookies } from "next/headers";

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export const getPropertyServiceProviders = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/service-provider?listing=${id}`, {
    headers: {
      Authorization: `JWT ${token}`,
    },
    next: { revalidate: 10, tags: [`property-service-providers-${id}`] },
  });

  if (!res.ok) {
    throw new Error("Failed to fetch service providers");
  }

  return res.json() as T;
};

export const addServiceProviderData = async <T>(
  payload: ServiceProviderPayload
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/service-provider`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log("Error is", res);
    throw new Error("Failed to add service provider data");
  }

  return res.json();
};

export const updateServiceProviderData = async <T>(
  serviceProviderId: string,
  payload: ServiceProviderPayload
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  const res = await fetch(`${BASE_URL}/service-provider/${serviceProviderId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  console.log("payload is", payload, serviceProviderId, res);

  if (!res.ok) {
    throw new Error("Failed to update service provider data");
  }

  return res.json();
};

export const addServiceProviderNote = async <T>(
  serviceProviderId: string,
  payload: {
    content: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(
    `${BASE_URL}/service-provider/${serviceProviderId}/comments`,
    {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(payload),
    }
  );

  if (!res.ok) {
    throw new Error("Failed to add notes");
  }

  return res.json();
};

export const deleteServiceProviderNote = async <T>(
  serviceProviderId: string,
  commentId: string
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(
    `${BASE_URL}/service-provider/${serviceProviderId}/comments/${commentId}`,
    {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `JWT ${token}`,
      },
    }
  );

  if (!res.ok) {
    throw new Error("Failed to delete note");
  }

  return;
};

export const getServiceProviderCompanies = async <T>(
  searchText = "",
  type = ""
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(
    `${BASE_URL}/service-provider-company?search=${searchText}&type=${type}&limit=20`,
    {
      headers: {
        Authorization: `JWT ${token}`,
      },
      cache: "no-store",
    }
  );

  if (!res.ok) {
    throw new Error("Failed to fetch service provider companies");
  }

  return res.json() as T;
};

export const addServiceProviderComapany = async <T>(
  payload: ServiceProviderCompanyPayload
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/service-provider-company`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    throw new Error("Failed to add notes");
  }

  return res.json();
};

export const updateServiceProviderComapany = async <T>(
  id: number,
  payload: ServiceProviderCompanyPayload
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/service-provider-company/${id}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log("error", res);
    throw new Error("Failed to add notes");
  }

  return res.json();
};
