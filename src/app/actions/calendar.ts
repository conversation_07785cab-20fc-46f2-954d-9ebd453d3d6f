"use server";

import {
  BlockedType,
  PropertyAvailabilityPayload,
  PropertyRentalRatePayload,
} from "@/types/calendar";

import dayjs from "dayjs";
import { cookies } from "next/headers";

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export const getRentalRateAndRulesForProperty = async <T>(
  propertyId: number,
  startDate: string,
  endDate: string
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  const res = await fetch(
    `${BASE_URL}/property/rentalrate/?nightlyRate=1&nrPropertyId=${propertyId}&startDate=${startDate}&endDate=${endDate}`,
    {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: {
        revalidate: 0,
        tags: [
          `rentalRates-${propertyId}-${dayjs(startDate, "YYYY-MM-DD").year()}`,
        ],
      },
    }
  );

  if (!res.ok) {
    throw new Error("Failed to fetch rental rate for property");
  }

  return res.json() as T;
};

export const addCalendarAvailability = async <T>(
  propertyId: number,
  payload: { availabilities: PropertyAvailabilityPayload[] }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  const res = await fetch(`${BASE_URL}/listings/${propertyId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log("the error is", res);
    throw new Error("Failed to Submit Calendar Availability");
  }

  return res.json() as T;
};

export const addCalendarRentalRate = async <T>(
  propertyId: number,
  payload: { rates: PropertyRentalRatePayload[] }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  const res = await fetch(`${BASE_URL}/listings/${propertyId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.error(res);
    throw new Error("Failed to Submit Calendar rental rate");
  }

  return res.json() as T;
};

export const updateCalendarAvailability = async (
  propertyId: number,
  payload: { availabilities: PropertyAvailabilityPayload[] }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/listings/${propertyId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log("Error", res);
    throw new Error("Failed to update Calendar availability");
  }

  return res.json();
};

export const deleteCalendarAvailability = async (
  propertyId: number,
  payload: {
    availabilities: {
      from_date: string;
      to_date: string;
      availability: boolean;
      type: BlockedType;
    }[];
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/listings/${propertyId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log("Error", res, res.headers);
    throw new Error("Failed to delete Calendar availability");
  }

  return res.json();
};

export const getCalendarUpdateLogs = async <T>(
  propertyId: number,
  limit: number = 5,
  offset: number = 0
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const url = `${BASE_URL}/listings/${propertyId}/calendar-update-log?limit=${limit}&offset=${offset}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `JWT ${token}`,
    },
    next: {
      revalidate: 0,
      tags: [`calendar-update-logs-${propertyId}-${offset}`],
    },
  });

  if (!res.ok) {
    console.debug("Failed to fetch calendar update logs");
    return { count: 0, results: [] };
  }

  return res.json() as T;
};

export const getCalendarNotes = async <T>(
  propertyId: number,
  offset: number = 0,
  limit: number = 5
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const url = `${BASE_URL}/listings/${propertyId}/comments?offset=${offset}&limit=${limit}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `JWT ${token}`,
    },
    next: {
      revalidate: 0,
      tags: [`calendar-notes-${propertyId}`],
    },
  });

  if (!res.ok) {
    console.debug("Failed to fetch calendar notes");
    return { count: 0, results: [] };
  }

  return res.json() as T;
};

export const addCalendarNote = async <T>(
  propertyId: number,
  payload: {
    content: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/listings/${propertyId}/comments`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    throw new Error("Failed to add note");
  }

  return res.json();
};

export const updateCalendarNote = async <T>(
  propertyId: number,
  commentId: string,
  payload: {
    content: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(
    `${BASE_URL}/listings/${propertyId}/comments/${commentId}`,
    {
      method: "PATCH",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(payload),
    }
  );

  if (!res.ok) {
    throw new Error("Failed to update note");
  }

  return res.json();
};

export const deleteCalendarNote = async <T>(
  propertyId: number,
  commentId: string
) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(
    `${BASE_URL}/listings/${propertyId}/comments/${commentId}`,
    {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `JWT ${token}`,
      },
    }
  );

  if (!res.ok) {
    throw new Error("Failed to delete note");
  }

  return;
};

export const getBrokerages = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  const res = await fetch(`${BASE_URL}/brokerages`, {
    headers: {
      Authorization: `JWT ${token}`,
    },
    next: {
      revalidate: 0,
      tags: [`brokerages`],
    },
  });

  if (!res.ok) {
    throw new Error("Failed to fetch brokerages");
  }

  return res.json() as T;
};

export const saveCalendarLink = async <T>(payload: {
  listing: number;
  url: string;
}) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/import-calendar`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    throw new Error("Failed to update calendar link");
  }

  return res.json();
};

export const validateCalendar = async (propertyId: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;
  const res = await fetch(`${BASE_URL}/listings/${propertyId}`, {
    method: "PATCH",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify({
      rates: [],
      availabilities: [],
    }),
  });

  if (!res.ok) {
    throw new Error("Failed to validate Calendar");
  }

  return res.json();
};
