import { getPropertyDetails } from "@/app/actions/property";

import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";
import { getUserProfile } from "@/app/actions/profile";
import { UserProfile } from "@/types/profile";
import PaymentInformationForm from "@/clients/views/rental-listings/payment-information/PaymentInformationForm";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingGeneralInfoPage = async ({ params: { id } }: PageProps) => {
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, propertyData] = await Promise.all([
    userDataPromise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return (
    <div className="border p-4 rounded-lg w-full lg:w-[50%]">
      <PaymentInformationForm property={propertyData} />
    </div>
  );
};

export default ListingGeneralInfoPage;
