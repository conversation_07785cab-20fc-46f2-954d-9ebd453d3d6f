import { getPropertyDetails } from "@/app/actions/property";
import GeneralInfoFormWrapper from "@/clients/views/rental-listings/general-info/GeneralInfoFormWrapper";
import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";
import { getUserProfile } from "@/app/actions/profile";
import { UserProfile } from "@/types/profile";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingGeneralInfoPage = async ({ params: { id } }: PageProps) => {
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, propertyData] = await Promise.all([
    userDataPromise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return <GeneralInfoFormWrapper property={propertyData} />;
};

export default ListingGeneralInfoPage;
