import { getPropertyDetails } from "@/app/actions/property";
import { Property } from "@/types/property";
import Image from "next/image";
import { notFound, redirect } from "next/navigation";
import PhotosEditWrapper from "@/clients/views/rental-listings/photos/PhotosEditWrapper";
import { getUserProfile } from "@/app/actions/profile";
import { UserProfile } from "@/types/profile";
import AddPhotoButton from "@/clients/views/rental-listings/photos/AddPhotoButton";
import SortPhotosButton from "@/clients/views/rental-listings/photos/SortPhotosButton";
import PhotosDropWrapper from "@/clients/views/rental-listings/photos/PhotosDropWrapper";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingPhotosPage = async ({ params: { id } }: PageProps) => {
  const userData = getUserProfile<UserProfile>();
  const propertyDetails = getPropertyDetails<Property>(Number(id));

  const [data, propertyData] = await Promise.all([userData, propertyDetails]);

  if (!data.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return (
    <div className="md:border p-2 md:p-4 rounded-lg">
      <PhotosDropWrapper
        images={propertyData?.images ?? []}
        propertyId={propertyData.listing_id}
      >
        <div className="px-0 py-2 flex items-center justify-between mb-2">
          <p className="text-sm font-bold">Photos</p>
          <div className="flex items-center gap-2">
            <AddPhotoButton
              propertyId={Number(id)}
              images={propertyData?.images ?? []}
            />
            <SortPhotosButton
              photos={propertyData?.images ?? []}
              propertyId={propertyData.listing_id}
            />
          </div>
        </div>
        <PhotosEditWrapper
          images={propertyData?.images ?? []}
          isAdmin={data?.is_admin}
          propertyId={propertyData.listing_id}
          virtual_tour_link={propertyData?.virtual_tour_link}
        >
          <div className="flex items-center flex-wrap gap-[10px] xl:gap-3">
            {propertyData?.images?.map((_image, index) => (
              <Image
                key={index}
                id={index.toString()}
                alt="property image"
                src={_image.small_url}
                width={0}
                height={0}
                sizes="128px"
                className="w-[calc(50%-5px)] md:w-[calc(33%-4px)] lg:w-[calc(25%-9px)] xl:w-[calc(20%-10px)] h-[100px] md:h-[160px] xl:h-[180px] shadow-card cursor-pointer rounded-[10px]"
              />
            ))}
          </div>
        </PhotosEditWrapper>
      </PhotosDropWrapper>
    </div>
  );
};

export default ListingPhotosPage;
