import { getUserProfile } from "@/app/actions/profile";
import { getPropertyDetails } from "@/app/actions/property";
import DescriptionForm from "@/clients/views/rental-listings/descriptions/DescriptionForm";
import FloorDescriptionForm from "@/clients/views/rental-listings/descriptions/FloorsDescriptionForm";
import { UserProfile } from "@/types/profile";
import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingDescriptionsPage = async ({ params: { id } }: PageProps) => {
  const userData = getUserProfile<UserProfile>();
  const propertyDetails = getPropertyDetails<Property>(Number(id));
  const [data, propertyData] = await Promise.all([userData, propertyDetails]);

  if (!data.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return (
    <>
      <div className="flex gap-4 flex-col lg:flex-row">
        <div className="w-full xl:w-[50%] 2xl:w-[40%] md:border p-4 rounded-lg h-min">
          <DescriptionForm
            property={propertyData}
            isAdmin={(await userData).is_admin}
            permaLink={
              <div className="py-2 flex items-center gap-2 w-full">
                <p className="w-[30%] text-xs">Permalink</p>
                <a
                  target="_blank"
                  className="w-[70%] text-xs underline"
                  href={`https://www.congdonandcoleman.com/nantucket-rentals/${propertyData.slug}`}
                >
                  {`https://www.congdonandcoleman.com/nantucket-rentals/${propertyData.slug}`}
                </a>
              </div>
            }
          />
        </div>
        <div className="w-full xl:w-[50%] 2xl:w-[60%] md:border p-4 rounded-lg h-min">
          <FloorDescriptionForm property={propertyData} />
        </div>
      </div>
    </>
  );
};

export default ListingDescriptionsPage;
