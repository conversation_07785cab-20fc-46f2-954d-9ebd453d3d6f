import {
  getAcTypes,
  getCoffeeMakerTypes,
  getGrillTypes,
  getHeatingTypes,
  getPoolTypes,
  getStoveTypes,
  getToasterTypes,
  getTVServices,
} from "@/app/actions/amenities";
import { getUserProfile } from "@/app/actions/profile";
import { getPropertyDetails } from "@/app/actions/property";
import AmenitiesFormWrapper from "@/clients/views/rental-listings/amenities/AmenitiesFormWrapper";
import { UserProfile } from "@/types/profile";
import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingAmenitiesPage = async ({ params: { id } }: PageProps) => {
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, propertyData] = await Promise.all([
    userDataPromise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  const acTypesPromise = getAcTypes<{ id: number; name: string }[]>();
  const heatingTypesPromise = getHeatingTypes<{ id: number; name: string }[]>();
  const coffeeMakerTypesPromise =
    getCoffeeMakerTypes<{ id: number; name: string }[]>();
  const stoveTypesPromise = getStoveTypes<{ id: number; name: string }[]>();
  const toasterTypesPromise = getToasterTypes<{ id: number; name: string }[]>();
  const grillTypesPromise = getGrillTypes<{ id: number; name: string }[]>();
  const poolTypesPromise = getPoolTypes<{ id: number; name: string }[]>();
  const tvServicePromise = getTVServices<{ id: number; name: string }[]>();
  const [
    acTypes,
    heatingTypes,
    coffeeMakerTypes,
    stoveTypes,
    toasterTypes,
    grillTypes,
    poolTypes,
    tvServices,
  ] = await Promise.all([
    acTypesPromise,
    heatingTypesPromise,
    coffeeMakerTypesPromise,
    stoveTypesPromise,
    toasterTypesPromise,
    grillTypesPromise,
    poolTypesPromise,
    tvServicePromise,
  ]);

  if (!propertyData) {
    notFound();
  }
  return (
    <AmenitiesFormWrapper
      property={propertyData}
      acTypes={acTypes ?? []}
      heatingTypes={heatingTypes ?? []}
      coffeeMakerTypes={coffeeMakerTypes ?? []}
      stoveTypes={stoveTypes ?? []}
      toasterTypes={toasterTypes ?? []}
      grillTypes={grillTypes ?? []}
      poolTypes={poolTypes ?? []}
      tvServices={tvServices ?? []}
    />
  );
};

export default ListingAmenitiesPage;
