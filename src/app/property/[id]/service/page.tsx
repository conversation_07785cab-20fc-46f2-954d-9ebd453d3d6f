import { getUserProfile } from "@/app/actions/profile";
import { getPropertyServiceProviders } from "@/app/actions/service-providers";
import ServiceProviderCompanyEdit from "@/clients/views/rental-listings/service-providers/ServiceProviderCompanyEdit";
import ServiceProviderCompanyForm from "@/clients/views/rental-listings/service-providers/ServiceProviderCompanyForm";
import ServiceProviderItem from "@/clients/views/rental-listings/service-providers/ServiceProviderItem";
import { UserProfile } from "@/types/profile";
import {
  PropertyServiceProvider,
  ServiceProviderType,
} from "@/types/service-providers";
import { redirect } from "next/navigation";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingServiceProvidersPage = async ({ params: { id } }: PageProps) => {
  const userDataPromise = getUserProfile<UserProfile>();
  const serviceProviderPromise = getPropertyServiceProviders<{
    results: PropertyServiceProvider[];
  }>(Number(id));

  const [userData, { results }] = await Promise.all([
    userDataPromise,
    serviceProviderPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
      <div className="grid grid-cols-1 gap-2">
        <ServiceProviderItem
          title="Alarm Company"
          data={results?.find(
            (_s) => _s.type === ServiceProviderType.ALARM_COMPANY
          )}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.ALARM_COMPANY}
        />
        <ServiceProviderItem
          title="Caretaker"
          data={results?.find(
            (_s) => _s.type === ServiceProviderType.CARETAKER
          )}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.CARETAKER}
        />

        <ServiceProviderItem
          title="Cleaner"
          data={results?.find((_s) => _s.type === ServiceProviderType.CLEANER)}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.CLEANER}
        />
        <ServiceProviderItem
          title="Electrician"
          data={results?.find(
            (_s) => _s.type === ServiceProviderType.ELECTRICIAN
          )}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.ELECTRICIAN}
        />
        <ServiceProviderItem
          title="Landscaper"
          data={results?.find(
            (_s) => _s.type === ServiceProviderType.LANDSCAPER
          )}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.LANDSCAPER}
        />
        <ServiceProviderItem
          title="Plumber"
          data={results?.find((_s) => _s.type === ServiceProviderType.PLUMBER)}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.PLUMBER}
        />
        <ServiceProviderItem
          title="Trash Service"
          data={results?.find(
            (_s) => _s.type === ServiceProviderType.TRASH_SERVICE
          )}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.TRASH_SERVICE}
        />
        <ServiceProviderItem
          title="Other"
          data={results?.find((_s) => _s.type === ServiceProviderType.OTHER)}
          userId={userData.user_id}
          listingId={Number(id)}
          type={ServiceProviderType.OTHER}
        />
      </div>
      <div className="">
        <ServiceProviderCompanyEdit
          userId={userData.user_id}
          listingId={Number(id)}
        />
        <div className="border p-4 rounded-lg mt-2">
          <p className="text-sm font-bold">
            Add a service provider to the database
          </p>
          <ServiceProviderCompanyForm />
        </div>
      </div>
    </div>
  );
};

export default ListingServiceProvidersPage;
