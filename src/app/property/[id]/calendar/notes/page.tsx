import { getCalendarNotes } from "@/app/actions/calendar";
import { getUserProfile } from "@/app/actions/profile";
import { Pagination } from "@/app/components/Pagination";
import CalendarNoteActions from "@/clients/views/calendar/CalendarNoteActions";
import { UserProfile } from "@/types/profile";
import { Comment } from "@/types/service-providers";
import dayjs from "dayjs";
import { redirect } from "next/navigation";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
  searchParams: {
    offset: string;
    limit: string;
  };
};

export default async function CalendarNotes({
  searchParams,
  params: { id },
}: PageProps) {
  const calendarNotesPromise = getCalendarNotes<{
    count: number;
    next: string;
    results: Comment[];
  }>(Number(id), Number(searchParams?.offset ?? 0), 10);
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, { results, count }] = await Promise.all([
    userDataPromise,
    calendarNotesPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  return (
    <div className="md:border p-2 md:p-4 rounded-lg">
      <p className="text-sm font-bold">Calendar notes</p>
      <div className="overflow-x-auto my-4">
        <table className="table text-xs text-left">
          <thead className="bg-white border-t">
            <tr>
              <th className="text-black-60 font-normal w-[10%]">Date</th>
              <th className="text-black-60 font-normal w-[20%]">Posted by</th>
              <th className="text-black-60 font-normal w-[70%]">Note</th>
            </tr>
          </thead>
          <tbody>
            {results?.map((_comment, index) => (
              <tr key={index}>
                <td className="text-left align-top">
                  {dayjs(_comment.date).format("MM/DD/YYYY")}
                </td>
                <td className="text-left align-top">{_comment.posted_by}</td>
                <td className="text-left align-top flex gap-4">
                  <p className="flex-grow">{_comment.content}</p>
                  <CalendarNoteActions
                    propertyId={Number(id)}
                    comment={_comment}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {count > 10 && (
        <Pagination
          prefix={`/${Number(id)}/calendar/notes`}
          queryParams={{
            offset: Number(searchParams?.offset ?? 0),
          }}
          total={count}
        />
      )}
    </div>
  );
}
