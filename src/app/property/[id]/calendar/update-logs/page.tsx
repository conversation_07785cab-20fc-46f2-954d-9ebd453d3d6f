import { getCalendarUpdateLogs } from '@/app/actions/calendar';
import { getUserProfile } from '@/app/actions/profile';
import LogContentItem from '@/app/components/calendar/update-logs/LogContentItem';
import { Pagination } from '@/app/components/Pagination';
import { UpdateLog, UpdateLogContent } from '@/types/calendar';
import { UserProfile } from '@/types/profile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { redirect } from 'next/navigation';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type PageProps = {
  params: { id: string | string[] };
  searchParams: {
    offset: string;
    limit: string;
  };
};

// Group logs by week starting on Saturday
const groupLogsByWeek = (logs: UpdateLog[]) => {
  if (!logs || logs.length === 0) return [];

  const weeks: { weekStart: string; logs: UpdateLog[]; type?: string }[] = [];

  logs.forEach((log) => {
    if (log.type === 'validate') {
      weeks.push({
        weekStart: dayjs(log.created_at).format('YYYY-MM-DD'),
        logs: [log],
        type: 'Validated',
      });
    }
    if (log.content instanceof Array && log.content.length === 0) {
      return;
    }
    const logDate = dayjs((log.content as any).from_date);
    // Calculate days since Saturday (day 6)
    // If today is Saturday (6), we want 0
    // If today is Sunday (0), we want 1
    // If today is Monday (1), we want 2, etc.
    const daysSinceSaturday = (logDate.day() + 1) % 7;
    const weekStart = logDate
      .subtract(daysSinceSaturday, 'day')
      .format('YYYY-MM-DD');

    // Find or create the week group
    let weekGroup = weeks.find((w) => w.weekStart === weekStart);
    if (!weekGroup) {
      weekGroup = { weekStart, logs: [], type: 'Updated' };
      weeks.push(weekGroup);
    }

    weekGroup.logs.push(log);
  });

  return weeks;
};

export default async function CalendarUpdateLogs({
  searchParams,
  params: { id },
}: PageProps) {
  // Define page size constant
  const PAGE_SIZE = 70;

  const updateLogsPromise = getCalendarUpdateLogs<{
    count: number;
    next: string;
    results: UpdateLog[];
  }>(Number(id), PAGE_SIZE, Number(searchParams?.offset ?? 0));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, { results, count }] = await Promise.all([
    userDataPromise,
    updateLogsPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
  }

  // Group logs by week
  const weeklyLogs = groupLogsByWeek(results || []);

  return (
    <div className="md:border p-2 md:p-4 rounded-lg">
      <p className="text-sm font-bold">Calendar update logs</p>

      <div className="overflow-x-auto my-4">
        {weeklyLogs.length === 0 ? (
          <p className="text-center py-4 text-gray-500">No update logs found</p>
        ) : (
          <table className="table text-xs text-left w-full">
            <thead className="bg-slate-50">
              <tr>
                <th className="text-black-60 font-normal w-[10%]">DATE</th>
                <th className="text-black-60 font-normal w-[15%]">
                  UPDATED BY
                </th>
                <th className="text-black-60 font-normal w-[15%]">TYPE</th>
                <th className="text-black-60 font-normal flex items-center gap-2 flex-grow">
                  <p className="w-[50%]">OLD</p>
                  <p className="w-[50%]">NEW</p>
                </th>
              </tr>
            </thead>
            <tbody>
              {weeklyLogs.map((week, weekIndex) => (
                <tr key={weekIndex}>
                  {week.type === 'Validated' ? (
                    <>
                      <td>
                        {dayjs(week.logs[0].created_at).format('MM/DD/YYYY')}
                      </td>
                      <td>{week.logs[0].user_name}</td>
                      <td className="uppercase">{week.type}</td>
                    </>
                  ) : (
                    <>
                      <td>
                        {dayjs(week.logs[0].created_at).format('MM/DD/YYYY')}
                      </td>
                      <td>{week.logs[0].user_name}</td>
                      <td className="uppercase">{week.logs[0].type}</td>
                      <td>
                        {week.logs[0].content instanceof Array ? (
                          (week.logs[0].content ?? [])?.map(
                            (_content, index) => (
                              <div
                                key={index}
                                className={classNames(
                                  'flex items-start gap-2',
                                  {
                                    'mb-2 pb-2 border-b':
                                      (
                                        week.logs[0]
                                          .content as UpdateLogContent[]
                                      ).length >
                                      index + 1,
                                  }
                                )}
                              >
                                <div className="w-[50%]">
                                  {_content.from_date} - {_content.to_date} =
                                  <LogContentItem
                                    value={_content.old_value}
                                    type={week.logs[0].type}
                                    contentType={_content?.type}
                                  />
                                </div>
                                <div className="w-[50%]">
                                  {_content.from_date} - {_content.to_date} =
                                  <LogContentItem
                                    value={_content.new_value}
                                    type={week.logs[0].type}
                                    contentType={_content?.type}
                                  />
                                </div>
                              </div>
                            )
                          )
                        ) : (
                          <div className={classNames('flex items-start gap-2')}>
                            <div className="w-[50%]">
                              {
                                (
                                  week.logs[week.logs.length - 1]
                                    .content as UpdateLogContent
                                )?.from_date
                              }{' '}
                              - {week.logs[0].content?.to_date} =
                              <LogContentItem
                                value={week.logs[0].content?.old_value}
                                type={week.logs[0].type}
                              />
                            </div>
                            <div className="w-[50%]">
                              {
                                (
                                  week.logs[week.logs.length - 1]
                                    .content as UpdateLogContent
                                )?.from_date
                              }{' '}
                              - {week.logs[0].content?.to_date} =
                              <LogContentItem
                                value={week.logs[0].content?.new_value}
                                type={week.logs[0].type}
                              />
                            </div>
                          </div>
                        )}
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {count > PAGE_SIZE && (
        <Pagination
          prefix={`/property/${Number(id)}/calendar/update-logs`}
          queryParams={{
            offset: Number(searchParams?.offset ?? 0),
            limit: PAGE_SIZE,
          }}
          total={count}
          pageSize={PAGE_SIZE}
        />
      )}
    </div>
  );
}
