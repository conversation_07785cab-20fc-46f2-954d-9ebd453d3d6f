import { getUserProfile } from "@/app/actions/profile";
import { getPropertyDetails } from "@/app/actions/property";
import BathroomsForm from "@/clients/views/rental-listings/bedroomsandbathrooms/BathroomsForm";
import BedroomsForm from "@/clients/views/rental-listings/bedroomsandbathrooms/BedroomsForm";
import { UserProfile } from "@/types/profile";
import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingRoomsPage = async ({ params: { id } }: PageProps) => {
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, propertyData] = await Promise.all([
    userDataPromise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 ">
      <div className="md:border p-4 rounded-lg h-min">
        <BedroomsForm property={propertyData} />
      </div>
      <div className="md:border p-4 rounded-lg h-min">
        <BathroomsForm property={propertyData} />
      </div>
    </div>
  );
};

export default ListingRoomsPage;
