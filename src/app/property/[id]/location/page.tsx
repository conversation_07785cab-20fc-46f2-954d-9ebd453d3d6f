import { getUserProfile } from "@/app/actions/profile";
import { getPropertyDetails } from "@/app/actions/property";
import LocationForm from "@/clients/views/rental-listings/location/LocationForm";
import { UserProfile } from "@/types/profile";
import { Property } from "@/types/property";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingLocationPage = async ({ params: { id } }: PageProps) => {
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const userDataPromise = getUserProfile<UserProfile>();
  const [userData, propertyData] = await Promise.all([
    userDataPromise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }

  return (
    <>
      <div className="mr-0 xl:mr-4">
        <Suspense fallback="Loading...">
          <LocationForm property={propertyData} />
        </Suspense>
      </div>
    </>
  );
};

export default ListingLocationPage;
