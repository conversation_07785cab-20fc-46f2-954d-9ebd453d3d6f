import React, { memo } from 'react';

import cn from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  error?: boolean;
  children?: React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement>;

const FormHelperText = ({ error, children, className }: Props) => {
  return (
    <div
      className={twMerge(
        cn('text-xs', {
          'text-error': error,
          'text-grey-main': !error,
        }),
        className,
      )}
    >
      {children}
    </div>
  );
};

export default memo(FormHelperText);
