import { ChevronRightIcon } from "@heroicons/react/24/outline";

import Link from "next/link";
import { twMerge } from "tailwind-merge";

type BreadcrumbElements = {
  id: number;
  title: string;
  link: string;
};

type Props = {
  elements: BreadcrumbElements[];
  className?: string;
};

const Breadcrumb = ({ elements, className }: Props) => {
  return (
    <div className={twMerge("flex items-center gap-8", className)}>
      {elements.map((_element, index) => (
        <div key={index} className="flex items-center gap-8">
          {index > 0 && <ChevronRightIcon className="h-[14px] w-auto" />}
          <Link
            href={_element.link}
            className="text-sm tracking-[0.84px] capitalize"
          >
            {_element.title}
          </Link>
        </div>
      ))}
    </div>
  );
};

export default Breadcrumb;
