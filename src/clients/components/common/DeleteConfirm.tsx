'use client';

import { memo } from 'react';

import { Nullable, ProgressStatus } from '@/types/common';
import Modal from '@/clients/ui/modal';
import Button from '@/clients/ui/button';
import ManageCalendarHeader from '@/clients/views/calendar/Manage/ManageCalendarHeader';

type Props = {
  onClose: () => void;
  onDelete: () => void;
  progressStatus: Nullable<ProgressStatus>;
  deleteButtonTitle?: string;
  title?: string;
  cancelBtnTitle?: string;
};

const DeleteConfirm = ({
  onClose,
  onDelete,
  progressStatus,
  deleteButtonTitle = 'Delete',
  cancelBtnTitle = 'Close',
  title,
}: Props) => {
  return (
    <Modal open className="p-8" onClose={onClose}>
      <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-0 p-4 md:p-0">
        <ManageCalendarHeader
          title={title ?? 'Are you sure you want to delete?'}
          onClose={onClose}
        />
        <Button
          className="rounded-lg text-sm font-normal"
          isLoading={progressStatus === ProgressStatus.LOADING}
          onClick={onDelete}
        >
          {deleteButtonTitle}
        </Button>
        <Button
          intent="secondary"
          className="rounded-lg text-sm font-normal border-black"
          onClick={onClose}
        >
          {cancelBtnTitle}
        </Button>
      </div>
    </Modal>
  );
};

export default memo(DeleteConfirm);
