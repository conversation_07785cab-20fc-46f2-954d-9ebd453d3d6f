'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { DateRange } from 'react-day-picker';

import { getDateRangePickerMonths } from '@/utils/calendar';

import {
  differenceInCalendarDays,
  format,
  getMonth,
  isBefore,
  isValid,
  startOfDay,
  subMonths,
} from 'date-fns';

import Button from '@/clients/ui/button';
import useHasMounted from '@/hooks/useHasMounted';
import MobileCalendar from '../MobileCalendar';
import { PropertyAvailability, PropertyRentalRates } from '@/types/calendar';
import { getBlockedStartAndEndDates } from '@/utils/date-range-picker';
import { parseDateString } from '@/utils/common';

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  onClose: () => void;
  availableCalendar?: PropertyAvailability[];
};

const DateRangePickerMobileComponent = ({
  date,
  setDate,
  onClose,
  availableCalendar = [],
}: Props) => {
  const hasMounted = useHasMounted();
  const [selected, setSelected] = useState<DateRange | undefined>(date);
  const [startIndex, setStartIndex] = useState<number>(0);
  const [endIndex, setEndIndex] = useState<number>(4);
  const months = useMemo(() => getDateRangePickerMonths(), []);
  // Get blocked dates
  const { start: blockedStartDates, end: blockedEndDates } = useMemo(
    () =>
      getBlockedStartAndEndDates(
        availableCalendar.filter(
          (_a) =>
            !isBefore(
              startOfDay(parseDateString(_a.from_date)),
              startOfDay(subMonths(new Date(), 1))
            ) &&
            !isBefore(
              startOfDay(parseDateString(_a.to_date)),
              startOfDay(subMonths(new Date(), 1))
            )
        )
      ),
    [availableCalendar]
  );

  const onClearDates = useCallback(() => {
    setDate(undefined);
    setSelected(undefined);
  }, [setDate]);

  const onConfirm = useCallback(() => {
    setDate(selected);
    onClose();
  }, [onClose, selected, setDate]);

  const isCheckinSelected = useMemo(
    () => selected?.from && isValid(selected.from),
    [selected?.from]
  );

  const isSelectedValid = useMemo(
    () =>
      !!(
        selected?.from &&
        selected?.to &&
        isValid(selected.from) &&
        isValid(selected.to)
      ),
    [selected?.from, selected?.to]
  );

  const onClickLoadPrev = useCallback(() => {
    setStartIndex(startIndex - 4 < 0 ? 0 : startIndex - 4);
  }, [startIndex]);

  const onClickLoadMore = useCallback(() => {
    setEndIndex(endIndex + 4 > months.length ? months.length : endIndex + 4);
  }, [endIndex, months.length]);

  const headerText = useMemo(() => {
    if (isCheckinSelected) {
      if (isSelectedValid && selected?.from && selected?.to) {
        return `${differenceInCalendarDays(
          selected?.to,
          selected?.from
        )} nights`;
      }
      return 'Select check-out date';
    } else {
      return 'Select check-in date';
    }
  }, [isCheckinSelected, isSelectedValid, selected?.from, selected?.to]);

  useEffect(() => {
    const startMonth = date?.from && date?.to && getMonth(date.from);
    const startIndex = months.findIndex((_m) => _m.month === startMonth);
    if (!hasMounted) {
      setStartIndex(startIndex > 0 ? startIndex : 0);
      setEndIndex((startIndex > 0 ? startIndex : 0) + 4);
    }
  }, [date?.from, date?.to, hasMounted, months]);

  return (
    <div className="p-4 bg-white rounded-t-[10px]">
      <div className="flex items-center justify-between">
        <div>
          <p className="m-0 text-xl font-medium">{headerText}</p>
          {isSelectedValid && selected?.from && selected?.to && (
            <p className="text-xs text-[#6D7380] m-0 mt-2">{`${format(
              selected?.from,
              'LLL d, yyyy'
            )} - ${format(selected?.to, 'LLL d, yyyy')}`}</p>
          )}
        </div>
        <Button
          intent="ghost"
          className="px-0 text-primary-text-blue text-xs"
          onClick={onClearDates}
        >
          Clear date
        </Button>
      </div>
      <div className="flex items-center gap-x-1 pb-2 border border-t-0 border-l-0 border-r-0 border-b border-solid border-[#6D7380] text-[#6D7380] mb-4">
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          SUN
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          MON{' '}
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          TUE
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          WED
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          THU
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          FRI
        </span>
        <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
          SAT
        </span>
      </div>
      <div className="overflow-y-auto h-[55vh] calendar-wrapper">
        {startIndex > 0 && (
          <Button
            intent="outline"
            className="w-full border-solid border-carolina-blue font-normal rounded-[32px] mb-6"
            onClick={onClickLoadPrev}
          >
            Load more dates
          </Button>
        )}
        {months.slice(startIndex, endIndex).map((_m, index) => (
          <MobileCalendar
            key={index}
            month={_m.month}
            year={_m.year}
            selected={selected}
            onSelectDate={setSelected}
            availableCalendar={availableCalendar}
            blockedStartDates={blockedStartDates}
            blockedEndDates={blockedEndDates}
          />
        ))}
        {months.length > endIndex && (
          <Button
            intent="outline"
            className="w-full border-solid border-carolina-blue font-normal rounded-[32px]"
            onClick={onClickLoadMore}
          >
            Load more dates
          </Button>
        )}
      </div>
      <div className="mt-4 flex items-center justify-between">
        <Button
          intent="outline"
          className="font-medium rounded-[32px] py-3"
          onClick={onClose}
        >
          Close
        </Button>
        <Button
          className="font-medium rounded-[32px] py-3"
          disabled={!isSelectedValid}
          onClick={onConfirm}
        >
          Confirm
        </Button>
      </div>
    </div>
  );
};

export default DateRangePickerMobileComponent;
