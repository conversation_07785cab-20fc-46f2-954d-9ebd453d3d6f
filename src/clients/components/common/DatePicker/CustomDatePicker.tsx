import React, { useMemo, useState, useCallback } from 'react';

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/button';
import Select from '@/clients/ui/select';

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const getDaysArray = (year: number, month: number) => {
  const firstDay = new Date(year, month, 1).getDay();
  const totalDays = new Date(year, month + 1, 0).getDate();

  return Array.from({ length: firstDay + totalDays }, (_, i) =>
    i < firstDay ? null : i - firstDay + 1
  );
};

// eslint-disable-next-line react/display-name
const DayButton = React.memo(
  ({
    day,
    isToday,
    isSelected,
    isDisabled,
    onDayClick,
  }: {
    day: number | null;
    isToday: boolean;
    isSelected: boolean;
    isDisabled: boolean;
    onDayClick: (day: number | null) => void;
  }) => {
    const handleClick = useCallback(() => {
      onDayClick(day);
    }, [onDayClick, day]);

    return (
      <Button
        intent="ghost"
        disabled={!day || isDisabled}
        onClick={handleClick}
        className={`
          cursor-pointer h-10 w-10 p-0 aria-selected:opacity-100 rounded-full border border-transparent border-solid hover:border-[#6D7380] hover:rounded-full font-medium text-sm
          ${!day ? 'invisible' : ''}
          ${isToday ? 'border border-blue-500' : ''}
          ${isSelected ? '!bg-blue-500 text-white' : 'hover:bg-blue-100'}
          ${isDisabled ? 'opacity-70' : ''}
        `}
      >
        {day}
      </Button>
    );
  }
);

type Props = {
  selected?: Date;
  onSelect: (date?: Date) => void;
  minDate?: Date;
  maxDate?: Date;
};

function CustomDatePicker({ selected, onSelect, minDate, maxDate }: Props) {
  // Initialize with selected date's month/year if available, otherwise current date
  const initialDate = selected || new Date();
  const [month, setMonth] = useState(initialDate.getMonth());
  const [year, setYear] = useState(initialDate.getFullYear());

  const days = useMemo(() => getDaysArray(year, month), [year, month]);

  const years = React.useMemo(
    () =>
      Array.from(
        { length: new Date().getFullYear() + 10 - 1900 + 1 },
        (_, i) => 1900 + i
      ),
    []
  );

  const isToday = useCallback(
    (day: number) => {
      const now = new Date();
      return (
        day === now.getDate() &&
        month === now.getMonth() &&
        year === now.getFullYear()
      );
    },
    [month, year]
  );

  const isSelected = useCallback(
    (day: number) =>
      selected?.getDate() === day &&
      selected?.getMonth() === month &&
      selected?.getFullYear() === year,
    [selected, month, year]
  );

  const isDisabled = useCallback(
    (day: number) => {
      if (minDate) {
        const date = new Date(year, month, day);
        return date < minDate;
      }

      if (maxDate) {
        const date = new Date(year, month, day);
        return date > maxDate;
      }

      return false;
    },
    [minDate, maxDate, month, year]
  );

  const handleDayClick = useCallback(
    (day: number | null) => {
      if (day) {
        onSelect(new Date(year, month, day));
      }
    },
    [month, onSelect, year]
  );

  const handleYearChange = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      setYear(+value.id);
    },
    []
  );

  const handleMonthChange = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      setMonth(+value.id);
    },
    []
  );

  const handleNext = useCallback(() => {
    setMonth((prevMonth) => {
      if (prevMonth === 11) {
        setYear((prevYear) => prevYear + 1);
        return 0;
      }
      return prevMonth + 1;
    });
  }, []);

  const handlePrev = useCallback(() => {
    setMonth((prevMonth) => {
      if (prevMonth === 0) {
        setYear((prevYear) => prevYear - 1);
        return 11;
      }
      return prevMonth - 1;
    });
  }, []);

  // If selected date changes externally (from input), update the calendar view
  React.useEffect(() => {
    if (selected) {
      setMonth(selected.getMonth());
      setYear(selected.getFullYear());
    }
  }, [selected]);

  return (
    <div className="p-4 bg-white shadow-md rounded-md w-fit mx-auto">
      <div className="flex items-center justify-center gap-x-2 mb-2">
        <Button intent="ghost" onClick={handlePrev} className="cursor-pointer">
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="tv_service"
          placeholder=""
          options={Array.from({ length: 12 }).map((_, i) => ({
            id: i,
            name: new Date(year, i).toLocaleString('default', {
              month: 'long',
            }),
          }))}
          onChange={handleMonthChange}
          value={month}
        />
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="tv_service"
          placeholder=""
          options={years.map((year) => ({
            id: year,
            name: year.toString(),
          }))}
          onChange={handleYearChange}
          value={year}
        />
        <Button intent="ghost" onClick={handleNext} className="cursor-pointer">
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex">
        {weekDays.map((d) => (
          <div
            key={d}
            className="text-muted-foreground rounded-md w-9 h-10 font-normal text-[0.8rem] flex items-center justify-center flex-1"
          >
            {d[0]}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 text-center">
        {days.map((day, idx) => (
          <DayButton
            key={idx}
            day={day}
            isToday={day ? isToday(day) : false}
            isSelected={day ? isSelected(day) : false}
            isDisabled={day ? isDisabled(day) : false}
            onDayClick={handleDayClick}
          />
        ))}
      </div>
    </div>
  );
}

export default CustomDatePicker;
