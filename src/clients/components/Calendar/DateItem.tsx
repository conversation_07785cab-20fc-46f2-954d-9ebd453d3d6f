"use client";

import debounce from "lodash/debounce";
import { MouseEvent, memo, useCallback, useMemo } from "react";

import {
  BlockedType,
  CalendarViewType,
  PropertyRentalRates,
} from "@/types/calendar";
import { Nullable } from "@/types/common";
import {
  checkIfBrokerageIsCNCorNR,
  getDateRangeStartAndEnd,
  isBetweenTwoDates,
} from "@/utils/calendar";
import { formatDateItemRate } from "@/utils/rates";

import classNames from "classnames";
import dayjs, { Dayjs } from "dayjs";
import { twMerge } from "tailwind-merge";

import DateItemInfo from "./DateItemInfo";
import { PopupProps } from "@/clients/views/calendar/CalendarWrapper";
import { PropertyAvailability } from "@/types/calendar";

type Props = {
  dateObject: Dayjs;
  date: number;
  active?: boolean;
  insideCurrentMonth: boolean;
  selected: [Nullable<Dayjs>, Nullable<Dayjs>];
  mouseOverDate: Nullable<Dayjs>;
  setMouseOverDate: any;
  cellHeightClassName?: string;
  view: CalendarViewType;
  onSelectDate: (e: MouseEvent<HTMLElement>, dateObject: Dayjs) => void;
  availabilities: PropertyAvailability[];
  rentalRate?: PropertyRentalRates;
  setShowPopup?: (s: Nullable<PopupProps>) => void;
};

const DateItem = ({
  date,
  insideCurrentMonth,
  selected,
  dateObject,
  mouseOverDate,
  setMouseOverDate,
  cellHeightClassName,
  view,
  onSelectDate,
  availabilities,
  rentalRate,
  setShowPopup,
}: Props) => {
  const bookedRanges = useMemo(
    () =>
      availabilities.filter(
        ({ from_date, to_date, type, brokerage }) =>
          isBetweenTwoDates(from_date, to_date, dateObject) &&
          type === BlockedType.LEASED &&
          checkIfBrokerageIsCNCorNR(brokerage)
      ),
    [availabilities, dateObject]
  );
  const otherRanges = useMemo(
    () =>
      availabilities.filter(
        ({ from_date, to_date, type, brokerage }) =>
          isBetweenTwoDates(from_date, to_date, dateObject) &&
          (type === BlockedType.OTHER ||
            (type === BlockedType.LEASED &&
              !checkIfBrokerageIsCNCorNR(brokerage)))
      ),
    [availabilities, dateObject]
  );
  const ownerTimeRanges = useMemo(
    () =>
      availabilities.filter(
        ({ from_date, to_date, type }) =>
          isBetweenTwoDates(from_date, to_date, dateObject) &&
          type === BlockedType.OWNER_TIME
      ),
    [availabilities, dateObject]
  );

  const [isBooked, bookedIsBeginning, bookedIsEnd] = useMemo(
    () => getDateRangeStartAndEnd(bookedRanges, dateObject),
    [bookedRanges, dateObject]
  );

  const [isOther, otherIsBeginning, otherIsEnd] = useMemo(
    () => getDateRangeStartAndEnd(otherRanges, dateObject),
    [otherRanges, dateObject]
  );

  const [isOwnerTime, ownerTimeIsBeginning, ownerTimeIsEnd] = useMemo(
    () => getDateRangeStartAndEnd(ownerTimeRanges, dateObject),
    [dateObject, ownerTimeRanges]
  );

  const [, pendingEdgeIsBeginning] = useMemo(
    () => getDateRangeStartAndEnd(ownerTimeRanges, dateObject, 1),
    [dateObject, ownerTimeRanges]
  );

  const [, bookedEdgeIsBeginning] = useMemo(
    () => getDateRangeStartAndEnd(bookedRanges, dateObject, 1),
    [dateObject, bookedRanges]
  );

  const [, otherEdgeIsBeginning] = useMemo(
    () => getDateRangeStartAndEnd(otherRanges, dateObject, 1),
    [dateObject, otherRanges]
  );

  const isBeforeToday = useMemo(
    () => dateObject.isBefore(dayjs()),
    [dateObject]
  );
  const isRangeValid = useMemo(
    () => selected.every((_d) => dayjs(_d).isValid()),
    [selected]
  );

  const isSelected = useMemo(
    () =>
      selected.some(
        (_date: any) => dateObject.isSame(_date) && insideCurrentMonth
      ),
    [dateObject, insideCurrentMonth, selected]
  );

  const isHighlighted = useMemo(() => {
    return (
      dayjs(selected[0]).isValid() &&
      dateObject.isAfter(selected[0]) &&
      ((dayjs(mouseOverDate).isValid() && mouseOverDate?.isAfter(dateObject)) ||
        (dayjs(selected[1]).isValid() && dateObject.isBefore(selected[1]))) &&
      insideCurrentMonth
    );
  }, [dateObject, insideCurrentMonth, mouseOverDate, selected]);

  const isMouseOverToday = useMemo(
    () =>
      dayjs(selected[0]).isValid() &&
      dateObject.isSame(mouseOverDate) &&
      insideCurrentMonth &&
      mouseOverDate?.isAfter(selected[0]),
    [dateObject, insideCurrentMonth, mouseOverDate, selected]
  );

  const onMouseOverDate = useCallback(() => {
    if (!isRangeValid && insideCurrentMonth) {
      setMouseOverDate(dateObject);
    }
  }, [dateObject, insideCurrentMonth, isRangeValid, setMouseOverDate]);

  const onMouseLeave = useCallback(
    (e: any) => {
      setMouseOverDate(null);
      // setShowPopup?.(null);
    },
    [setMouseOverDate]
  );

  const debouncedOnMouseLeave = debounce(onMouseLeave, 200);
  const debouncedOnMouseEnter = debounce(onMouseOverDate, 200);

  const onClick = useCallback(
    (e: MouseEvent<HTMLElement>) => {
      setShowPopup?.(null);
      onSelectDate(e, dateObject);
    },
    [dateObject, onSelectDate, setShowPopup]
  );

  const isStart = useMemo(
    () => selected[0]?.isSame(dateObject),
    [dateObject, selected]
  );
  const isEnd = useMemo(
    () => selected[1]?.isSame(dateObject),
    [dateObject, selected]
  );

  return (
    <td
      id={`date__item-${dateObject.format("YYYY-MM-DD")}`}
      className={twMerge(
        classNames(
          `relative p-2 md:px-2 md:py-4 2xl:px-4 border md:rounded-[10px] z-1 h-[initial] max-w-10`,
          {
            "hover:bg-[#D0EDFA]": insideCurrentMonth,
            "!bg-carolina-blue-40": isSelected || isMouseOverToday,
            "bg-carolina-blue-20": isHighlighted,
            "text-black-40": isBeforeToday,
            "md:p-4": view === CalendarViewType.MONTH,
            "md:p-2": view === CalendarViewType.YEAR,
          }
        ),
        cellHeightClassName
      )}
      onClick={onClick}
      onMouseEnter={debouncedOnMouseEnter}
      onMouseLeave={debouncedOnMouseLeave}
    >
      {insideCurrentMonth && (
        <>
          <div
            className={classNames(
              "text-xs md:text-sm flex flex-col items-center md:items-start justify-between h-full w-full gap-2 md:gap-auto",
              {
                "md:text-xs 2xl:text-sm": view === CalendarViewType.YEAR,
              }
            )}
          >
            <p
              className={classNames("z-[2] font-bold", {
                "text-white":
                  // (isSelected && !isDisabled) ||
                  // (isMouseOverToday && !isDisabled) ||
                  (isBooked && view === CalendarViewType.YEAR) ||
                  (isOther && view === CalendarViewType.YEAR),
              })}
            >
              {date}
            </p>
            <p
              className={classNames("opacity-100 text-[10px] 2xl:text-sm", {
                hidden: isOther || isBooked || isOwnerTime,
              })}
            >
              {rentalRate?.amount
                ? formatDateItemRate(Number(rentalRate.amount))
                : "NA"}
            </p>
          </div>
          {isBooked && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning={bookedIsBeginning}
              isEnd={bookedIsEnd}
              rangeInfo={bookedRanges[0]}
              blockType={BlockedType.LEASED}
            />
          )}
          {bookedRanges.length > 1 && bookedEdgeIsBeginning && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning
              rangeInfo={bookedRanges[1]}
              blockType={BlockedType.LEASED}
            />
          )}

          {isOwnerTime && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning={ownerTimeIsBeginning}
              isEnd={ownerTimeIsEnd}
              rangeInfo={ownerTimeRanges[0]}
              blockType={BlockedType.OWNER_TIME}
            />
          )}
          {ownerTimeRanges.length > 1 && pendingEdgeIsBeginning && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning
              rangeInfo={ownerTimeRanges[1]}
              blockType={BlockedType.OWNER_TIME}
            />
          )}

          {isOther && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning={otherIsBeginning}
              isEnd={otherIsEnd}
              rangeInfo={otherRanges[0]}
              blockType={BlockedType.OTHER}
            />
          )}
          {otherRanges.length > 1 && otherEdgeIsBeginning && (
            <DateItemInfo
              view={view}
              setShowPopup={setShowPopup}
              dateObject={dateObject}
              isBeginning
              rangeInfo={otherRanges[1]}
              blockType={BlockedType.OTHER}
            />
          )}
        </>
      )}
    </td>
  );
};

export default memo(DateItem);
