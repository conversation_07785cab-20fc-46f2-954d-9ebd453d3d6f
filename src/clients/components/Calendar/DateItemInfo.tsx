import { memo, useCallback, useMemo, useRef } from "react";
import { throttle } from "lodash";
import classNames from "classnames";
import Image from "next/image";
import { Dayjs } from "dayjs";

import {
  BlockedType,
  CalendarViewType,
  PropertyAvailability,
} from "@/types/calendar";
import { Nullable } from "@/types/common";
import { PopupProps } from "@/clients/views/calendar/CalendarWrapper";
import { checkIfBrokerageIsCNCorNR } from "@/utils/calendar";
import { isHover } from "@/utils/common";

type Props = {
  dateObject: Dayjs;
  view: CalendarViewType;
  isBeginning?: boolean;
  isEnd?: boolean;
  rangeInfo: PropertyAvailability;
  setShowPopup?: (s: Nullable<PopupProps>) => void;
  blockType: BlockedType;
};
const DateItemInfo = ({
  view,
  isBeginning,
  isEnd,
  rangeInfo,
  setShowPopup,
  dateObject,
  blockType,
}: Props) => {
  const wrapperRef = useRef<null | HTMLDivElement>(null);

  const isOther = useMemo(
    () =>
      blockType === BlockedType.OTHER ||
      (blockType === BlockedType.LEASED &&
        !checkIfBrokerageIsCNCorNR(rangeInfo.brokerage)),
    [rangeInfo.brokerage, blockType]
  );

  const onMouseEnter = useCallback(
    (e: any) => {
      const parent = wrapperRef.current;
      const x =
        (parent?.getBoundingClientRect()?.left ?? 0) +
        0.5 * (parent?.getBoundingClientRect().width ?? 0) -
        200;
      const y =
        (parent?.getBoundingClientRect()?.top ?? 0) +
        0.85 * (parent?.getBoundingClientRect()?.height ?? 0);

      setShowPopup?.({
        x,
        y,
        height: parent?.getBoundingClientRect()?.height,
        width: parent?.getBoundingClientRect()?.width,
        availability_uuid: rangeInfo.availability_uuid,
      });
    },
    [rangeInfo.availability_uuid, setShowPopup]
  );

  const debouncedOnMouseEnter = throttle(onMouseEnter, 400);

  const onMouseLeave = useCallback(
    (e: any) => {
      const container = wrapperRef.current;
      const popupElement = document.getElementById("CalendarPopup");
      const isOverPopup = popupElement && isHover(popupElement);
      if (!isOverPopup || !container?.contains(e.target)) {
        setShowPopup?.(null);
      }
    },
    [setShowPopup]
  );

  const debouncedOnMouseLeave = throttle(onMouseLeave, 400);

  return (
    <div
      id={`DateItemInfo-${dateObject.format("YYYY-MM-DD")}`}
      ref={wrapperRef}
      onMouseEnter={debouncedOnMouseEnter}
      onMouseLeave={debouncedOnMouseLeave}
      className={classNames(
        "absolute cursor-pointer grid bg-carolina-blue inset-0 top-[40%] md:rounded-[10px] md:px-2 md:py-2",
        {
          "bg-leased": blockType === BlockedType.LEASED,
          "bg-owner-time": blockType === BlockedType.OWNER_TIME,
          "bg-other": blockType === BlockedType.OTHER,
          "!top-0 md:rounded-t-[10px] !p-0": view === CalendarViewType.YEAR,
          "left-[51%] rounded-l-none md:rounded-l-none rounded-tl-[10px] md:rounded-tl-[10px]":
            isBeginning,
          "md:rounded-t-none": view === CalendarViewType.MONTH,
          "md:rounded-tl-none md:rounded-tr-[10px]  !p-0":
            isBeginning && view === CalendarViewType.YEAR,
          "right-[51%] rounded-r-none md:rounded-r-none rounded-tr-[10px] md:rounded-tr-[10px]":
            isEnd,
          "md:rounded-tr-none md:rounded-tl-[10px]  !p-0":
            isEnd && view === CalendarViewType.YEAR,
        }
      )}
    >
      {!isOther && (
        <>
          {isBeginning || isEnd ? (
            <p
              className={classNames(
                "hidden md:block text-[10px] truncate max-w-10 lg:max-w-max self-center justify-self-center font-bold w-full",
                {
                  "-rotate-90": view === CalendarViewType.YEAR,
                  "text-white":
                    blockType === BlockedType.LEASED ||
                    blockType === BlockedType.OWNER_TIME,
                }
              )}
            >
              {isBeginning ? "Check in" : "Check out"}
            </p>
          ) : (
            <div
              className={classNames("flex flex-col text-left", {
                "px-2 items-center justify-center":
                  view === CalendarViewType.YEAR,
                "justify-between items-center md:items-start self-center md:self-auto":
                  view === CalendarViewType.MONTH,
              })}
            >
              {view === CalendarViewType.YEAR ? (
                <p
                  className={classNames(
                    "text-[10px] truncate max-w-10 lg:max-w-max self-center justify-self-center font-bold",
                    {
                      "text-white":
                        blockType === BlockedType.LEASED ||
                        blockType === BlockedType.OWNER_TIME ||
                        blockType === BlockedType.OTHER,
                    }
                  )}
                >
                  {blockType === BlockedType.LEASED ? "Booked" : "Owner time"}
                </p>
              ) : (
                <>
                  {rangeInfo?.brokerage && (
                    <>
                      <p
                        className={classNames("text-[5px] truncate", {
                          "text-white": blockType === BlockedType.LEASED,
                        })}
                      >
                        booked by
                      </p>
                      {rangeInfo?.brokerage?.logo_desktop ? (
                        <Image
                          alt="Source logo"
                          src={rangeInfo?.brokerage?.logo_desktop ?? ""}
                          className="w-6 md:w-[86px] h-6 md:h-5 hidden md:block"
                          width="0"
                          height="0"
                          sizes="100vw"
                        />
                      ) : (
                        <p className="hidden md:block text-[10px] truncate max-w-10 lg:max-w-max font-medium">
                          {rangeInfo?.brokerage?.name ?? ""}
                        </p>
                      )}
                      {rangeInfo?.brokerage?.logo_mobile && (
                        <Image
                          alt="Source logo"
                          src={rangeInfo?.brokerage?.logo_mobile ?? ""}
                          className="w-6 h-6 block md:hidden"
                          width="0"
                          height="0"
                          sizes="100vw"
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default memo(DateItemInfo);
