"use client";

import debounce from "lodash/debounce";
import { Fragment, memo, useCallback, useMemo } from "react";

import { Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/solid";

import Button from "@/clients/ui/button";
import { BlockedType } from "@/types/calendar";
import { Nullable } from "@/types/common";

import classNames from "classnames";
import dayjs from "dayjs";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { PopupProps } from "@/clients/views/calendar/CalendarWrapper";
import { PropertyAvailability } from "@/types/calendar";
import { checkIfBrokerageIsCNCorNR } from "@/utils/calendar";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type Props = {
  popup: Nullable<PopupProps>;
  isShowing?: boolean;
  setShowPopup?: (s: Nullable<PopupProps>) => void;
  popupData?: PropertyAvailability;
  onDeleteClick: (id: string) => void;
};

const CalendarPopup = ({
  popup,
  setShowPopup,
  popupData,
  isShowing,
  onDeleteClick,
}: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const isLeased = useMemo(
    () =>
      popupData?.type === BlockedType.LEASED &&
      checkIfBrokerageIsCNCorNR(popupData?.brokerage),
    [popupData?.brokerage, popupData?.type]
  );
  const isOther = useMemo(
    () =>
      popupData?.type === BlockedType.OTHER ||
      (popupData?.type === BlockedType.LEASED &&
        !checkIfBrokerageIsCNCorNR(popupData?.brokerage)),
    [popupData?.brokerage, popupData?.type]
  );
  const isOwnerTime = useMemo(
    () => popupData?.type === BlockedType.OWNER_TIME,
    [popupData?.type]
  );
  const onClose = useCallback(() => {
    setShowPopup?.(null);
  }, [setShowPopup]);

  const onMouseLeave = useCallback(
    (e: any) => {
      setShowPopup?.(null);
    },
    [setShowPopup]
  );

  const debouncedOnMouseLeave = debounce(onMouseLeave, 500);

  const onEditBooking = useCallback(() => {
    if (popupData?.lease) {
      window.location.assign(`${BASE_URL}/lease/${popupData.lease}`);
      return;
    }
    const params = new URLSearchParams(searchParams.toString());
    if (searchParams.has("availabilityId")) {
      params.delete("availabilityId");
    }
    params.append(
      "availabilityId",
      (popupData?.availability_uuid ?? "")?.toString()
    );

    router.push(`${pathname}?${params.toString()}`);
    onClose();
  }, [
    popupData?.lease,
    popupData?.availability_uuid,
    searchParams,
    router,
    pathname,
    onClose,
  ]);

  return (
    <>
      <Transition
        as={Fragment}
        show={isShowing}
        appear={true}
        enter="transition-opacity ease-linear duration-150"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity ease-linear duration-150"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div
          id="CalendarPopup"
          onMouseLeave={debouncedOnMouseLeave}
          className={classNames(
            "hidden opacity md:block absolute w-[400px] min-h-[100px] z-[999999] bg-white rounded-lg shadow-modal transition left-auto bottom-auto"
          )}
          style={{
            left: popup?.x && popup.x - 100 < 0 ? 56 : popup?.x,
            top: popup?.y && popup.y,
          }}
        >
          <div className="p-4 relative">
            <div>
              <div
                className="absolute right-2.5 top-2.5 z-[1] h-6 w-6 cursor-pointer"
                onClick={onClose}
              >
                <XMarkIcon className="fill-current" />
              </div>
            </div>

            <div className="text-[10px] flex gap-2 px-4 mb-2">
              <div
                className={classNames("w-4 h-4 rounded-full", {
                  "bg-leased": isLeased,
                  "bg-other": isOther,
                  "bg-owner-time": isOwnerTime,
                })}
              />
              {isLeased ? `Leased` : `Blocked`}
            </div>
            {isLeased || isOther ? (
              <div className="rounded-lg border py-4 px-2 flex-center-between mb-2">
                <div className="flex flex-col items-center w-[45%] gap-2">
                  <p className="text-xs">Check in</p>
                  <p className="text-sm md:text-xs font-bold">
                    {dayjs(popupData?.from_date).format(`MMM D, YYYY`)}
                  </p>
                </div>
                <div className="w-[10%]">
                  <div className="m-auto font-bold w-[10px] h-[1px] bg-black md:my-1" />
                </div>
                <div className="flex flex-col items-center w-[45%] gap-2">
                  <p className="text-xs">Check out</p>
                  <p className="text-sm md:text-xs font-bold">
                    {dayjs(popupData?.to_date).format(`MMM D, YYYY`)}
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex-center-between mb-2 px-4 py-2">
                <p className="text-sm md:text-xs font-bold">
                  {dayjs(popupData?.from_date).format(`MMM D, YYYY`)}
                </p>
                <div className="w-[10%]">
                  <div className="m-auto font-bold w-[10px] h-[1px] bg-black md:my-1" />
                </div>
                <p className="text-sm md:text-xs font-bold">
                  {dayjs(popupData?.to_date).format(`MMM D, YYYY`)}
                </p>
              </div>
            )}

            {(isLeased || isOther) && (
              <div className="py-2 flex-center-between">
                <div className="rounded-lg border py-2 w-full text-center text-xs">
                  <span className="font-bold">
                    {dayjs(popupData?.to_date).diff(
                      popupData?.from_date,
                      "days"
                    )}
                  </span>{" "}
                  Nights
                </div>
              </div>
            )}
            <div className="mt-2 flex-center-between">
              {(isLeased || isOther) && (
                <Button
                  intent="secondary"
                  className="rounded-lg text-sm font-normal"
                  onClick={onClose}
                >
                  Cancel
                </Button>
              )}

              {(isLeased || isOwnerTime) && (
                <Button
                  className="rounded-lg text-sm font-normal"
                  onClick={onEditBooking}
                >
                  {isLeased
                    ? "View Details"
                    : isOwnerTime
                    ? "Edit"
                    : "Edit Booking"}
                </Button>
              )}
              {(isOwnerTime || isOther) && (
                <Button
                  className="rounded-lg text-sm font-normal text-error"
                  intent="outline"
                  onClick={() => {
                    if (popupData?.availability_uuid) {
                      onDeleteClick(popupData?.availability_uuid);
                    }
                  }}
                >
                  Delete
                </Button>
              )}
            </div>
          </div>
        </div>
      </Transition>
    </>
  );
};

export default memo(CalendarPopup);
