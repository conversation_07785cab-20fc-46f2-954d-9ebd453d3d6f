'use client';

import { Lease } from '@/types/lease';
import classNames from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { memo, useMemo } from 'react';

type SidebarNavProps = {
  lease?: Lease;
};

const LeaseSidebarNav = ({ lease }: SidebarNavProps) => {
  const pathname = usePathname();
  const fragments = pathname.split('/');
  const id = fragments?.[2];
  const subpage = fragments?.[3] ?? '';

  const linkBase = id ? `/lease/${id}` : '';

  const navItems = useMemo(
    () => [
      { path: '', label: 'Lease Details', completed: lease },
      {
        path: '/agreement',
        label: 'Lease Agreement and Signatures',
        pending: lease,
        completed: lease?.sign_info?.signature_request?.signatures?.every(
          (_r) => _r.signed_at
        ),
      },
      {
        path: '/summary',
        label: 'Financial Summary',
        completed: lease?.status === 'Paid in Full',
      },
    ],
    [lease]
  );

  const isActive = (path: string, label: string) => {
    if (path === '' && subpage === '') return true;

    const pathSegment = path.replace('/', '').toLowerCase();
    const currentSubpage = subpage.toLowerCase();

    return pathSegment !== '' && currentSubpage.includes(pathSegment);
  };

  return (
    <div className="hidden md:flex md:flex-col gap-y-4 text-sm justify-start">
      {navItems.map((item) => {
        const NavItem = (
          <div
            className={classNames(
              'text-left font-medium py-2 px-4 rounded-l-md pr-2 flex gap-x-2 items-center justify-between',
              {
                'font-bold bg-white': isActive(item.path, item.label),
                'cursor-pointer': !!id,
                'cursor-not-allowed opacity-50': !id,
              }
            )}
          >
            {item.label}
            <div
              className={classNames(
                'min-w-3 min-h-3 rounded-full bg-transparent',
                {
                  '!bg-[#FFC41F]': item.pending,
                  '!bg-success': item.completed,
                }
              )}
            ></div>
          </div>
        );

        return id ? (
          <Link key={item.path} href={linkBase + item.path}>
            {NavItem}
          </Link>
        ) : (
          <div key={item.path}>{NavItem}</div>
        );
      })}
    </div>
  );
};

export default memo(LeaseSidebarNav);
