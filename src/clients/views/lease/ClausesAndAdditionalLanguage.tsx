"use client";
import { Lease } from "@/types/lease";
import Checkbox from "@/clients/ui/checkbox";
import Textarea from "@/clients/ui/textarea";

type Props = {
  lease: Lease;
};

const ClausesAndAdditionalLanguage = ({ lease }: Props) => {
    return (<div className="w-full md:border p-2 md:p-4 rounded-lg h-auto">
      <p className="text-sm font-bold flex gap-4 items-center">
        <span>
          {/*<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.2001 9.99998L9.06676 11.8666L12.8001 8.13331M18.4001 9.99998C18.4001 14.6392 14.6393 18.4 10.0001 18.4C5.36091 18.4 1.6001 14.6392 1.6001 9.99998C1.6001 5.36078 5.36091 1.59998 10.0001 1.59998C14.6393 1.59998 18.4001 5.36078 18.4001 9.99998Z" stroke="#34A853" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>*/}
        </span>
        Clauses and Additional Language
      </p>
      <div className="py-2 flex items-center gap-2 w-full">
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">No Smoking</p>
        <div className="w-[30%] text-xs flex items-center gap-2"><Checkbox /></div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Pets Allowed</p>
        <div className="w-[30%] text-xs flex items-center gap-2"><Checkbox /></div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Pool aiver Required</p>
        <div className="w-[30%] text-xs flex items-center gap-2"><Checkbox /></div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Show Clause</p>
        <div className="w-[30%] text-xs flex items-center gap-2"><Checkbox /></div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Roof Walker Waiver Required</p>
        <div className="w-[30%] text-xs flex items-center gap-2"><Checkbox /></div>
      </div>
      <p className="text-sm font-bold flex gap-4 items-center">
        Signature Page Additions
      </p>
      <small className="text-xs italic">*This text will be printed on the signature page.</small>
      <div className="py-2 w-full">
        <Textarea className="input input-bordered min-h-min rounded focus:outline-none text-xs p-2 w-full h-[100px]" />
      </div>
    </div>);
  };
  
  export default ClausesAndAdditionalLanguage;
  