'use client';

import { cancelLease } from '@/app/actions/lease';
import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import Modal from '@/clients/ui/modal';
import { Nullable } from '@/types/common';
import { Lease } from '@/types/lease';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type Props = {
  lease: Lease;
  onToggle: () => void;
};

enum CheckboxOptions {
  SEND_NOTIFICATION = 'SEND_NOTIFICATION',
  DO_NOT_SEND_NOTIFICATION = 'DO_NOT_SEND_NOTIFICATION',
  RECREATE_LEASE = 'RECREATE_LEASE',
}

const CancelLeaseDialog = ({ onToggle, lease }: Props) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] =
    useState<Nullable<CheckboxOptions>>(null);

  const onToggleCheckobox = useCallback((event: any) => {
    const { name, checked } = event.target;
    setSelectedOption(checked ? name : null);
  }, []);

  const onCancelConfirm = useCallback(() => {
    setIsSubmitting(true);
    cancelLease(
      lease.lease_id,
      selectedOption && selectedOption !== CheckboxOptions.RECREATE_LEASE
        ? {
            is_send_email:
              selectedOption === CheckboxOptions.SEND_NOTIFICATION
                ? true
                : false,
          }
        : undefined
    )
      .then((data) => {
        console.log(data);
        setIsSubmitting(false);
        onToggle();
        toast.success('Lease cancelled successfully');
        if (selectedOption === CheckboxOptions.RECREATE_LEASE) {
          router.push('/lease');
        } else {
          window.location.href = `${BASE_URL}/leases`;
        }
      })
      .catch((err) => {
        console.log(err);
        setIsSubmitting(false);
        toast.error('Failed to cancel lease');
      });
  }, [lease.lease_id, onToggle, router, selectedOption]);

  return (
    <Modal open className="p-4">
      <XMarkIcon
        className="w-8 h-8 absolute -top-3 -right-3 bg-white rounded-full shadow-sm cursor-pointer p-2 z-[999]"
        onClick={onToggle}
      />
      <p>Are you sure you want to cancel?</p>
      <hr className="mt-2 my-4" />
      <div className="flex items-center gap-x-2 my-4">
        <Checkbox
          name={CheckboxOptions.SEND_NOTIFICATION}
          checked={selectedOption === CheckboxOptions.SEND_NOTIFICATION}
          onChange={onToggleCheckobox}
        />
        <label htmlFor="noSmoking" className="text-xs md:text-sm">
          Cancel lease and send notification to Tenant and Owner
        </label>
      </div>
      <div className="flex items-center gap-x-2 my-4">
        <Checkbox
          name={CheckboxOptions.DO_NOT_SEND_NOTIFICATION}
          checked={selectedOption === CheckboxOptions.DO_NOT_SEND_NOTIFICATION}
          onChange={onToggleCheckobox}
        />
        <label htmlFor="noSmoking" className="text-xs md:text-sm">
          Cancel lease but DO NOT send notification emails
        </label>
      </div>
      <div className="flex items-center gap-x-2 my-4">
        <Checkbox
          name={CheckboxOptions.RECREATE_LEASE}
          checked={selectedOption === CheckboxOptions.RECREATE_LEASE}
          onChange={onToggleCheckobox}
        />
        <label htmlFor="noSmoking" className="text-xs md:text-sm">
          Cancel and Re-create Lease
        </label>
      </div>
      <div className="flex items-center justify-between mt-6">
        <Button
          intent="secondary"
          className="rounded-lg text-sm font-medium w-[45%]"
          onClick={onToggle}
        >
          Close
        </Button>
        <Button
          className="rounded-lg text-sm font-medium w-[45%] bg-error hover:bg-error/70"
          onClick={onCancelConfirm}
          disabled={isSubmitting}
          isLoading={isSubmitting}
        >
          Cancel Lease
        </Button>
      </div>
    </Modal>
  );
};

export default CancelLeaseDialog;
