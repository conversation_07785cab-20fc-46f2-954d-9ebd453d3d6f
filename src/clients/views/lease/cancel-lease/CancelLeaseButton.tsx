'use client';

import Button from '@/clients/ui/button';
import { Lease } from '@/types/lease';
import dynamic from 'next/dynamic';
import { useCallback, useState } from 'react';

const CancelLeaseDialog = dynamic(() => import('./CancelLeaseDialog'));

type Props = {
  lease: Lease;
};

const CancelLeaseButton = ({ lease }: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowModal((prev) => !prev);
  }, []);

  return (
    <>
      <Button
        intent="outline"
        className="font-medium text-sm text-error hover:text-error/50 rounded-full h-[32px]"
        onClick={onToggle}
      >
        Cancel Lease
      </Button>
      {showModal && <CancelLeaseDialog lease={lease} onToggle={onToggle} />}
    </>
  );
};

export default CancelLeaseButton;
