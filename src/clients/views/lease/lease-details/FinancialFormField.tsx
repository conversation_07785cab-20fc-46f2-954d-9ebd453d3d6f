'use client';

import FormHelperText from '@/app/ui/form-helper-text';
import CurrencyInput from '@/clients/ui/currency-input';
import classNames from 'classnames';
import { memo } from 'react';

type Props = {
  label: string;
  name: string;
  value: any;
  error?: string;
  onChangeNumeric: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  additionalProps?: any;
};

const FinancialFormField = ({
  label,
  name,
  value,
  error,
  onChangeNumeric,
  disabled,
  additionalProps,
}: Props) => {
  return (
    <div className="flex items-center justify-between my-2">
      <div className="relative min-w-[240px]">
        <label className="text-xs md:text-sm">{label}</label>
        {error && (
          <FormHelperText className="text-[10px] absolute top-4 left-1" error>
            {error}
          </FormHelperText>
        )}
      </div>
      <CurrencyInput
        name={name}
        wrapperclassName={classNames(
          'w-32 flex items-center border rounded-[4px] p-2',
          {
            'border-[#F37391]': error,
            'hover:border-black': !disabled,
          }
        )}
        className="text-xs md:text-sm !p-0 border-none w-full"
        onChange={onChangeNumeric}
        value={value}
        error={!!error}
        disabled={disabled}
        {...additionalProps}
      />
    </div>
  );
};

export default memo(FinancialFormField);
