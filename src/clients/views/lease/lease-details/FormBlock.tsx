'use client';

import classNames from 'classnames';
import { memo } from 'react';

type FormBlockProps = {
  title: string;
  children?: React.ReactNode;
  status?: 'error' | 'success' | 'none';
};

const FormBlock = ({ title, children, status }: FormBlockProps) => (
  <div
    className={classNames(
      'p-4 border border-solid border-outline rounded-md w-full md:text-lg',
      {
        '!border-error': status === 'error',
      }
    )}
  >
    <div className="flex gap-x-4 items-center my-2.5 md:my-4">
      <h3 className="text-md font-bold">{title}</h3>
    </div>
    {children}
  </div>
);

export default memo(FormBlock);
