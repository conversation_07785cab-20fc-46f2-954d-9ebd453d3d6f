'use client';

import classNames from 'classnames';
import { FormEvent, memo, useCallback } from 'react';
import { Payment, PaymentScheduleValues } from './PaymentSchedule';
import FormHelperText from '@/app/ui/form-helper-text';
import CurrencyInput from '@/clients/ui/currency-input';
import { currencyFormatter, parseDateString } from '@/utils/common';
import DatePicker from '@/clients/components/common/DatePicker';
import { format } from 'date-fns';

type Props = {
  values: PaymentScheduleValues;
  payment: Payment;
  index: number;
  errors: any;
  setFieldValue: (field: string, value: any) => void;
  validateField: (field: string) => void;
  disabled?: boolean;
  isFirstPayment?: boolean;
  isLastPayment?: boolean;
  disableInputs?: boolean;
  rent: number;
};

const PaymentScheduleItem = ({
  payment,
  index,
  setFieldValue,
  validateField,
  errors,
  disabled,
  isFirstPayment,
  isLastPayment,
  disableInputs,
  values,
  rent,
}: Props) => {
  const onChangeDate = useCallback(
    (dt?: Date) => {
      setFieldValue(
        `paymentSchedule.payments.${index}.due_date`,
        dt ? format(dt, 'yyyy-MM-dd') : ''
      );
      setTimeout(
        () => validateField(`paymentSchedule.payments.${index}.due_date`),
        10
      );
    },
    [index, setFieldValue, validateField]
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>, index: number) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }

      const numberOfPayments = values.payments.length;

      setFieldValue(name, Number(number));
      setTimeout(() => validateField(name), 10);

      for (let i = index + 1; i < numberOfPayments; i++) {
        const rentFromEarlierPayments = values.payments
          .slice(0, index)
          .reduce((sum, payment) => sum + payment.rent, 0);
        setFieldValue(
          `paymentSchedule.payments.${i}.rent`,
          (rent - Number(number) - rentFromEarlierPayments) /
            (numberOfPayments - index - 1)
        );
        setTimeout(
          () => validateField(`paymentSchedule.payments.${i}.rent`),
          10
        );
      }
    },
    [rent, setFieldValue, validateField, values.payments]
  );

  return (
    <div>
      <h3 className="font-bold">Payment {index + 1}</h3>
      <div className="flex justify-between items-center relative">
        <label className="text-xs md:text-base">Payment Due</label>
        <DatePicker
          className={classNames(
            'w-44 !p-2 rounded-md text-right !pr-8 text-xs md:text-sm'
          )}
          popOverClassName="p-0 w-full"
          selected={
            payment?.due_date ? parseDateString(payment.due_date) : undefined
          }
          minDate={
            index > 0
              ? parseDateString(values.payments[index - 1].due_date)
              : undefined
          }
          maxDate={
            index === values.payments.length - 1
              ? undefined
              : parseDateString(values.payments[index + 1].due_date)
          }
          onSelect={onChangeDate}
          disabled={disabled}
        />
        {!!errors?.payments?.[index]?.due_date && (
          <FormHelperText className="text-[10px] absolute top-6 left-1" error>
            {errors?.payments?.[index]?.due_date}
          </FormHelperText>
        )}
      </div>

      <div className="flex items-center justify-between my-2">
        <div className="relative min-w-[50%] md:min-w-[240px]">
          <label className="text-xs md:text-sm">Rent</label>
          {errors?.payments?.[index]?.rent && (
            <FormHelperText className="text-[10px] absolute top-4 left-1" error>
              {errors?.payments?.[index]?.rent}
            </FormHelperText>
          )}
        </div>

        <CurrencyInput
          name={`paymentSchedule.payments.${index}.rent`}
          wrapperclassName={classNames(
            'md:w-44 flex items-center border rounded-[4px] p-2 hover:border-black bg-white',
            {
              'border-[#F37391]': errors?.payments?.[index]?.rent,
              'hover:border-transparent': disableInputs || disabled,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full text-right pl-2"
          iconClassName="font-normal pr-2"
          onChange={(e) => onChangeNumeric(e, index)}
          value={payment.rent}
          error={!!errors?.payments?.[index]?.rent}
          disabled={disabled || isLastPayment || disableInputs}
        />
      </div>

      {isFirstPayment && (
        <div className="flex items-center justify-between my-2">
          <label className="min-w-[50%] md:min-w-[240px] text-xs md:text-sm">
            Booking Fee
          </label>
          <p className="w-44 p-2 text-xs md:text-sm text-right">
            {currencyFormatter.format(Number(payment?.processing_fee ?? 0))}
          </p>
        </div>
      )}

      {isLastPayment && (
        <div className="flex items-center justify-between my-2">
          <label className="min-w-[50%] md:min-w-[240px] text-xs md:text-sm">
            Other Fees
          </label>
          <p className="w-44 p-2 text-xs md:text-sm text-right">
            {currencyFormatter.format(Number(payment?.other_fee ?? 0))}
          </p>
        </div>
      )}
      {isFirstPayment && (
        <div className="flex items-center justify-between my-2">
          <label className="min-w-[50%] md:min-w-[240px] text-xs md:text-sm">
            Occupancy Tax
          </label>
          <p className="w-44 p-2 text-xs md:text-sm text-right">
            {currencyFormatter.format(Number(payment?.occupancy_tax ?? 0))}
          </p>
        </div>
      )}
      {isLastPayment && (
        <div className="flex items-center justify-between my-2">
          <label className="min-w-[50%] md:min-w-[240px] text-xs md:text-sm">
            Security Deposit
          </label>
          <p className="w-44 p-2 text-xs md:text-sm text-right">
            {currencyFormatter.format(Number(payment?.security_deposit ?? 0))}
          </p>
        </div>
      )}
    </div>
  );
};

export default memo(PaymentScheduleItem);
