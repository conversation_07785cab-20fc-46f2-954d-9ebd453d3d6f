'use client';

import { Form, FormikProps } from 'formik';

import Button from '@/clients/ui/button';
import FinancialForm from './FinancialForm';
import ClausesForm from './Clauses';
import PaymentSchedule from './PaymentSchedule';
import FormBlock from './FormBlock';
import { Lease } from '@/types/lease';
import { useEffect } from 'react';
import LeaseInfoForm from './LeaseInfoForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { useLease } from '@/clients/contexts/LeaseContext';
import { parseStringPrice } from '@/utils/common';
import { UserProfile } from '@/types/profile';
import { LeaseDetailsFormValues } from './FormikWrapper';
// import useJsonDiffEffect from '@/hooks/useJsonDiffEffect';
import CancelLeaseButton from '../cancel-lease/CancelLeaseButton';
import UnblockDatesButton from '../unblock-dates/UnblockDatesButton';

type Props = {
  userData: UserProfile;
  leaseId?: number;
  lease?: Lease;
  progressStatus?: Nullable<ProgressStatus>;
  isLegacyBookingRule: boolean;
  setIsLegacyBookingRule: (value: boolean) => void;
  isAdmin?: boolean;
} & FormikProps<LeaseDetailsFormValues>;

const getBlockStatus = (touched: any, error: any) => {
  if (!touched) {
    return 'none';
  }

  return error ? 'error' : 'success';
};

const LeaseDetailsFormWrapper = ({
  lease,
  values,
  errors,
  setFieldValue,
  touched,
  validateField,
  isSubmitting,
  progressStatus,
  validateForm,
  userData,
  isLegacyBookingRule,
  setIsLegacyBookingRule,
  isAdmin,
}: Props) => {
  const { listingDetails, rentInfo, listingId } = useLease();

  // useJsonDiffEffect((diff) => {
  //   console.debug('Changed keys:', diff);
  // }, values.financialInfo);

  // useJsonDiffEffect((diff) => {
  //   console.debug('Payment schedule changed', diff);
  // }, values.paymentSchedule);

  useEffect(() => {
    if (isSubmitting && Object.keys(errors).length > 0) {
      // Scroll to top of form when there are validation errors on submit
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [isSubmitting, errors]);

  useEffect(() => {
    if (lease) {
      validateForm();
    }
  }, [lease, validateForm]);

  return (
    <Form className="w-full flex flex-col md:pb-8 pt-[76px] md:pt-0">
      <LeaseInfoForm
        listingId={listingId}
        values={values.leaseInfo}
        errors={errors?.leaseInfo}
        setFieldValue={setFieldValue}
        touched={touched?.leaseInfo}
        validateField={validateField}
        lease={lease}
        listingDetails={listingDetails}
        userData={userData}
        validateForm={validateForm}
        financialInfo={values.financialInfo}
        isLegacyBookingRule={isLegacyBookingRule}
        charge_community_impact_fee={
          listingDetails?.requirement?.charge_community_impact_fee
        }
        min_security_deposit={listingDetails?.requirement?.min_security_deposit}
      />
      {lease && (
        <div className="flex items-center justify-end gap-x-4 mt-4">
          {isAdmin && <UnblockDatesButton lease={lease} />}
          {lease.status !== 'Cancelled' && lease.status !== 'Paid in Full' && (
            <CancelLeaseButton lease={lease} />
          )}
        </div>
      )}
      <div className="flex-col gap-y-8 md:flex-row">
        <div className="p-4 flex flex-col gap-y-4 md:flex-row gap-x-4 md:px-0">
          <FormBlock
            title="Financial Info"
            status={getBlockStatus(
              touched?.financialInfo,
              Object.values(errors?.financialInfo ?? {}).length > 0
            )}
          >
            <FinancialForm
              values={values.financialInfo}
              errors={errors.financialInfo}
              setFieldValue={setFieldValue}
              touched={touched?.financialInfo}
              validateField={validateField}
              lease={lease}
              charge_community_impact_fee={
                listingDetails?.requirement?.charge_community_impact_fee
              }
              min_security_deposit={
                listingDetails?.requirement?.min_security_deposit
              }
              arrival_date={values.leaseInfo.dates?.from}
              departure_date={values.leaseInfo.dates?.to}
              paymentScheduleValues={values.paymentSchedule}
              isLegacyBookingRule={isLegacyBookingRule}
              setIsLegacyBookingRule={setIsLegacyBookingRule}
            />
          </FormBlock>
          <FormBlock
            title="Clauses and Additional Language"
            status={getBlockStatus(
              touched?.clauses,
              Object.values(errors?.clauses ?? {}).length > 0
            )}
          >
            <ClausesForm
              values={values.clauses}
              errors={errors.clauses}
              setFieldValue={setFieldValue}
              validateField={validateField}
              lease={lease}
            />
          </FormBlock>
        </div>
        <div className="p-4 flex flex-col gap-y-4 md:flex-row gap-x-4 md:px-0 mt-4">
          <FormBlock
            title="Payment Schedule"
            status={getBlockStatus(
              touched?.paymentSchedule,
              Object.values(errors?.paymentSchedule ?? {}).length > 0
            )}
          >
            <PaymentSchedule
              values={values.paymentSchedule}
              errors={errors.paymentSchedule}
              setFieldValue={setFieldValue}
              validateField={validateField}
              rent={
                (lease ? parseStringPrice(lease?.rent) : rentInfo?.rent) ?? 0
              }
              financialInfo={values.financialInfo}
              dates={values.leaseInfo.dates}
              lease={lease}
            />
          </FormBlock>
        </div>
      </div>
      <div className="w-full flex justify-end px-4 pb-4">
        <Button
          className="text-xs md:text-base w-40"
          isLoading={progressStatus === ProgressStatus.LOADING}
          disabled={
            progressStatus === ProgressStatus.LOADING ||
            (lease && lease.status !== 'Draft')
          }
          isSubmit
        >
          Save
        </Button>
      </div>
    </Form>
  );
};

export default LeaseDetailsFormWrapper;
