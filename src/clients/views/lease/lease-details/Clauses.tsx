import React, { memo, useCallback } from 'react';

import Textarea from '@/clients/ui/textarea';
import Checkbox from '@/clients/ui/checkbox';
import { Lease } from '@/types/lease';

const NO_SMOKING_COMMENT =
  'No Smoking - No Smoking is allowed on the premises.';
const PETS_ALLOWED_COMMENT =
  'Pets Allowed - Notwithstanding the foregoing, Landlord agrees to allow tenant to have one dog on the property during tenancy with the understanding that tenant shall be solely responsible for any dog-related damage at the end of lease term. Amount of dog related damage shall not be limited to the amount of the security deposit. Tenant agrees to abide by the Nantucket Leash and Pick Up Laws.';
const POOL_WAIVER_REQUIRED_COMMENT =
  'Pool Waiver- Tenant is required to sign the attached Pool/Spa waiver.';
const SHOW_CLAUSE_COMMENT =
  'Show Clause- Tenant agrees to allow property to be shown to prospective buyers with 24 hour notice from listing broker.';
const ROOF_WALK_WAIVER_REQUIRED_COMMENT =
  'Roofwalk Waiver- Tenant is required to sign the attached Roofwalk Waiver.';

export type ClausesFormValues = {
  [key in ClauseBooleanKeys]: boolean;
} & {
  comment: string;
};

interface ClausesFormProps {
  values: ClausesFormValues;
  errors: any;
  setFieldValue: (field: string, value: any) => void;
  validateField: (field: string) => void;
  lease?: Lease;
}

export enum ClauseBooleanKeys {
  NO_SMOKING = 'noSmoking',
  PETS_OK = 'petsOk',
  POOL_WAIVER_REQUIRED = 'poolWaiverRequired',
  SHOW_CLAUSE = 'showClause',
  ROOF_WALK_WAIVER_REQUIRED = 'roofWalkWaiverRequired',
}

export enum ClauseNames {
  NO_SMOKING = 'No Smoking',
  PETS_OK = 'Pets OK',
  POOL_WAIVER_REQUIRED = 'Pool Waiver Required',
  SHOW_CLAUSE = 'Show Clause',
  ROOF_WALK_WAIVER_REQUIRED = 'Roof Walk Waiver Required',
}

const ClausesForm: React.FC<ClausesFormProps> = ({
  values,
  errors,
  setFieldValue,
  validateField,
  lease,
}) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      setFieldValue(`clauses.${name}`, checked);
      setTimeout(() => validateField(`clauses.${name}`), 10);

      let comment = '';
      comment +=
        (name === ClauseBooleanKeys.NO_SMOKING && checked) ||
        (values.noSmoking && name !== ClauseBooleanKeys.NO_SMOKING)
          ? NO_SMOKING_COMMENT + '\r\n\r\n'
          : '';

      comment +=
        (name === ClauseBooleanKeys.PETS_OK && checked) ||
        (values.petsOk && name !== ClauseBooleanKeys.PETS_OK)
          ? PETS_ALLOWED_COMMENT + '\r\n\r\n'
          : '';

      comment +=
        (name === ClauseBooleanKeys.POOL_WAIVER_REQUIRED && checked) ||
        (values.poolWaiverRequired &&
          name !== ClauseBooleanKeys.POOL_WAIVER_REQUIRED)
          ? POOL_WAIVER_REQUIRED_COMMENT + '\r\n\r\n'
          : '';

      comment +=
        (name === ClauseBooleanKeys.SHOW_CLAUSE && checked) ||
        (values.showClause && name !== ClauseBooleanKeys.SHOW_CLAUSE)
          ? SHOW_CLAUSE_COMMENT + '\r\n\r\n'
          : '';

      comment +=
        (name === ClauseBooleanKeys.ROOF_WALK_WAIVER_REQUIRED && checked) ||
        (values.roofWalkWaiverRequired &&
          name !== ClauseBooleanKeys.ROOF_WALK_WAIVER_REQUIRED)
          ? ROOF_WALK_WAIVER_REQUIRED_COMMENT + '\r\n\r\n'
          : '';

      setFieldValue('clauses.comment', comment);
    },
    [
      setFieldValue,
      validateField,
      values.noSmoking,
      values.petsOk,
      values.poolWaiverRequired,
      values.roofWalkWaiverRequired,
      values.showClause,
    ]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      setFieldValue(name, value);
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  return (
    <div className="flex flex-col gap-y-2.5">
      <div className="flex flex-col gap-y-2.5">
        <div className="flex justify-between items-center md:w-5/6">
          <label
            htmlFor={ClauseBooleanKeys.NO_SMOKING}
            className="text-xs md:text-sm"
          >
            No Smoking
          </label>
          <Checkbox
            id={ClauseBooleanKeys.NO_SMOKING}
            name={ClauseBooleanKeys.NO_SMOKING}
            checked={values.noSmoking}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label
            htmlFor={ClauseBooleanKeys.PETS_OK}
            className="text-xs md:text-sm"
          >
            Pets Allowed
          </label>
          <Checkbox
            id={ClauseBooleanKeys.PETS_OK}
            name={ClauseBooleanKeys.PETS_OK}
            checked={values.petsOk}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label
            htmlFor={ClauseBooleanKeys.POOL_WAIVER_REQUIRED}
            className="text-xs md:text-sm"
          >
            Pool Waiver Required
          </label>
          <Checkbox
            id={ClauseBooleanKeys.POOL_WAIVER_REQUIRED}
            name={ClauseBooleanKeys.POOL_WAIVER_REQUIRED}
            checked={values.poolWaiverRequired}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label
            htmlFor={ClauseBooleanKeys.SHOW_CLAUSE}
            className="text-xs md:text-sm"
          >
            Show Clause
          </label>
          <Checkbox
            id={ClauseBooleanKeys.SHOW_CLAUSE}
            name={ClauseBooleanKeys.SHOW_CLAUSE}
            checked={values.showClause}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label
            htmlFor={ClauseBooleanKeys.ROOF_WALK_WAIVER_REQUIRED}
            className="text-xs md:text-sm"
          >
            Roof Walker Waiver Required
          </label>
          <Checkbox
            id={ClauseBooleanKeys.ROOF_WALK_WAIVER_REQUIRED}
            name={ClauseBooleanKeys.ROOF_WALK_WAIVER_REQUIRED}
            checked={values.roofWalkWaiverRequired}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
      </div>

      <div className="md:mt-8">
        <div>
          <label htmlFor="comment" className="text-xs md:text-sm font-bold">
            Signature Page Additions
          </label>
          <p className="text-[10px] italic">
            *This text will be printed on the signature page.
          </p>
        </div>
        <Textarea
          id="comment"
          name="clauses.comment"
          value={values.comment}
          onChange={onChangeTextInput}
          className="w-full h-24 text-[10px] text-xs"
          placeholder="Enter text here"
          disabled={lease && lease.status !== 'Draft'}
        />
      </div>
    </div>
  );
};

export default memo(ClausesForm);
