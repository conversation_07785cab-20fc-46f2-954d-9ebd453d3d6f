'use client';

import { FieldArray } from 'formik';
import { FinancialFormValues } from './FinancialForm';
import Input from '@/clients/ui/input';
import Checkbox from '@/clients/ui/checkbox';
import CurrencyInput from '@/clients/ui/currency-input';
import FormHelperText from '@/app/ui/form-helper-text';
import { FormEvent, useCallback } from 'react';
import { TrashIcon } from '@heroicons/react/24/outline';

type Props = {
  values: FinancialFormValues;
  onChangeTextInput: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onOtherFeesChanged: (other_fees: any[]) => void;
  errors?: any;
  editAllowed: boolean;
};

const FinancialFormOtherFees = ({
  values,
  onChangeTextInput,
  onOtherFeesChanged,
  errors,
  editAllowed,
}: Props) => {
  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) return;

      const index = Number(name.split('.')[2]);
      const otherFees = [...values.otherFees];
      otherFees[index].amount = Number(number);
      onOtherFeesChanged(otherFees as any);
    },
    [onOtherFeesChanged, values.otherFees]
  );

  const onToggleCheckbox = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = event.target;

      const index = Number(name.split('.')[2]);
      const otherFees = [...values.otherFees];
      otherFees[index].taxable = checked;
      onOtherFeesChanged(otherFees as any);
    },
    [onOtherFeesChanged, values.otherFees]
  );

  const onRemoveOtherFee = useCallback(
    (index: number) => {
      const otherFees = [...values.otherFees];
      otherFees.splice(index, 1);
      onOtherFeesChanged(otherFees as any);
    },
    [onOtherFeesChanged, values.otherFees]
  );

  return (
    <FieldArray name="financialInfo.otherFees">
      {({ push }) => (
        <>
          <div>
            <label className="text-xs md:text-sm">Other Fees</label>
          </div>
          {values.otherFees.map((fee, index) => (
            <div key={index} className="my-2">
              <div className="w-full flex items-center justify-between pl-4 relative">
                <div className="flex items-center justify-between gap-x-4">
                  <Input
                    name={`financialInfo.otherFees.${index}.reason`}
                    className="w-32 text-xs md:text-sm !p-2"
                    onChange={onChangeTextInput}
                    value={values.otherFees[index]?.reason ?? ''}
                    error={!!(errors?.otherFees as any)?.[index]?.reason}
                    disabled={!editAllowed}
                  />
                  <div className="flex items-center gap-x-1">
                    <p className="text-[10px] md:text-xs">Taxable</p>
                    <Checkbox
                      id={`otherFees.${index}.taxable`}
                      name={`financialInfo.otherFees.${index}.taxable`}
                      checked={values.otherFees[index]?.taxable}
                      onChange={onToggleCheckbox}
                      disabled={!editAllowed}
                    />
                    <TrashIcon
                      className=" ml-2 w-5 h-5 text-error cursor-pointer"
                      onClick={() => onRemoveOtherFee(index)}
                    />
                  </div>
                </div>
                <CurrencyInput
                  name={`financialInfo.otherFees.${index}.amount`}
                  wrapperclassName="w-20 flex items-center border rounded-[4px] p-2"
                  className="text-xs md:text-sm !p-0 border-none w-full"
                  onChange={onChangeNumeric}
                  value={values.otherFees[index]?.amount}
                  error={!!(errors?.otherFees as any)?.[index]?.amount}
                  disabled={!editAllowed}
                />
              </div>
              {!!errors?.otherFees?.[index] && (
                <div className="pl-4">
                  {Object.values(errors.otherFees?.[index]).map(
                    (_v: any, _index: number) => (
                      <FormHelperText key={_index} error>
                        {_v}
                      </FormHelperText>
                    )
                  )}
                </div>
              )}
            </div>
          ))}

          {editAllowed && (
            <button
              type="button"
              className="pl-2 py-2 inline-block ml-0 mr-auto text-[10px] md:text-xs"
              onClick={() =>
                push({
                  reason: '',
                  taxable: false,
                  amount: 0,
                  custom: true,
                })
              }
            >
              + Add Other Fee
            </button>
          )}
        </>
      )}
    </FieldArray>
  );
};

export default FinancialFormOtherFees;
