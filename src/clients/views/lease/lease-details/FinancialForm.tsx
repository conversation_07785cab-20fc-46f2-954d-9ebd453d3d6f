'use client';

import React, { FormEvent, memo, useCallback, useMemo } from 'react';
import { FormikErrors, FormikTouched } from 'formik';
import Input from '@/clients/ui/input';
import Checkbox from '@/clients/ui/checkbox';
import classNames from 'classnames';
import FormHelperText from '@/app/ui/form-helper-text';
import CurrencyInput from '@/clients/ui/currency-input';
import { Lease } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';
import {
  calculateOccupancyTax,
  calculateGrandTotalFinancialInfo,
  updateLeasePaymentScheduleInfo,
} from '@/utils/lease';
import { ensureNumberHas2Decimals } from '@/utils/common';
import { isValid } from 'date-fns';
import { Nullable } from '@/types/common';
import { PaymentScheduleValues } from './PaymentSchedule';
import FinancialFormField from './FinancialFormField';
import FinancialFormOtherFees from './FinancialFormOtherFees';

type FinancialFormProps = {
  values: FinancialFormValues;
  paymentScheduleValues: PaymentScheduleValues;
  setFieldValue: (field: string, value: any) => void;
  errors?: FormikErrors<FinancialFormValues>;
  touched?: FormikTouched<FinancialFormValues>;
  validateField: (field: string) => void;
  lease?: Lease;
  charge_community_impact_fee?: boolean;
  min_security_deposit?: Nullable<number>;
  arrival_date?: Date;
  departure_date?: Date;
  isLegacyBookingRule: boolean;
  setIsLegacyBookingRule: (value: boolean) => void;
};

interface OccupancyTax {
  amount: number;
  exempt: boolean;
}

export interface FinancialFormValues {
  commission?: number;
  rent: number;
  processingFee: number;
  otherFees: {
    amount: number;
    reason: string;
    taxable: boolean;
    custom?: boolean;
  }[];
  occupancyTax: OccupancyTax;
  securityDeposit: number;
  returning_tenant_info: string;
}

const FinancialForm: React.FC<FinancialFormProps> = ({
  values,
  errors,
  setFieldValue,
  validateField,
  lease,
  charge_community_impact_fee,
  min_security_deposit,
  arrival_date,
  departure_date,
  paymentScheduleValues,
  isLegacyBookingRule,
  setIsLegacyBookingRule,
}) => {
  const editAllowed = useMemo(
    () => (lease && lease?.status === 'Draft') || !lease,
    [lease]
  );

  const areDatesValid = useMemo(
    () =>
      arrival_date &&
      departure_date &&
      isValid(arrival_date) &&
      isValid(departure_date),
    [arrival_date, departure_date]
  );

  const updatePaymentSchedule = useCallback(
    (updatedValues: Partial<FinancialFormValues>, rentChanged?: boolean) => {
      if (!areDatesValid) return;

      const newPayments = updateLeasePaymentScheduleInfo(
        paymentScheduleValues.payments,
        { ...values, ...updatedValues },
        rentChanged
      );
      setFieldValue('paymentSchedule.payments', newPayments);
    },
    [areDatesValid, paymentScheduleValues.payments, values, setFieldValue]
  );

  const validateFields = useCallback(
    (fieldNames: string[]) => {
      setTimeout(() => {
        fieldNames.forEach((field) => validateField(field));
        if (areDatesValid) {
          validateField('paymentSchedule.payments');
        }
      }, 50);
    },
    [validateField, areDatesValid]
  );

  const onRentChanged = useCallback(
    (rentAmount: number) => {
      const processing_fee = isLegacyBookingRule ? 100 : rentAmount * 0.1;
      const security_deposit = isLegacyBookingRule
        ? (min_security_deposit ?? 0.1) * rentAmount
        : 0;
      const occupancy_tax = calculateOccupancyTax(
        rentAmount,
        processing_fee,
        values.otherFees as any,
        charge_community_impact_fee
      );

      const updatedValues = {
        rent: rentAmount,
        processingFee: processing_fee,
        securityDeposit: security_deposit,
        occupancyTax: {
          amount: occupancy_tax,
          exempt: values.occupancyTax.exempt,
        },
      };

      // Update form fields
      setFieldValue('financialInfo.rent', ensureNumberHas2Decimals(rentAmount));
      setFieldValue(
        'financialInfo.processingFee',
        ensureNumberHas2Decimals(processing_fee)
      );
      setFieldValue(
        'financialInfo.securityDeposit',
        ensureNumberHas2Decimals(security_deposit)
      );
      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(occupancy_tax)
      );

      updatePaymentSchedule(updatedValues, true);
      validateFields([
        'financialInfo.rent',
        'financialInfo.securityDeposit',
        'financialInfo.processingFee',
        'financialInfo.occupancyTax.amount',
      ]);
    },
    [
      isLegacyBookingRule,
      min_security_deposit,
      values,
      charge_community_impact_fee,
      setFieldValue,
      updatePaymentSchedule,
      validateFields,
    ]
  );

  const onProcessingFeeChanged = useCallback(
    (processing_fee: number) => {
      const occupancy_tax = calculateOccupancyTax(
        values.rent,
        processing_fee,
        values.otherFees as any,
        charge_community_impact_fee
      );

      const updatedValues = {
        processingFee: processing_fee,
        occupancyTax: {
          amount: occupancy_tax,
          exempt: values.occupancyTax.exempt,
        },
      };

      setFieldValue(
        'financialInfo.processingFee',
        ensureNumberHas2Decimals(processing_fee)
      );
      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(occupancy_tax)
      );

      updatePaymentSchedule(updatedValues);
      validateFields([
        'financialInfo.processingFee',
        'financialInfo.occupancyTax.amount',
      ]);
    },
    [
      charge_community_impact_fee,
      values,
      setFieldValue,
      updatePaymentSchedule,
      validateFields,
    ]
  );

  const onOccupancyTaxChanged = useCallback(
    (occupancy_tax: number, exempt?: boolean) => {
      console.log('onOccupancyTaxChanged', { occupancy_tax });
      const updatedValues = {
        occupancyTax: {
          amount: occupancy_tax,
          exempt: exempt ?? values.occupancyTax.exempt,
        },
      };

      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(occupancy_tax)
      );
      updatePaymentSchedule(updatedValues);
      validateFields(['financialInfo.occupancyTax.amount']);
    },
    [
      values.occupancyTax.exempt,
      setFieldValue,
      updatePaymentSchedule,
      validateFields,
    ]
  );

  const onSecurityDepositChanged = useCallback(
    (securityDeposit: number) => {
      const updatedValues = { securityDeposit };

      setFieldValue(
        'financialInfo.securityDeposit',
        ensureNumberHas2Decimals(securityDeposit)
      );
      updatePaymentSchedule(updatedValues);
      validateFields(['financialInfo.securityDeposit']);
    },
    [setFieldValue, updatePaymentSchedule, validateFields]
  );

  const onOtherFeesChanged = useCallback(
    (otherFees: any[]) => {
      const occupancy_tax = calculateOccupancyTax(
        values.rent,
        values.processingFee,
        otherFees as any,
        charge_community_impact_fee
      );

      const updatedValues = {
        occupancyTax: {
          amount: occupancy_tax,
          exempt: values.occupancyTax.exempt,
        },
        otherFees,
      };

      setFieldValue('financialInfo.otherFees', otherFees);
      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(occupancy_tax)
      );
      updatePaymentSchedule(updatedValues);
      validateFields([
        'financialInfo.otherFees',
        'financialInfo.occupancyTax.amount',
      ]);
    },
    [
      charge_community_impact_fee,
      setFieldValue,
      updatePaymentSchedule,
      validateFields,
      values.occupancyTax.exempt,
      values.processingFee,
      values.rent,
    ]
  );

  const onReturningTenantInfoChanged = useCallback(
    (returning_tenant_info: string) => {
      const { rent } = values;
      const applyOldRule = returning_tenant_info.length > 0;
      const processing_fee = applyOldRule ? 100 : rent * 0.1;
      const security_deposit = applyOldRule
        ? (min_security_deposit ?? 0.1) * rent
        : 0;

      const occupancy_tax = calculateOccupancyTax(
        rent,
        processing_fee,
        values.otherFees as any,
        charge_community_impact_fee
      );

      const updatedValues = {
        processingFee: processing_fee,
        securityDeposit: security_deposit,
        occupancyTax: {
          amount: occupancy_tax,
          exempt: values.occupancyTax.exempt,
        },
      };

      // Update form fields
      setFieldValue(
        'financialInfo.processingFee',
        ensureNumberHas2Decimals(processing_fee)
      );
      setFieldValue(
        'financialInfo.securityDeposit',
        ensureNumberHas2Decimals(security_deposit)
      );
      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(occupancy_tax)
      );

      updatePaymentSchedule(updatedValues);
      validateFields([
        'financialInfo.securityDeposit',
        'financialInfo.processingFee',
        'financialInfo.occupancyTax.amount',
      ]);
    },
    [
      charge_community_impact_fee,
      min_security_deposit,
      setFieldValue,
      updatePaymentSchedule,
      validateFields,
      values,
    ]
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) return;

      const fieldHandlers = {
        'financialInfo.rent': () => onRentChanged(Number(number)),
        'financialInfo.processingFee': () =>
          onProcessingFeeChanged(Number(number)),
        'financialInfo.occupancyTax.amount': () =>
          onOccupancyTaxChanged(Number(number)),
        'financialInfo.securityDeposit': () =>
          onSecurityDepositChanged(Number(number)),
      };

      const handler = fieldHandlers[name as keyof typeof fieldHandlers];
      if (handler) {
        handler();
      } else {
        setFieldValue(name, Number(number));
        setTimeout(() => validateField(name), 10);
      }
    },
    [
      onRentChanged,
      onProcessingFeeChanged,
      onOccupancyTaxChanged,
      onSecurityDepositChanged,
      setFieldValue,
      validateField,
    ]
  );

  const onChangeReturningTenantInfoText = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = event.target;
      setFieldValue(name, value);
      onReturningTenantInfoChanged(value);
    },
    [onReturningTenantInfoChanged, setFieldValue]
  );

  const onChangeTextInput = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = event.target;
      setFieldValue(name, value);
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  const onToggleTaxExemption = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = event.target;
      setFieldValue(name, checked);
      const occupancy_tax = checked
        ? 0
        : calculateOccupancyTax(
            values.rent,
            values.processingFee,
            values.otherFees as any,
            charge_community_impact_fee
          );

      onOccupancyTaxChanged(occupancy_tax, checked);
    },
    [charge_community_impact_fee, onOccupancyTaxChanged, setFieldValue, values]
  );

  const onToggleLegacyBookingRule = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { checked } = event.target;
      setIsLegacyBookingRule(checked);
    },
    [setIsLegacyBookingRule]
  );

  return (
    <div>
      {/* Legacy Lease Info */}
      <div className="flex items-center gap-x-2">
        <label className="text-[10px] max-w-[70%] leading-normal">
          Use legacy booking fee and security deposit
        </label>
        <Checkbox
          checked={isLegacyBookingRule}
          disabled={!editAllowed || !!lease?.lease_id}
          onChange={onToggleLegacyBookingRule}
        />
      </div>
      {isLegacyBookingRule && (
        <div className="flex items-start justify-between my-2">
          <label className="text-[10px] max-w-[70%] leading-normal">
            If the renter has a lease dated prior to September 1, 2025, enter
            the lease number in the box on the right to apply the legacy booking
            fee and security deposit rules. Otherwise, the standard 10% booking
            fee applies automatically.
          </label>
          <Input
            name="financialInfo.returning_tenant_info"
            className="w-32 text-xs md:text-sm !p-2 text-right hover:border-black"
            onChange={onChangeReturningTenantInfoText}
            value={values?.returning_tenant_info ?? ''}
            disabled={!!lease?.lease_id}
          />
        </div>
      )}

      {/* Commission */}
      <div className="flex items-center justify-between my-2">
        <div className="relative  min-w-[50%] md:min-w-[240px]">
          <label className="text-xs md:text-sm">Commission %</label>
          {errors?.commission && (
            <FormHelperText className="text-[10px] absolute top-4 left-1" error>
              {errors?.commission}
            </FormHelperText>
          )}
        </div>
        <Input
          name="financialInfo.commission"
          className={classNames('w-32 text-xs md:text-sm !p-2 text-right', {
            'border-[#F37391]': errors?.commission,
            'hover:border-black': editAllowed,
          })}
          onChange={onChangeNumeric}
          value={values.commission}
          error={!!errors?.commission}
          disabled={!editAllowed}
        />
      </div>

      {/* Main Financial Fields */}
      <FinancialFormField
        label="Rent"
        name="financialInfo.rent"
        value={values.rent}
        error={errors?.rent}
        onChangeNumeric={onChangeNumeric}
        disabled={!editAllowed}
      />
      <FinancialFormField
        label="Booking Fee"
        name="financialInfo.processingFee"
        value={values.processingFee}
        error={errors?.processingFee}
        onChangeNumeric={onChangeNumeric}
        disabled={!editAllowed}
      />

      {/* Other Fees */}
      <FinancialFormOtherFees
        values={values}
        onChangeTextInput={onChangeTextInput}
        onOtherFeesChanged={onOtherFeesChanged}
        errors={errors}
        editAllowed={editAllowed}
      />

      {/* Occupancy Tax */}
      <div className="flex justify-between items-center my-2">
        <label htmlFor="occupancyTax" className="text-xs md:text-sm">
          Occupancy Tax
        </label>
        <div className="flex gap-x-2 items-center">
          <label htmlFor="exempt" className="text-[10px] md:text-xs">
            Exempt
          </label>
          <Checkbox
            id="exempt"
            name="financialInfo.occupancyTax.exempt"
            checked={values?.occupancyTax?.exempt}
            onChange={onToggleTaxExemption}
            disabled={!editAllowed}
          />
        </div>
        <CurrencyInput
          name="financialInfo.occupancyTax.amount"
          wrapperclassName={classNames(
            'w-32 flex items-center border rounded-[4px] p-2',
            {
              'border-[#F37391]': errors?.occupancyTax?.amount,
              'hover:border-black': editAllowed,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full"
          onChange={onChangeNumeric}
          value={
            values?.occupancyTax?.exempt ? '0.00' : values.occupancyTax.amount
          }
          error={!!errors?.occupancyTax?.amount}
          disabled={!editAllowed || values?.occupancyTax?.exempt}
        />
      </div>

      {/* Security Deposit */}
      <FinancialFormField
        label="Security Deposit"
        name="financialInfo.securityDeposit"
        value={values.securityDeposit}
        error={errors?.securityDeposit}
        onChangeNumeric={onChangeNumeric}
        disabled={!isLegacyBookingRule || !editAllowed}
      />

      <hr className="my-4" />

      {/* Grand Total */}
      <div className="flex justify-between text-sm md:text-base">
        <h3 className="font-bold">Grand Total</h3>
        <p className="w-32 text-black font-bold text-right">
          {currencyFormatter.format(calculateGrandTotalFinancialInfo(values))}
        </p>
      </div>
    </div>
  );
};

export default memo(FinancialForm);
