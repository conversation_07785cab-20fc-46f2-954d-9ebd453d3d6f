'use client';

import { searchContacts } from '@/app/actions/profile';
import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import { Contact } from '@/types/profile';
import { useCallback, useMemo, useState } from 'react';

type Props = {
  disabled?: boolean;
  onSelectTenant: (t?: Contact) => void;
  name?: string;
  value?: string;
};

const SelectTenantInput = ({
  onSelectTenant,
  disabled,
  name,
  value = '',
}: Props) => {
  const [query, setQuery] = useState<string>(value);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [contacts, setContacts] = useState<Contact[]>([]);

  const options = useMemo(
    () =>
      contacts.map((_contact, index) => ({
        id: _contact.contact_id,
        label: (
          <span className="font-sm">
            {_contact.first_name} {_contact.last_name}{' '}
            <span className="font-xs text-disabled">({_contact.email1})</span>
          </span>
        ),
        value: _contact.contact_id,
      })),
    [contacts]
  );

  const onChangeQuery = useCallback((q: string) => {
    setQuery(q);
  }, []);

  const onSelect = useCallback(
    (_t: AutocompleteOption) => {
      const contact = contacts?.find((_c) => _c.contact_id === _t.id);
      onSelectTenant(contact);
      setQuery(`${contact?.first_name ?? ''} ${contact?.last_name ?? ''}`);
    },
    [contacts, onSelectTenant]
  );

  const fetchContacts = useCallback((query = '') => {
    setIsFetching(true);
    searchContacts<{ results: Contact[] }>(query, ['tenant'])
      .then(({ results }) => {
        setContacts(results);
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  return (
    <Autocomplete
      name={name}
      className="!text-xs !p-0 border-none font-medium"
      dropdownClassName="w-max"
      value={query}
      placeholder="Select tenant"
      options={options}
      onChangeValue={onChangeQuery}
      isFetchingData={isFetching}
      fetchData={fetchContacts}
      onSelect={onSelect}
      disabled={disabled}
    />
  );
};

export default SelectTenantInput;
