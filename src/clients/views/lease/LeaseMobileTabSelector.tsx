'use client';

import { useCallback, useMemo, useState } from 'react';
import { usePathname } from 'next/navigation';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import SidebarNavItem from '@/clients/views/rental-listings/SidebarNavItem';

type Props = {
  leaseId?: number;
  altText?: string;
};

const LeaseMobileTabSelector = ({ leaseId, altText }: Props) => {
  const pathname = usePathname();

  const tabName = useMemo(() => {
    const name = pathname?.split('/')[3]?.replace('-', ' ');
    switch (name) {
      case 'agreement':
        return 'Lease Agreement and Signatures';
      case 'summary':
        return 'Financial Summary';
      default:
        return 'Lease General Information';
    }
  }, [pathname]);

  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    if (!leaseId) {
      return;
    }
    setOpen(!open);
  }, [leaseId, open]);

  return (
    <div
      className="collapse collapse-arrow bg-white shadow-dropdown"
      onClick={onToggle}
    >
      <input
        type="radio"
        name="my-accordion-3"
        checked={open}
        onChange={onToggle}
        className="!min-h-[36px]"
      />
      <div className="w-full flex items-center justify-between collapse-title px-4 py-2 !min-h-[36px] after:!hidden">
        <p
          className={classNames('text-sm font-bold capitalize', {
            'opacity-0': open,
          })}
        >
          {altText ?? tabName}
        </p>
        <ChevronDownIcon
          className={classNames('w-auto h-[14px]', {
            'opacity-0': open,
          })}
        />
      </div>
      <div className="collapse-content flex flex-col z-[99] gap-3 -mt-[28px] relative">
        <ChevronUpIcon className="w-auto h-[14px] absolute top-1 right-4" />
        <SidebarNavItem
          className="p-0"
          href={`/lease/${leaseId}`}
          title="Lease General Information"
          pagePath=""
        />
        <SidebarNavItem
          className="p-0"
          href={`/lease/${leaseId}/agreement`}
          title="Lease Agreement and Signatures"
          pagePath="agreement"
        />
        <SidebarNavItem
          className="p-0"
          href={`/lease/${leaseId}/summary`}
          title="Financial Summary"
          pagePath="summary"
        />
      </div>
    </div>
  );
};

export default LeaseMobileTabSelector;
