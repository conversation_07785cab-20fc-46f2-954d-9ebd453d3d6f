'use client';

import { payoutDamageClaim, submitDamageClaim } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import useForm from '@/hooks/useForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { DamageClaim } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';
import { isAfter } from 'date-fns';
import dayjs from 'dayjs';
import { FormEvent, useCallback, useMemo, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
  damageClaim: Nullable<DamageClaim>;
  isAdmin?: boolean;
  paidAllPayments?: boolean;
  departureDate: string;
};

type FormValues = {
  amount: string;
  reference: string;
};

const DamagePaymentsForm = ({
  leaseId,
  damageClaim,
  isAdmin,
  paidAllPayments,
  departureDate,
}: Props) => {
  const claimDisabled = useMemo(
    () => !isAfter(new Date(), departureDate) || paidAllPayments,
    [departureDate, paidAllPayments]
  );

  console.log('is after', {
    isAfter: isAfter(new Date(), departureDate),
    claimDisabled,
    paidAllPayments,
  });

  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const { formState, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      amount: damageClaim?.amount ?? '',
      reference: '',
    },
    {
      amount: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Amount is required.`;
        } else if (isNaN(Number(_value))) {
          return `Amount must be a number.`;
        } else if (Number(_value) < 0) {
          return `Amount must be greater than zero.`;
        }
      },
    }
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onSubmitClaim = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      Object.values(_errors).map(
        (_error) => _error !== '' && toast.error(_error ?? '')
      );
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      lease: leaseId,
      amount: Number(formState.amount),
    };

    submitDamageClaim(payload)
      .then((res: any) => {
        if (res?.status >= 200 && res?.status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Damage claim submitted successfully!');
        } else {
          console.log('data is', res?.data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(res?.data?.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState.amount, leaseId, preSubmitCheck]);

  const onSubmitPayout = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);
    let payload: any = {
      amount_paid: Number(formState.amount),
    };

    if (formState.reference.trim().length > 0) {
      payload = {
        ...payload,
        reference: formState.reference,
      };
    }

    payoutDamageClaim(leaseId, payload)
      .then((res: any) => {
        if (res?.status >= 200 && res?.status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Damage claim submitted successfully!');
        } else {
          console.log('data is', res?.data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(res?.data?.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState.amount, formState.reference, leaseId]);

  return (
    <>
      <div className="my-2 flex items-center justify-between">
        <Button
          onClick={onSubmitClaim}
          disabled={
            progressStatus === ProgressStatus.LOADING ||
            !!damageClaim?.created_at ||
            damageClaim?.status === 'paid' ||
            claimDisabled
          }
          isLoading={
            progressStatus === ProgressStatus.LOADING &&
            !damageClaim?.created_at
          }
          className="rounded-md !bg-[#F7A2B6] hover:!bg-[#F7A2B6]/50 text-xs min-h-[26px] min-w-[40%]"
        >
          {damageClaim?.created_at
            ? `Submitted by Agent on ${dayjs(damageClaim?.created_at).format(
                'MM/DD/YYYY'
              )}`
            : 'Submit Claim'}
        </Button>
        {!damageClaim?.created_at && (
          <Input
            name="amount"
            wrapperclassName="w-[30%]"
            className="!text-xs !px-4 !py-2 w-full"
            placeholder="$ Amt."
            value={formState.amount}
            onChange={onChangeNumeric}
            error={!!errors?.amount?.length}
            disabled={claimDisabled}
          />
        )}
      </div>
      <div className="flex items-center justify-between gap-x-1 my-2 w-full">
        <Button
          className="text-xs min-w-[40%] min-h-[26px] rounded-md bg-pending-dark hover:bg-pending-dark/70"
          disabled={
            !damageClaim?.created_at ||
            damageClaim?.status === 'paid' ||
            !isAdmin ||
            claimDisabled
          }
          isLoading={
            progressStatus === ProgressStatus.LOADING &&
            !!damageClaim?.created_at
          }
          onClick={onSubmitPayout}
        >
          {damageClaim?.status === 'paid'
            ? 'Payout Recorded by Admin'
            : 'Record Payout'}
        </Button>
        {damageClaim?.status !== 'paid' && (
          <>
            <Input
              value={formState.reference}
              onChange={onChangeTextInput}
              error={!!errors?.reference?.length}
              name="reference"
              placeholder="Ref."
              wrapperclassName="w-[25%]"
              className="!text-xs !px-4 !py-2 w-full"
              disabled={!damageClaim?.created_at}
            />

            <p className="w-[30%] text-right font-normal text-xs border px-4 py-2 rounded-md h-8">
              {damageClaim?.amount &&
                currencyFormatter.format(Number(damageClaim?.amount))}
            </p>
          </>
        )}
      </div>
    </>
  );
};

export default DamagePaymentsForm;
