'use client';

import { submitDisbursement } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import CurrencyInput from '@/clients/ui/currency-input';
import useForm from '@/hooks/useForm';
import { ProgressStatus } from '@/types/common';
import { Payments } from '@/types/lease';
import { currencyFormatter, ensureNumberHas2Decimals } from '@/utils/common';
import { FormEvent, memo, useCallback, useMemo, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  payment: Payments;
};

type FormValues = {
  processing_fee: string;
  tax: string;
  agent_commission: string;
  office_commission: string;
  co_broke_commission: string;
  rent_to_owner: string;
  security_deposit: string;
  other_fee: string;
};

const LeasePaymentDisbursementForm = ({ payment }: Props) => {
  const { disbursement_form, lease: leaseId } = payment;
  const {
    formState,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>({
    tax: disbursement_form.tax ?? '',
    processing_fee: disbursement_form.processing_fee ?? '',
    rent_to_owner: disbursement_form.rent_to_owner ?? '',
    co_broke_commission: disbursement_form.co_broke_commission ?? '',
    agent_commission: disbursement_form.agent_commission ?? '',
    office_commission: disbursement_form.office_commission ?? '',
    security_deposit: disbursement_form.security_deposit ?? '',
    other_fee: disbursement_form.other_fee ?? '',
  });

  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const total = useMemo(
    () =>
      Object.values(formState).reduce(
        (prev, curr) => (prev += Number(curr)),
        0
      ),
    [formState]
  );

  const onSubmit = useCallback(() => {
    console.log({
      amount_received: Number(payment.amount_received),
      total,
    });
    if (
      ensureNumberHas2Decimals(Number(payment.amount_received)) !==
      ensureNumberHas2Decimals(total)
    ) {
      toast.error(
        'Disbursement amount does not match the total received amount'
      );
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    submitDisbursement(payment.disbursement_form.disbursement_form_uuid, {
      ...formState,
    })
      .then((res: any) => {
        if (res?.status >= 200 && res?.status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Disbursement recorded successfully!');
        } else {
          console.log('data is', res?.data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(res?.data?.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    formState,
    leaseId,
    payment.amount_received,
    payment.disbursement_form.disbursement_form_uuid,
    total,
  ]);

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  return (
    <>
      <table className="w-full text-xs overflow-x-auto">
        <thead className="text-black/60">
          <tr className="border-b border-outline">
            <th className="py-2 text-left font-normal w-[30%]">Description</th>
            <th className="py-2 text-left font-normal w-[20%]">Date</th>
            <th className="py-2 text-left font-normal w-[25%]">Reference</th>
            <th className="py-2 font-normal w-[25%] text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Occupancy Tax</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="tax"
                value={formState.tax}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Booking Fee</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="processing_fee"
                value={formState.processing_fee}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Rent to Owner</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="rent_to_owner"
                value={formState.rent_to_owner}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Fees to Owner</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="other_fee"
                value={formState.other_fee}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Agent Comm</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="agent_commission"
                value={formState.agent_commission}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">CoBroke Comm</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="co_broke_commission"
                value={formState.co_broke_commission}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">C&C Comm</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="office_commission"
                value={formState.office_commission}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">To Sec. Deposit</td>
            <td className="py-2 text-left font-normal" />
            <td className="py-2 text-left font-normal" />
            <td className="py-2 font-normal text-right">
              <CurrencyInput
                name="security_deposit"
                value={formState.security_deposit}
                onChange={onChangeNumeric}
                wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                className="text-xs !p-0 border-none w-full"
              />
            </td>
          </tr>
        </tbody>
      </table>
      <div className="flex items-center justify-between my-2 text-xs">
        Total
        <span>{currencyFormatter.format(total)}</span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs">
        Total Received{' '}
        <span>
          {currencyFormatter.format(Number(payment.amount_received ?? 0))}
        </span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs font-semibold">
        Balance{' '}
        <span>
          {currencyFormatter.format(
            ensureNumberHas2Decimals(Number(payment.amount_received ?? 0)) -
              ensureNumberHas2Decimals(total)
          )}
        </span>
      </div>
      <Button
        disabled={
          payment.status !== 'Paid' || !!payment?.disbursement_form?.submit_at
        }
        onClick={onSubmit}
        isLoading={progressStatus === ProgressStatus.LOADING}
        className="rounded-md bg-success hover:bg-success/70 text-xs min-h-[26px] flex justify-self-end min-w-[220px]"
      >
        Submit Payment Request
      </Button>
    </>
  );
};

export default memo(LeasePaymentDisbursementForm);
