'use client';

import { uploadFile, uploadSecurityDepositFiles } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import { Nullable, ProgressStatus } from '@/types/common';
import classNames from 'classnames';
import { ChangeEvent, memo, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
  endpoint?: string;
  disabled?: boolean;
};

const SecurityDepositUploadDocument = ({
  leaseId,
  endpoint,
  disabled,
}: Props) => {
  const [progressStatus, setProgressStatus] =
    useState<Nullable<ProgressStatus>>(null);
  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      if (disabled) {
        event.preventDefault();
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      const file = event.target.files[0];
      console.log('file is', file);

      try {
        let data = new FormData();
        data.append('file', file);
        data.append('file_name', file.name);
        uploadFile(data)
          .then((res: any) => {
            if (res?.status >= 200 && res?.status < 300) {
              uploadSecurityDepositFiles(
                leaseId,
                {
                  attach_file: res?.data?.object_uuid,
                },
                endpoint
              )
                .then((data) => {
                  console.log('response', data);
                  revalidateTagByName(`lease-details-${leaseId}`);
                  setProgressStatus(ProgressStatus.SUCCESSFUL);
                  toast.success('File uploaded successfully!');
                })
                .catch((err) => {
                  console.log('response', err);
                  setProgressStatus(ProgressStatus.FAILED);
                  toast.error('Something went wrong. Please try again later.');
                });
            } else {
              setProgressStatus(ProgressStatus.FAILED);
              toast.error('Something went wrong. Please try again later.');
            }
          })
          .catch((err) => {
            console.log('response', err);
          });
      } catch {}
    }
  };

  return (
    <div className="relative flex items-center justify-end w-full">
      <Button
        className="!bg-[#2C3E50] hover:bg-[#2C3E50]/70 rounded-md mt-2 text-sm !font-normal relative"
        isLoading={progressStatus === ProgressStatus.LOADING}
        disabled={disabled}
      >
        Upload Document
        <input
          id="signed-lease"
          className={classNames(
            'absolute inset-0 opacity-0 z-10  cursor-pointer',
            {
              '!cursor-not-allowed': disabled,
            }
          )}
          type="file"
          accept="application/pdf"
          onChange={handleFileChange}
          disabled={progressStatus === ProgressStatus.LOADING || disabled}
        />
      </Button>
    </div>
  );
};

export default memo(SecurityDepositUploadDocument);
