'use client';

import Button from '@/clients/ui/button';
import { memo, useCallback, useState } from 'react';
import { EnvelopeIcon } from '@heroicons/react/24/outline';
import { ProgressStatus } from '@/types/common';
import { sendReminderAgent } from '@/app/actions/lease';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
};

const LeasePaymentSendRemainderButton = ({ leaseId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onRemind = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);
    sendReminderAgent({ leaseId })
      .then(() => {
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Reminder sent successfully!');
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [leaseId]);

  return (
    <Button
      onClick={onRemind}
      isLoading={progressStatus === ProgressStatus.LOADING}
      className="text-xs bg-navy font-normal !p-2 rounded-md h-[26px] hover:bg-navy/80 w-[140px]"
    >
      <EnvelopeIcon className="w-4 h-4 mr-4" />
      Send Reminder
    </Button>
  );
};

export default memo(LeasePaymentSendRemainderButton);
