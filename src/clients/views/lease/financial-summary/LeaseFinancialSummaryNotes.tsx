'use client';

import { addLeaseComment, deleteLeaseComment } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import DeleteConfirm from '@/clients/components/common/DeleteConfirm';
import Button from '@/clients/ui/button';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { Nullable, ProgressStatus } from '@/types/common';
import { LeaseComment } from '@/types/lease';
import { TrashIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
  comments: LeaseComment[];
  isDamageClaim?: boolean;
  disabled?: boolean;
};

const LeaseFinancialSummaryNotes = ({
  comments,
  leaseId,
  isDamageClaim,
  disabled,
}: Props) => {
  const [showAdd, setShowAdd] = useState<boolean>(false);
  const [note, setNote] = useState<string>('');
  const [showDelete, setShowDelete] = useState<Nullable<string>>(null);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [error, setError] = useState<Nullable<string>>(null);

  const onToggle = useCallback(() => setShowAdd((_a) => !_a), []);
  const onCloseDelete = useCallback(() => setShowDelete(null), []);

  const onChange = useCallback((e: any) => setNote(e.target.value), []);

  const onSave = useCallback(() => {
    console.log('errr', error, note);
    if (error && error?.length !== 0) {
      return;
    }

    if (note.trim().length === 0) {
      setError('Note is required');
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    addLeaseComment(
      leaseId,
      {
        content: note,
      },
      isDamageClaim ? 'damage-claim' : 'security-deposit'
    )
      .then((data) => {
        revalidateTagByName(`lease-comments-${leaseId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Note added successfully!');
        setShowAdd(false);
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [error, isDamageClaim, leaseId, note]);

  const onConfirmDelete = useCallback(() => {
    if (!showDelete) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    deleteLeaseComment(
      leaseId,
      showDelete,
      isDamageClaim ? 'damage-claim' : 'security-deposit'
    )
      .then((data) => {
        revalidateTagByName(`lease-comments-${leaseId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Note deleted successfully!');
        setShowDelete(null);
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [isDamageClaim, leaseId, showDelete]);

  return (
    <>
      <div className="p-4 bg-white rounded-md mt-2">
        <p className="m-0 text-sm font-semibold">Notes</p>
        {comments?.map((_c) => (
          <div
            key={_c.comment_uuid}
            className="p-4 border border-outline rounded-md my-2"
          >
            <div className="text-[10px] flex items-center justify-between">
              {_c.posted_by}
              <div className="flex items-center gap-x-2">
                {dayjs(_c.date).format('MM/DD/YYYY')}
                <TrashIcon
                  className="w-4 h-4 cursor-pointer"
                  onClick={() => setShowDelete(_c.comment_uuid)}
                />
              </div>
            </div>
            <p className="text-xs m-0 mt-1">{_c.content}</p>
          </div>
        ))}
        <Button
          intent="ghost"
          className={classNames(
            'cursor-pointer mt-2 text-xs font-semibold !p-0'
          )}
          onClick={onToggle}
          disabled={disabled}
        >
          + Add Note
        </Button>
      </div>
      {showAdd && (
        <Modal open className="p-4">
          <p className="mb-4 font-bold text-center">Add Note</p>
          <hr />
          <Textarea
            placeholder="Note"
            className="w-full"
            value={note}
            onChange={onChange}
            helperText={error ?? ''}
            error={!!error}
          />
          <div className="flex gap-2 mt-4 justify-between">
            <Button
              intent="outline"
              className="text-xs font-normal"
              onClick={onToggle}
              disabled={progressStatus === ProgressStatus.LOADING}
            >
              Close
            </Button>
            <Button
              className="text-xs"
              onClick={onSave}
              disabled={progressStatus === ProgressStatus.LOADING}
              isLoading={progressStatus === ProgressStatus.LOADING}
            >
              Save
            </Button>
          </div>
        </Modal>
      )}
      {showDelete && (
        <DeleteConfirm
          onClose={onCloseDelete}
          onDelete={onConfirmDelete}
          progressStatus={progressStatus}
        />
      )}
    </>
  );
};

export default memo(LeaseFinancialSummaryNotes);
