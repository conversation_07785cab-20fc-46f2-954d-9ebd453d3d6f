'use client';

import { removeSecurityDepositFiles } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import { Nullable, ProgressStatus } from '@/types/common';
import { TrashIcon } from '@heroicons/react/24/outline';
import { ChangeEvent, memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
  fileId: string;
  endpoint: string;
};

const SecurityDepositDeleteAttachedFileButton = ({
  leaseId,
  fileId,
  endpoint,
}: Props) => {
  const [progressStatus, setProgressStatus] =
    useState<Nullable<ProgressStatus>>(null);

  const onDelete = useCallback(() => {
    if (progressStatus === ProgressStatus.LOADING) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    removeSecurityDepositFiles(
      leaseId,
      {
        attach_file: fileId,
      },
      endpoint
    )
      .then((data) => {
        console.log('response', data);
        revalidateTagByName(`lease-details-${leaseId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.error('File removed successfully!');
      })
      .catch((err) => {
        console.log('response', err);
        setProgressStatus(ProgressStatus.FAILED);
        toast.error('Something went wrong. Please try again later.');
      });
  }, [progressStatus, leaseId, fileId, endpoint]);

  return (
    <>
      {progressStatus === ProgressStatus.LOADING ? (
        <span className="loading loading-spinner loading-sm"></span>
      ) : (
        <TrashIcon className="w-4 h-4 cursor-pointer" onClick={onDelete} />
      )}
    </>
  );
};

export default memo(SecurityDepositDeleteAttachedFileButton);
