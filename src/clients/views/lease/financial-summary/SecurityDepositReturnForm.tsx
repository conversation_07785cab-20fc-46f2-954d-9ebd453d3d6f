'use client';

import { returnSecurityDeposit } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import CurrencyInput from '@/clients/ui/currency-input';
import useForm from '@/hooks/useForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { Payments, SecurityDeposit } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';
import { format } from 'date-fns';
import { FormEvent, memo, useCallback, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import SDFormItem from './SDFormItem';

type Props = {
  leaseId: number;
  securityDeposit: Nullable<SecurityDeposit>;
  lastPayment: Payments;
};

type FormValues = {
  homeowner: string;
  tenant: string;
};

const SecurityDepositReturnButton = ({
  leaseId,
  securityDeposit,
  lastPayment,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const { formState, onChange, preSubmitCheck } = useForm<FormValues>({
    homeowner: '',
    tenant: '',
  });

  const disabled = useMemo(
    () =>
      securityDeposit?.status === 'Paid' ||
      Number(formState.homeowner) + Number(formState.tenant) !==
        Number(securityDeposit?.amount),
    [
      securityDeposit?.status,
      securityDeposit?.amount,
      formState.homeowner,
      formState.tenant,
    ]
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  const onSubmit = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      Object.values(_errors).map(
        (_error) => _error !== '' && toast.error(_error ?? '')
      );
      return;
    }

    if (
      securityDeposit?.amount &&
      Number(formState.homeowner) + Number(formState.tenant) >
        Number(securityDeposit?.amount ?? 0)
    ) {
      toast.error('Amount cannot be greater than the security deposit');
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      lease: leaseId,
      tenant: formState.tenant ?? '',
      homeowner: formState.homeowner ?? '',
    };

    returnSecurityDeposit(leaseId, { return_info: payload })
      .then((res) => {
        if (res.status >= 200 && res.status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Returned successfully!');
        } else {
          console.log('data is', res.data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(res.data.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, leaseId, preSubmitCheck, securityDeposit?.amount]);

  return (
    <>
      <table className="w-full text-xs overflow-x-auto">
        <thead className="text-black/60">
          <tr className="border-b border-outline">
            <th className="py-4 text-left font-normal w-[20%]">Type</th>
            <th className="py-4 text-center font-normal w-[30%]">Date</th>
            <th className="py-4 text-center font-normal w-[30%]">Reference</th>
            <th className="py-4 font-normal w-[20%] text-right">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Received from Tenant</td>
            <td className="py-2 text-center font-normal ">
              {lastPayment.updated_at
                ? format(lastPayment.updated_at, 'LLL d, yyyy')
                : ''}
            </td>
            <td className="py-2 text-center font-normal">
              {lastPayment.reference ?? ''}
            </td>
            <td className="py-2 font-normal text-right">
              {currencyFormatter.format(Number(securityDeposit?.amount ?? 0))}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-2 text-left font-normal">Payout to Owner</td>
            <SDFormItem
              leaseId={leaseId}
              qbInfo={securityDeposit?.return_info}
              dateName="sd_homeowner_bill_payment_date"
              refName="sd_homeowner_bill_payment_ref"
            />
            <td className="py-2 font-normal text-right">
              {securityDeposit?.return_info?.homeowner ? (
                currencyFormatter.format(
                  Number(securityDeposit?.return_info?.homeowner ?? 0)
                )
              ) : (
                <CurrencyInput
                  name="homeowner"
                  wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                  className="text-xs !p-0 border-none w-full"
                  iconClassName="font-normal text-xs"
                  value={formState.homeowner}
                  onChange={onChangeNumeric}
                />
              )}
            </td>
          </tr>
          <tr>
            <td className="py-2 text-left font-normal">Return to Tenant</td>
            <SDFormItem
              leaseId={leaseId}
              qbInfo={securityDeposit?.return_info}
              dateName="sd_tenant_bill_payment_date"
              refName="sd_tenant_bill_payment_ref"
            />
            <td className="py-2 font-normal text-right">
              {securityDeposit?.return_info?.tenant ? (
                currencyFormatter.format(
                  Number(securityDeposit?.return_info?.tenant ?? 0)
                )
              ) : (
                <CurrencyInput
                  name="tenant"
                  wrapperclassName="w-full flex items-center border rounded-[4px] p-2 hover:border-black bg-white"
                  className="text-xs !p-0 border-none w-full"
                  iconClassName="font-normal text-xs"
                  value={formState.tenant}
                  onChange={onChangeNumeric}
                />
              )}
            </td>
          </tr>
        </tbody>
      </table>

      <div className="flex items-center justify-between my-2 text-xs">
        Total{' '}
        <span>{currencyFormatter.format(Number(securityDeposit?.amount))}</span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs">
        Total Received{' '}
        <span>
          {currencyFormatter.format(Number(securityDeposit?.amount_received))}
        </span>
      </div>
      <div className="flex items-center justify-between text-xs font-semibold">
        Balance{' '}
        <span>
          {currencyFormatter.format(Number(securityDeposit?.balance))}
        </span>
      </div>
      <Button
        disabled={disabled}
        onClick={onSubmit}
        isLoading={progressStatus === ProgressStatus.LOADING}
        className="rounded-md bg-success hover:bg-success/70 text-xs min-h-[26px] flex justify-self-end min-w-[220px] mt-2"
      >
        Submit Return Authorization
      </Button>
    </>
  );
};

export default memo(SecurityDepositReturnButton);
