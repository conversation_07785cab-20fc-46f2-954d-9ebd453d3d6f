"use client";

import classNames from "classnames";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { twMerge } from "tailwind-merge";

type Props = {
  href: string;
  title: string;
  status: string;
  className?: string;
  pagePath: string;
};

const SidebarNavItem = ({ href, title, status='', className = "", pagePath }: Props) => {
  const pathname = usePathname();

  return (
    <Link
      href={href}
      className={twMerge(
        classNames("px-4 py-4 text-sm", {
          "bg-white rounded-[10px] rounded-r-none text-[#2C3E50] font-bold flex items-center gap-2":
            pathname.includes(pagePath),
        }),
        className
      )}
    >
      <div>{title}</div>
      <div className="rounded-full bg-yellow-400 w-5">&nbsp;</div>
    </Link>
  );
};

export default SidebarNavItem;
