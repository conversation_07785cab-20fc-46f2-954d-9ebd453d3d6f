'use client';

import { blockLeaseDates, unblockLeaseDates } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import { Nullable, ProgressStatus } from '@/types/common';
import { Lease } from '@/types/lease';
import dynamic from 'next/dynamic';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

const DeleteConfirm = dynamic(
  () => import('@/clients/components/common/DeleteConfirm')
);

type Props = {
  lease: Lease;
};

const UnblockDatesButton = ({ lease }: Props) => {
  const [progressStatus, setProgressStatus] =
    useState<Nullable<ProgressStatus>>(null);
  const [showModal, setShowModal] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowModal((prev) => !prev);
  }, []);

  const onDelete = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);
    if (lease.is_block_date) {
      unblockLeaseDates({ lease: lease.lease_id })
        .then((res) => {
          console.log({ res });
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Dates unblocked successfully!');
          revalidateTagByName(`lease-${lease.lease_id}`);
          onToggle();
        })
        .catch((error) => {
          setProgressStatus(ProgressStatus.FAILED);
          toast.error('Failed to unblock dates!');
        });
    } else {
      blockLeaseDates({ lease: lease.lease_id })
        .then((res) => {
          console.log({ res });
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Dates blocked successfully!');
          revalidateTagByName(`lease-${lease.lease_id}`);
          onToggle();
        })
        .catch((error) => {
          setProgressStatus(ProgressStatus.FAILED);
          toast.error('Failed to block dates!');
        });
    }
  }, [lease.is_block_date, lease.lease_id, onToggle]);

  return (
    <>
      <Button
        intent="outline"
        className="font-medium text-sm rounded-full h-[32px]"
        onClick={onToggle}
      >
        {lease.is_block_date ? `Unblock dates` : `Block dates`}
      </Button>
      {showModal && (
        <DeleteConfirm
          onClose={onToggle}
          progressStatus={progressStatus}
          onDelete={onDelete}
          title={`Do you want to ${
            lease.is_block_date ? 'unblock' : 'lock'
          } this date?`}
          cancelBtnTitle="Close"
          deleteButtonTitle={lease.is_block_date ? 'Unblock' : 'Block'}
        />
      )}
    </>
  );
};

export default UnblockDatesButton;
