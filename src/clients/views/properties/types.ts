import { Listing } from '@/types/property';

export type SortConfig = {
  key: string | null;
  direction: 'asc' | 'desc';
};

export type Column = {
  key: string;
  label: string;
  sortable: boolean;
  className: string;
};

export type PropertiesTableBaseProps = {
  data: Listing[];
  total?: number;
  flag?: boolean;
  offset?: number;
  currentPage?: number;
  searchInfor?: Record<string, any>;
  limit?: number;
  onPagesChanged?: (page: number) => void;
  onSort?: (sortConfig: { prop: string; order: string }) => void;
  onSubset?: (items: Listing[]) => void;
  onShowAllPage?: () => void;
  onLoad?: () => void;
};

export const DEFAULT_COLUMNS: Column[] = [
  { key: 'checkbox', label: '', sortable: false, className: 'w-12' },
  {
    key: 'peak_rate',
    label: 'Price on search criteria',
    sortable: true,
    className: 'w-[200px]',
  },
  { key: 'url', label: 'Image', sortable: false, className: 'w-[120px]' },
  {
    key: 'address',
    label: 'Address',
    sortable: true,
    className: 'max-w-[120px]',
  },
  { key: 'area_name', label: 'Area', sortable: true, className: '' },
  {
    key: 'bedroom_number',
    label: 'BRS',
    sortable: true,
    className: '',
  },
  { key: 'bathroom_number', label: 'Baths', sortable: true, className: '' },
  { key: 'capacity', label: 'Capacity', sortable: true, className: '' },
  {
    key: 'homeowner__last_name',
    label: 'OWNER',
    className: '',
    sortable: true,
  },
  {
    key: 'requirement__turnover_day',
    label: 'TURNOVER',
    className: 'w-[120px]',
    sortable: true,
  },
  {
    key: 'calendar_updated_at',
    label: 'LAST UPDATE',
    className: 'w-[140px]',
    sortable: true,
  },
  {
    key: 'key_number',
    label: 'KEY',
    className: 'max-w-[120px]',
    sortable: true,
  },
  {
    key: '',
    label: 'LATEST LEASED',
    className: 'w-[160px]',
    sortable: false,
  },
];

export const DEFAULT_PROPS = {
  data: [],
  total: 0,
  flag: false,
  offset: 0,
  currentPage: 1,
  searchInfor: {},
  limit: 25,
};
