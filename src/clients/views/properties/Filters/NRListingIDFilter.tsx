'use client';

import Input from '@/clients/ui/input';
import Image from 'next/image';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { memo, useCallback, useState } from 'react';

type Props = {
  setIsUpdatingSearch: (s: boolean) => void;
  nr_listing_id: string;
};

const NRListingIDFilter = ({ setIsUpdatingSearch, nr_listing_id }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [id, setId] = useState(nr_listing_id);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setId(e.target.value);
  }, []);

  const onEnterPressed = useCallback(
    (e: any) => {
      if (e.key === 'Enter') {
        setIsUpdatingSearch(true);
        const params = new URLSearchParams(searchParams.toString());
        if (id === '') {
          params.delete('nr_listing_id');
        } else params.set('nr_listing_id', id);
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [id, pathname, router, searchParams, setIsUpdatingSearch]
  );

  return (
    <Input
      icon={
        <Image
          alt="key icon"
          src="/images/icons/key.svg"
          width={20}
          height={20}
          className="absolute left-2 z-10"
        />
      }
      wrapperclassName="my-2 h-[42px] flex items-center"
      className="text-sm !p-2 w-full !pl-8"
      placeholder="NR Listing ID"
      value={id}
      onChange={handleChange}
      onKeyDown={onEnterPressed}
    />
  );
};

export default memo(NRListingIDFilter);
