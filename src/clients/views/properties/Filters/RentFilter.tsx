'use client';

import Button from '@/clients/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';
import { Slider } from '@/clients/ui/slider';
import { Nullable } from '@/types/common';
import { currencyFormatterRound } from '@/utils/common';
import Image from 'next/image';
import { useCallback, useMemo, useState } from 'react';

type Props = {
  minPrice: Nullable<number>;
  maxPrice: Nullable<number>;
  max: number;
  onChange: (min: Nullable<number>, max: Nullable<number>) => void;
};

const RentFilter = ({ minPrice, maxPrice, onChange, max }: Props) => {
  const [showPrice, setShowPrice] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([
    minPrice ?? 0,
    maxPrice ?? 30000,
  ]);

  const onToggle = useCallback(() => {
    setShowPrice((_p) => !_p);
  }, []);

  const onConfirm = useCallback(() => {
    onChange(priceRange[0], priceRange[1] === max ? null : priceRange[1]);
    setShowPrice(false);
  }, [onChange, priceRange, max]);

  const onClear = useCallback(() => {
    onChange(null, null);
    setShowPrice(false);
    setPriceRange([0, 30000]);
  }, [onChange]);

  const onChangePriceRange = useCallback((_v: [number, number]) => {
    setPriceRange(_v);
  }, []);

  const isRangeSelected = useMemo(
    () => minPrice !== null || maxPrice !== null,
    [maxPrice, minPrice]
  );

  const rangeText = useMemo(
    () =>
      `${currencyFormatterRound.format(priceRange[0])} - ${
        priceRange[1] === 30000
          ? `$30,000+`
          : currencyFormatterRound.format(priceRange[1])
      }`,
    [priceRange]
  );

  return (
    <>
      <Popover open={showPrice} onOpenChange={onToggle}>
        <PopoverTrigger asChild>
          <div className="flex items-center gap-x-2 p-2 border rounded text-sm cursor-pointer h-[42px]">
            <Image
              alt="rent filter icon"
              src="/images/icons/price.svg"
              width={20}
              height={20}
            />
            {isRangeSelected ? rangeText : `Price Range`}
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="md:min-w-[400px] w-max border border-solid border-english-manor border-opacity-40 p-6 z-[9999] bg-white"
          align="start"
        >
          <div className="flex items-center gap-x-2">
            <p className="text-xs">$0</p>
            <Slider
              defaultValue={priceRange}
              step={5000}
              onValueChange={onChangePriceRange}
              max={max}
            />
            <p className="text-xs">$30,000+</p>
          </div>
          <p className="my-2 font-medium">
            Selected range:{' '}
            <span className="text-carolina-blue">{rangeText}</span>
          </p>
          <div className="flex items-center justify-between">
            <Button
              className="!text-sm !font-medium rounded-md"
              intent="outline"
              onClick={onClear}
            >
              Clear
            </Button>
            <Button
              className="!text-sm !font-medium rounded-md"
              onClick={onConfirm}
            >
              Confirm
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default RentFilter;
