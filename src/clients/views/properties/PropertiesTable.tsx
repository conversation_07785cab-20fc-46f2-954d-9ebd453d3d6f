'use client';
import React from 'react';
import { PropertiesTableClient } from './PropertiesTableClient';
import {
  PropertiesTableBaseProps,
  DEFAULT_COLUMNS,
  DEFAULT_PROPS,
} from './types';

export type PropertiesTableProps = PropertiesTableBaseProps;

/**
 * Backward compatibility wrapper for PropertiesTable
 * This maintains the existing API while using the new client component
 */
const PropertiesTable = ({
  data = DEFAULT_PROPS.data,
  total = DEFAULT_PROPS.total,
  flag = DEFAULT_PROPS.flag,
  offset = DEFAULT_PROPS.offset,
  currentPage = DEFAULT_PROPS.currentPage,
  searchInfor = DEFAULT_PROPS.searchInfor,
  limit = DEFAULT_PROPS.limit,
  onPagesChanged,
  onSort,
  onSubset,
  onShowAllPage,
  onLoad,
}: PropertiesTableProps) => {
  // Create a placeholder static table (not used in client component)
  const staticTable = <div></div>;
  console.log('the offset', offset);

  return (
    <>
      {data.length > 0 && (
        <div className="mt-[30px] flex gap-x-4 items-center">
          <p className="text-xs font-medium">Show 1 - 25 of {total} listings</p>
        </div>
      )}
      <PropertiesTableClient
        data={data}
        total={total}
        flag={flag}
        offset={offset}
        currentPage={currentPage}
        searchInfor={searchInfor}
        limit={limit}
        staticTable={staticTable}
        columns={DEFAULT_COLUMNS}
        onPagesChanged={onPagesChanged}
        onSort={onSort}
        onSubset={onSubset}
        onShowAllPage={onShowAllPage}
        onLoad={onLoad}
      />
    </>
  );
};

export default PropertiesTable;
