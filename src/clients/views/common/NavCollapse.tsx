"use client";

import { ChevronDownIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import React, { useCallback, useState } from "react";

type Props = {
  children: React.ReactNode;
  title: React.ReactNode;
  name: string;
};

const NavCollapse = ({ children, title, name }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setOpen(!open);
  }, [open]);
  return (
    <div className="collapse collapse-arrow " onClick={onToggle}>
      <input
        type="radio"
        name={name}
        checked={open}
        onChange={onToggle}
        className="!min-h-[36px]"
      />
      <div className="w-full flex items-center justify-between collapse-title p-2 !min-h-[36px] after:!hidden">
        {title}
        <ChevronDownIcon
          className={classNames("w-auto h-[14px]", {
            "rotate-180": open,
          })}
        />
      </div>
      <div className="collapse-content flex flex-col z-[99]">{children}</div>
    </div>
  );
};

export default NavCollapse;
