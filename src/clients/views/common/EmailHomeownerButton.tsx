"use client";

import { Property } from "@/types/property";
import dynamic from "next/dynamic";
import { ReactNode, useCallback, useState } from "react";

const EmailHomeownerModal = dynamic(() => import("./EmailHomeownerModal"));

type Props = {
  children: ReactNode;
  property: Property;
};

const EmailHomeownerButton = ({ children, property }: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setShowModal(false);
  }, []);
  return (
    <>
      <div
        onClick={() => setShowModal(true)}
        className="flex items-center justify-between cursor-pointer"
      >
        {children}
      </div>
      {showModal && (
        <EmailHomeownerModal onClose={onClose} property={property} />
      )}
    </>
  );
};

export default EmailHomeownerButton;
