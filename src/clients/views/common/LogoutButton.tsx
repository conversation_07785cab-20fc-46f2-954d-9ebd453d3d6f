"use client";

import { useCallback } from "react";

import { useRouter } from "next/navigation";
import { deleteCookie } from "@/app/actions/cookies";

const LogoutButton = () => {
  const router = useRouter();

  const onLogout = useCallback(async () => {
    deleteCookie("token")
      .then((res) => {
        console.log("the res in deleteCookie", res);
        router.push("/login");
      })
      .catch((err) => console.error("Failed to logout", err));
  }, [router]);
  return (
    <>
      <div className="rounded-none" onClick={onLogout}>
        Logout
      </div>
    </>
  );
};

export default LogoutButton;
