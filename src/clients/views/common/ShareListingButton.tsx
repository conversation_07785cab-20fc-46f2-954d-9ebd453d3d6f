"use client";

import { ShareIcon } from "@heroicons/react/24/outline";
import dynamic from "next/dynamic";
import { useCallback, useState } from "react";

const ShareListingModal = dynamic(() => import("./ShareListingModal"));

type Props = {
  propertyId: number;
};

const ShareListingButton = ({ propertyId }: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setShowModal(false);
  }, []);

  return (
    <>
      <div
        onClick={() => setShowModal(true)}
        className="w-[28px] h-[28px] rounded-full bg-[#678993] text-white flex items-center justify-center cursor-pointer"
      >
        <ShareIcon className="w-4 h-4" />
      </div>
      {showModal && (
        <ShareListingModal onClose={onClose} propertyId={propertyId} />
      )}
    </>
  );
};

export default ShareListingButton;
