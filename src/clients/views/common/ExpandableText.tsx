'use client';

import { memo, useCallback, useState } from 'react';

type Props = {
  text: string;
  className?: string;
  limitLength?: number;
};

const ExpandableText = ({ text, className = '', limitLength = 275 }: Props) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const shouldShowToggle = text.length > limitLength;
  const displayedText = isExpanded ? text : text.slice(0, limitLength);

  return (
    <div>
      <p className={className}>
        {displayedText}

        {shouldShowToggle && (
          <span
            role="presentation"
            onClick={toggleExpanded}
            className="text-carolina-blue underline cursor-pointer font-semibold pl-1"
          >
            {isExpanded ? 'Show Less' : 'Show More'}
          </span>
        )}
      </p>
    </div>
  );
};

export default memo(ExpandableText);
