"use client";

import { useCallback, useState } from "react";

import ReactDatePicker from "react-datepicker";

import { Nullable, ProgressStatus } from "@/types/common";

import dayjs from "dayjs";
import Modal from "@/clients/ui/modal";
import Button from "@/clients/ui/button";

type Props = {
  open: boolean;
  onClose: () => void;
  time: string;
  title?: string;
  isCheckin?: boolean;
  onSave: (time: Date) => void;
  progressStatus?: ProgressStatus;
};

const TimePickerModal = ({
  open,
  onClose,
  time,
  title,
  isCheckin,
  progressStatus,
  onSave,
}: Props) => {
  const [startDate, setStartDate] = useState<Nullable<Date>>(
    dayjs(time, "hh:mm:ss").toDate()
  );

  const onChange = useCallback((date: Nullable<Date>) => {
    setStartDate(date);
  }, []);

  const getTimeClassName = useCallback((date: Date) => {
    return `text-[19px] font-medium text-center cursor-pointer focus:outline-none`;
  }, []);

  const onClickedSave = useCallback(() => {
    if (startDate) {
      onSave(startDate);
    }
  }, [onSave, startDate]);

  return (
    <Modal open={open} className="p-8 max-w-max">
      {!!title && (
        <p className="py-4 text-[19px] font-bold text-center mb-2">{title}</p>
      )}
      <div>
        <div className="max-h-[200px] overflow-y-scroll">
          <ReactDatePicker
            selected={startDate}
            onChange={onChange}
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={30}
            timeCaption=""
            dateFormat="HH:MM"
            timeClassName={getTimeClassName}
            inline
          />
        </div>
        <div className="mt-4 flex-center-between w-full">
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal w-[128px]"
            onClick={onClose}
          >
            Close
          </Button>
          <Button
            className="rounded-lg text-sm font-normal w-[128px]"
            onClick={onClickedSave}
            disabled={progressStatus === ProgressStatus.LOADING}
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default TimePickerModal;
