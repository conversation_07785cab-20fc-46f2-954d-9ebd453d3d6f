"use client";

import { emailListingHomeowner } from "@/app/actions/property";
import Button from "@/clients/ui/button";
import Modal from "@/clients/ui/modal";
import { ProgressStatus } from "@/types/common";
import { Property } from "@/types/property";
import {
  EnvelopeIcon,
  PaperClipIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { FormEvent, useCallback, useState } from "react";
import toast from "react-hot-toast";
import "react-quill/dist/quill.snow.css";
import RichTextEditor from "./RichTextEditor";
import Input from "@/clients/ui/input";
import { EMAIL_PATTERN } from "@/constants/pattern";
import FormHelperText from "@/app/ui/form-helper-text";
import classNames from "classnames";

type Props = {
  onClose: () => void;
  property: Property;
};

type Errors = {
  cc_emails?: string;
  email_body?: string;
};

const EmailHomeownerModal = ({ onClose, property }: Props) => {
  const [ccEmails, setCCEmails] = useState("");
  const [showCc, setShowCc] = useState<boolean>(false);
  const [subject, setSubject] = useState(property.address);
  const [value, setValue] = useState("");
  const [errors, setErrors] = useState<Errors>({});
  const [files, setFiles] = useState<File[]>([]);

  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onImageSelect = useCallback(
    async (e: FormEvent<HTMLInputElement>) => {
      const target = e.currentTarget;
      const selectedFiles = target.files as FileList;
      setFiles([...files, ...selectedFiles]);
    },
    [files]
  );

  const onRemoveImage = useCallback(
    (index: number) => {
      setFiles([...files.slice(0, index), ...files.slice(index + 1)]);
    },
    [files]
  );

  const onToggleCC = useCallback(() => {
    setShowCc(!showCc);
  }, [showCc]);

  const onChangeSubject = useCallback((e: any) => {
    const { value } = e.target;
    setSubject(value);
  }, []);

  const onChangeCcEmails = useCallback(
    (e: any) => {
      const { value } = e.target;
      setCCEmails(value);
      const emails = (value as string)
        .split(";")
        .filter((_em) => _em.trim().length > 0);

      if (emails.some((_email) => !_email.trim().match(EMAIL_PATTERN))) {
        setErrors({
          ...errors,
          cc_emails: "Enter valid email",
        });
      } else {
        setErrors({
          ...errors,
          cc_emails: undefined,
        });
      }
    },
    [errors]
  );

  const onSend = useCallback(() => {
    if (value.trim().length === 0) {
      setErrors({
        ...errors,
        email_body: "Body is required",
      });

      return;
    }

    if (Object.keys(errors).length > 0) {
      setErrors({});
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    let payload;
    if (files.length > 0) {
      const formdata = new FormData();
      formdata.append("to_emails", property?.homeowner?.email1 ?? "");
      if (ccEmails.length > 0) {
        ccEmails.split(";").map((_email) => {
          formdata.append("cc_emails", _email);
        });
      }
      formdata.append("email_subject", property.address);
      formdata.append("email_body", value);
      files.map((_file) => {
        formdata.append("attachments", _file);
      });
      payload = formdata;
    } else {
      payload = {
        to_emails: [property?.homeowner?.email1 ?? ""],
        cc_emails: ccEmails.split(";").filter((_em) => _em.trim().length > 0),
        email_subject: subject,
        email_body: value,
      };
    }
    emailListingHomeowner(payload)
      .then((values) => {
        console.log(values);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Email sent Successfully!");
        onClose();
      })
      .catch((error) => {
        console.error(error.message);
        toast.success("Failed to sent email!");
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    ccEmails,
    errors,
    files,
    onClose,
    property.address,
    property?.homeowner?.email1,
    subject,
    value,
  ]);

  return (
    <Modal open className="w-full md:w-[650px] md:p-0 rounded-[10px]">
      <div className="bg-navy flex items-center justify-between text-sm text-white px-4 py-2 rounded-t-[10px]">
        New Message
        <XMarkIcon className="w-4 h-4 cursor-pointer" onClick={onClose} />
      </div>
      <div className="flex items-center gap-x-8 px-4 py-2 text-xs border-b border-solid">
        <p className="text-black-80">To: </p>
        <p className="w-10/12 text-black">{property?.homeowner?.email1}</p>
        <p className="text-black-60 cursor-pointer" onClick={onToggleCC}>
          Cc
        </p>
      </div>
      {showCc && (
        <div
          className={classNames(
            "flex items-center gap-x-8 px-4 py-2 text-xs border-b border-solid relative",
            {
              "pb-4": !!errors?.cc_emails,
            }
          )}
        >
          <p className="text-black-80">Cc: </p>
          <Input
            name="cc_emails"
            value={ccEmails}
            type="email"
            onChange={onChangeCcEmails}
            placeholder="Separate multiple email with a semicolon"
            wrapperclassName="flex-grow"
            className="text-black w-full p-0 border-0 text-xs"
          />
          {errors?.cc_emails && (
            <div className="ml-2 absolute bottom-0">
              <FormHelperText error>{errors?.cc_emails}</FormHelperText>
            </div>
          )}
        </div>
      )}
      <div className="px-4 py-2 text-black-60 text-xs border-b border-solid">
        <Input
          name="subject"
          value={subject}
          onChange={onChangeSubject}
          placeholder="Subject"
          wrapperclassName="flex-grow"
          className="text-black w-full p-0 border-0 text-xs"
        />
      </div>
      <div
        className={classNames("relative", {
          "pb-4": !!errors.email_body,
        })}
      >
        <RichTextEditor value={value} setValue={setValue} />
        {errors?.email_body && (
          <div className="ml-2 absolute bottom-0">
            <FormHelperText error>{errors?.email_body}</FormHelperText>
          </div>
        )}
      </div>
      <div
        className={classNames({
          "px-4 mt-2": files.length > 0,
        })}
      >
        {files.map((_file, index) => (
          <div
            key={index}
            className="my-1 w-full bg-black/5 text-xs text-carolina-blue p-2 rounded flex items-center"
          >
            <span className="flex-grow">{_file.name}</span>
            <XMarkIcon
              className="w-4 h-4 text-error cursor-pointer"
              onClick={() => onRemoveImage(index)}
            />
          </div>
        ))}
      </div>
      <div className="p-4 flex items-center gap-x-4">
        <Button
          className="flex items-center justify-center gap-x-2 min-w-[100px]"
          onClick={onSend}
          disabled={
            Object.values(errors).filter((_v) => !!_v).length > 0 ||
            progressStatus === ProgressStatus.LOADING
          }
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          <EnvelopeIcon className="w-4 h-4" /> Send
        </Button>
        <div className="relative cursor-pointer p-2">
          <input
            type="file"
            className="absolute inset-0 cursor-pointer opacity-0"
            onChange={onImageSelect}
            accept="*"
            multiple
          />
          <PaperClipIcon className="w-4 h-4" />
        </div>
      </div>
    </Modal>
  );
};

export default EmailHomeownerModal;
