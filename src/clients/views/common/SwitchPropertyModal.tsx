"use client";

import Modal from "@/clients/ui/modal";
import AddressForm from "./AddressForm";

type Props = {
  onClose: () => void;
};

const SwitchPropertyModal = ({ onClose }: Props) => {
  return (
    <Modal
      open
      className="p-8"
      wrapperClassName="items-start !pt-[10vh]"
      onClose={onClose}
    >
      <AddressForm />
    </Modal>
  );
};

export default SwitchPropertyModal;
