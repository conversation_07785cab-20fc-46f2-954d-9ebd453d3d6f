'use client';

import { useCallback, useState } from 'react';

import { Nullable } from '@/types/common';

import Button from '@/clients/ui/button';
import { usePathname, useRouter } from 'next/navigation';
import AddressAutocomplete from './AddressAutocomplete';
import { ListingAddressPayload } from '@/types/property';

const AddressForm = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [address, setAdress] = useState<string>('');
  const [selectedpropertyId, setSelectedPropertyId] =
    useState<Nullable<number>>(null);

  const onSelectAddress = useCallback((option: ListingAddressPayload) => {
    setAdress(option.address);
    setSelectedPropertyId(option.listing_id);
  }, []);

  const onContinue = useCallback(() => {
    if (address && selectedpropertyId) {
      const pathnameSplit = pathname.split('/');
      pathnameSplit[2] = selectedpropertyId.toString();
      router.push(pathnameSplit.join('/'));
    }
  }, [address, pathname, router, selectedpropertyId]);

  return (
    <>
      <div className="rounded-lg border border-[rgba(0,0,0,0.20)] p-2 md:p-4 my-4">
        <p className="text-xs md:text-sm">
          Enter your vacation rental listing address
        </p>
        <hr className="my-2" />
        <AddressAutocomplete
          value={address}
          placeholder="Listing Address..."
          onSelectAddress={onSelectAddress}
        />
      </div>
      <Button
        className="text-sm py-2 px-4 font-normal bg-carolina-blue text-white rounded-lg flex m-auto"
        disabled={!address}
        onClick={onContinue}
      >
        Continue
      </Button>
    </>
  );
};

export default AddressForm;
