"use client";

import { Bars3Icon } from "@heroicons/react/24/outline";
import { Fragment, useCallback, useState } from "react";
import { Transition } from "@headlessui/react";

type Props = {
  children: React.ReactNode;
};

const MobileNavigation = ({ children }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setOpen(!open);
  }, [open]);

  return (
    <div className="md:hidden">
      <Bars3Icon className="w-5 h-5 text-white" onClick={onToggle} />
      <Transition
        as={Fragment}
        show={open}
        appear={true}
        enter="transition ease-linear duration-150 transform"
        enterFrom="translate-x-full"
        enterTo="translate-x-0"
        leave="transition ease-linear duration-150 transform"
        leaveFrom="translate-x-0"
        leaveTo="translate-x-full"
      >
        {(ref) => (
          <div className="z-[99999] absolute top-[63px] left-2 right-2 bg-white shadow-dropdown rounded-md px-6 py-4">
            {children}
          </div>
        )}
      </Transition>
    </div>
  );
};

export default MobileNavigation;
