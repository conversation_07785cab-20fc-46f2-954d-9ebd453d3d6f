"use client";

import { useEffect, useRef, useState } from "react";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";

const fontSizeArr = [
  "10px",
  "12px",
  "14px",
  "16px",
  "20px",
  "24px",
  "32px",
  "42px",
  "54px",
  "68px",
  "84px",
  "98px",
];

const Size = Quill.import("attributors/style/size") as any;
Size.whitelist = fontSizeArr;
Quill.register(Size, true);

const modules = {
  toolbar: [
    [{ font: [] }], // Font dropdown
    [{ size: fontSizeArr }],
    ["bold", "italic", "underline", "strike"], // Toggled buttons
    [{ list: "ordered" }, { list: "bullet" }],
    [
      { align: "" },
      { align: "center" },
      { align: "right" },
      { align: "justify" },
    ], // Alignment options
    ["blockquote", "code-block"],
    [{ color: [] }, { background: [] }],
    ["link"], // Add link and image support
    ["clean"], // Remove formatting
  ],
};

const formats = [
  "font",
  "size",
  "bold",
  "italic",
  "underline",
  "strike",
  "color",
  "background",
  "align",
  "blockquote",
  "code-block",
  "list",
  "link",
];

type Props = {
  value: string;
  setValue: (_v: string) => void;
};

const RichTextEditor = ({ value, setValue }: Props) => {
  const quillWrapperRef = useRef<HTMLDivElement | null>(null);

  const handleClickOutside = (event: MouseEvent) => {
    const toolbarDropdowns = document.querySelectorAll(
      ".ql-toolbar .ql-picker-options"
    );
    if (
      quillWrapperRef.current &&
      !quillWrapperRef.current.contains(event.target as Node)
    ) {
      toolbarDropdowns.forEach((dropdown) => {
        // Ensure TypeScript knows dropdown is an HTMLElement
        if (dropdown instanceof HTMLElement) {
          dropdown.style.display = "none"; // Hide all dropdowns
        }
      });
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={quillWrapperRef}>
      <ReactQuill
        className="flex flex-col-reverse [&>.ql-container]:!border-0  [&_.ql-editor]:min-h-[100px]"
        theme="snow"
        value={value}
        onChange={setValue}
        modules={modules}
        formats={formats}
      />
    </div>
  );
};

export default RichTextEditor;
