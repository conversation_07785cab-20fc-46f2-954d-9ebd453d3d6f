"use client";

import { ArrowsRightLeftIcon } from "@heroicons/react/24/outline";
import dynamic from "next/dynamic";
import { useCallback, useState } from "react";

const SwitchPropertyModal = dynamic(() => import("./SwitchPropertyModal"));

const SwitchPropertyButton = () => {
  const [show, setShow] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShow(!show);
  }, [show]);

  return (
    <>
      <span
        className="cursor-pointer w-[28px] h-[28px] rounded-full flex items-center justify-center bg-[#678993] text-white"
        onClick={onToggle}
      >
        <ArrowsRightLeftIcon className="w-5 h-5" />
      </span>
      {show && <SwitchPropertyModal onClose={onToggle} />}
    </>
  );
};

export default SwitchPropertyButton;
