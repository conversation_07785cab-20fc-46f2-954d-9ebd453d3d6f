"use client";

import { Fragment, ReactNode } from "react";

import { Transition } from "@headlessui/react";

import { twMerge } from "tailwind-merge";

type Props = {
  isShowing?: boolean;
  children: ReactNode;
  wrapperClassName?: string;
};

const SlidePane = ({ isShowing, children, wrapperClassName }: Props) => {
  return (
    <Transition
      as={Fragment}
      show={isShowing}
      appear={true}
      enter="transition ease-linear duration-150 transform"
      enterFrom="translate-x-full"
      enterTo="translate-x-0"
      leave="transition ease-linear duration-150 transform"
      leaveFrom="translate-x-0"
      leaveTo="translate-x-full"
    >
      {(ref) => (
        <div
          // ref={ref}
          id="slidePane"
          className={twMerge(
            "z-[55] bg-white fixed md:absolute inset-0 top-[64px] md:top-0 xl:top-4 pb-[80px] md:pb-0 h-full",
            wrapperClassName
          )}
        >
          {children}
        </div>
      )}
    </Transition>
  );
};

export default SlidePane;
