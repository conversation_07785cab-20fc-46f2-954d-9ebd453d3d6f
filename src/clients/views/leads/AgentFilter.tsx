'use client';

import { searchAgents } from '@/app/actions/profile';
import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import { AgentUser } from '@/types/profile';
import { getFullName } from '@/utils/common';
import { useCallback, useMemo, useState } from 'react';

type Props = {
  onSelectAgent?: (agent?: AgentUser) => void;
  agent_name?: string;
};

const AgentFilter = ({ onSelectAgent, agent_name }: Props) => {
  const [text, setText] = useState<string>(agent_name ?? '');
  const [isFetching, setIsFetching] = useState<boolean>(false);

  const [agents, setAgents] = useState<AgentUser[]>([]);

  const options = useMemo(
    () =>
      agents?.map((_agent) => ({
        id: _agent.user_id,
        label: getFullName(_agent.first_name, _agent.last_name),
        value: _agent.user_id,
      })),
    [agents]
  );

  const onSelect = useCallback(
    (option: AutocompleteOption) => {
      setText(option.label);
      const agent = agents?.find((_a) => _a.user_id === option.id);
      if (agent && onSelectAgent) {
        onSelectAgent?.(agent);
      }
    },
    [agents, onSelectAgent]
  );

  const fetchAgents = useCallback((query = '') => {
    setIsFetching(true);
    searchAgents<{ results: AgentUser[] }>(query)
      .then(({ results }) => {
        setAgents(results);
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  const onClear = useCallback(() => {
    setText('');
    onSelectAgent?.(undefined);
  }, [onSelectAgent]);

  return (
    <Autocomplete
      className="py-2 px-4"
      dropdownClassName="absolute w-full"
      wrapperClassName="w-[280px] ml-8"
      placeholder="Search by agent name "
      value={text}
      options={options}
      onChangeValue={(text: string) => setText(text)}
      isFetchingData={isFetching}
      fetchData={fetchAgents}
      onSelect={onSelect}
      onClear={onClear}
    />
  );
};

export default AgentFilter;
