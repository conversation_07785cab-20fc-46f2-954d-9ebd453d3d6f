'use client';

import LoadingSpinner from '@/clients/ui/loading-spinner';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { twMerge } from 'tailwind-merge';

type Props = {
  title: string;
  queryKey: string;
  queryValue: string;
  className: string;
};

const SortableTableHeader = ({
  title,
  className,
  queryKey,
  queryValue,
}: Props) => {
  // UI state
  const [isUpdatingSearch, setIsUpdatingSearch] = useState(false);

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const isDescending = useMemo(
    () =>
      (searchParams.get(queryKey) &&
        searchParams.get(queryKey) === `-${queryValue}`) ||
      !searchParams.get(queryKey),
    [queryKey, queryValue, searchParams]
  );

  const onClick = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());
    const ordering = isDescending ? queryValue : `-${queryValue}`;
    setIsUpdatingSearch(true);
    params.set(queryKey, ordering);
    router.push(`${pathname}?${params.toString()}`);
  }, [isDescending, pathname, queryKey, queryValue, router, searchParams]);

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams]);

  return (
    <>
      <th className={twMerge('cursor-pointer', className)} onClick={onClick}>
        <span className="mr-2">{title}</span>
        {isDescending ? '▼' : '▲'}
      </th>
      {isUpdatingSearch && (
        <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
          <LoadingSpinner className="w-10 h-10 text-olive" />
        </div>
      )}
    </>
  );
};

export default memo(SortableTableHeader);
