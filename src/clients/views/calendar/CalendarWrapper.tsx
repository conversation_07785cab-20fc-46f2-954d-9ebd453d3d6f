"use client";

import {
  MouseEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import toast from "react-hot-toast";

import { revalidateTagByName } from "@/app/actions/revalidateTag";
import { deleteCalendarAvailability } from "@/app/actions/calendar";
import {
  BlockedType,
  CalendarViewType,
  PropertyRentalRates,
} from "@/types/calendar";
import { Nullable, ProgressStatus } from "@/types/common";
import { getMonthYearsList } from "@/utils/calendar";
import { isMobileDevice } from "@/utils/responsive";

import classNames from "classnames";
import dayjs, { Dayjs } from "dayjs";
import dynamic from "next/dynamic";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Calendar from "@/clients/components/Calendar";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";
import { PropertyAvailability } from "@/types/calendar";

const DeleteBlockModal = dynamic(
  () => import("./Manage/Availability/DeleteBlockModal")
);
const CalendarPopup = dynamic(
  () => import("@/clients/components/Calendar/CalendarPopup")
);

type Props = {
  availabilities: PropertyAvailability[];
  rentalRates: PropertyRentalRates[];
  propertyId: number;
};

export type PopupProps = {
  x?: number;
  y?: number;
  height?: number;
  width?: number;
  availability_uuid: string;
};

const CalendarWrapper = ({
  rentalRates,
  propertyId,
  availabilities,
}: Props) => {
  const [wrapperScrollY, setWrapperScrollY] = useState(0);
  const {
    currentMonth,
    currentYear,
    selected,
    setSelected,
    view,
    optimisticRates,
  } = useContext(ManageCalendarContext);
  const wrapperRef = useRef<null | HTMLDivElement>(null);
  const currentMonthRef = useRef<null | HTMLDivElement>(null);
  const prevMonthRef = useRef<null | HTMLDivElement>(null);
  const nextMonthRef = useRef<null | HTMLDivElement>(null);
  const [mouseOverDate, setMouseOverDate] = useState<null | Dayjs>(null);
  const [showPopup, setShowPopup] = useState<Nullable<PopupProps>>(null);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [confirmDeleteId, setConfirmDeleteId] =
    useState<Nullable<string>>(null);

  const popupData = useMemo(
    () =>
      availabilities?.find(
        (_a) => _a.availability_uuid === showPopup?.availability_uuid
      ),
    [availabilities, showPopup?.availability_uuid]
  );
  const months = useMemo(() => getMonthYearsList(currentYear), [currentYear]);
  const cellHeightClassName = useMemo(
    () =>
      view === CalendarViewType.MONTH
        ? `h-[80px] md:h-[112px]`
        : `h-[80px] md:h-[90px]`,
    [view]
  );

  const onDeleteClick = useCallback((id: string) => {
    console.log("id", id);
    setConfirmDeleteId(id);
  }, []);
  const onCloseConfirmDelete = useCallback(() => {
    setConfirmDeleteId(null);
  }, []);

  const onDeleteConfirm = useCallback(() => {
    if (confirmDeleteId) {
      setProgressStatus(ProgressStatus.LOADING);
      const availability = availabilities.find(
        (_a) => _a.availability_uuid === confirmDeleteId
      );
      if (availability) {
        deleteCalendarAvailability(propertyId, {
          availabilities: [
            {
              from_date: availability?.from_date ?? "",
              to_date: availability?.to_date ?? "",
              availability: true,
              type: availability?.type,
            },
          ],
        })
          .then((data) => {
            console.debug("data is", data);
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            revalidateTagByName(`property-details-${propertyId}`);
            toast.success("Successfully deleted!");
            onCloseConfirmDelete();
          })
          .catch((error) => {
            console.log("errror is", error);
            setProgressStatus(ProgressStatus.FAILED);
          });
      }
    }
  }, [availabilities, confirmDeleteId, onCloseConfirmDelete, propertyId]);

  const onSelectStartDate = useCallback((e: MouseEvent<HTMLElement>) => {
    const target = e.currentTarget;
    const isMobile = isMobileDevice(navigator.userAgent);
    const deviceOffset = 100;
    const scroll = target.offsetTop - deviceOffset;
    setWrapperScrollY(scroll);
    if (isMobile) {
      const mobileleftpane = document.getElementById("calendar-left-pane");
      if (mobileleftpane) {
        mobileleftpane.scrollTo({
          top: scroll,
          left: 0,
          behavior: "smooth",
        });
      }
    }
  }, []);

  const onSelectDate = useCallback(
    (e: MouseEvent<HTMLElement>, dateObject: Dayjs) => {
      if (!dateObject?.isValid()) return;

      if (selected[0]?.isSame(dateObject)) {
        setSelected([null, null]);
        return;
      }

      const isStartNull = selected.every((_s) => _s === null);
      const isBothValid = selected.every((_d) => dayjs(_d).isValid());

      if (isStartNull || isBothValid) {
        onSelectStartDate(e);
        setSelected([dateObject, null]);
        return;
      }

      if (dateObject.isAfter(selected[0])) {
        setSelected([selected[0], dateObject]);
      } else {
        setSelected([dateObject, null]);
        return;
      }
    },
    [onSelectStartDate, selected, setSelected]
  );

  useEffect(() => {
    const isMobile = isMobileDevice(navigator.userAgent);
    const pageContent = document.getElementById("pageContent");
    if (pageContent && !isMobile) {
      pageContent.style.overflowY = "hidden";
      pageContent.style.height = "100vh";
    }
    return () => {
      if (pageContent && !isMobile) {
        pageContent.style.overflowY = "auto";
        pageContent.style.height = "initial";
      }
    };
  }, []);

  useEffect(() => {
    if (wrapperRef.current && currentMonthRef.current) {
      const isMobile = isMobileDevice(navigator.userAgent);
      const deviceOffset = isMobile
        ? 60
        : view === CalendarViewType.YEAR
        ? 226
        : 186;
      const scroll = currentMonthRef.current.offsetTop - deviceOffset;
      setWrapperScrollY(scroll);
      if (isMobile) {
        const mobileleftpane = document.getElementById("calendar-left-pane");
        if (mobileleftpane) {
          mobileleftpane.scrollTop = scroll;
        }
      } else {
        wrapperRef.current.scrollTop = scroll;
      }
    }
  }, [view, currentYear]);

  return (
    <div
      id="calendarWrapper"
      className={classNames(
        `grid grid-cols-1 xl:grid-cols-2 gap-2 xl:gap-4 pb-[40px]`,
        {
          "!grid-cols-1": view === CalendarViewType.MONTH,
          "!pb-[200px] md:!pb-[40px]": selected.some((_date) =>
            _date?.isValid()
          ),
        }
      )}
      ref={wrapperRef}
    >
      {months.map((_month, index) => (
        <div
          ref={
            _month.month === currentMonth
              ? currentMonthRef
              : _month.month === currentMonth + 1
              ? nextMonthRef
              : _month.month === currentMonth - 1
              ? prevMonthRef
              : null
          }
          key={index}
          className={classNames({
            [`current-month-${_month.month}-${_month.year}`]:
              _month.month === currentMonth,
            [`prev-month-${_month.month}-${_month.year}`]:
              _month.month === currentMonth - 1,
            [`next-month-${_month.month}-${_month.year}`]:
              _month.month === currentMonth + 1,
          })}
        >
          <Calendar
            month={_month.month}
            year={_month.year}
            selected={selected}
            mouseOverDate={mouseOverDate}
            setMouseOverDate={setMouseOverDate}
            cellHeightClassName={cellHeightClassName}
            view={view}
            onSelectDate={onSelectDate}
            availabilities={availabilities}
            rentalRates={optimisticRates}
            setShowPopup={setShowPopup}
          />
        </div>
      ))}
      {showPopup !== null && (
        <CalendarPopup
          isShowing={!!showPopup}
          popup={showPopup}
          setShowPopup={setShowPopup}
          popupData={popupData}
          onDeleteClick={onDeleteClick}
        />
      )}
      {!!confirmDeleteId && (
        <DeleteBlockModal
          open={!!confirmDeleteId}
          onClose={onCloseConfirmDelete}
          progressStatus={progressStatus}
          onDeleteConfirm={onDeleteConfirm}
        />
      )}
    </div>
  );
};

export default CalendarWrapper;
