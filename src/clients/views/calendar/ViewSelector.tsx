"use client";

import { Fragment, MouseEvent, useCallback, useContext, useState } from "react";

import { Menu, Transition } from "@headlessui/react";
import { CalendarDaysIcon, ChevronDownIcon } from "@heroicons/react/24/outline";

import { CalendarViewType } from "@/types/calendar";
import { isMobileDevice } from "@/utils/responsive";

import classNames from "classnames";
import dayjs from "dayjs";
import dynamic from "next/dynamic";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";

const MobileViewSelector = dynamic(() => import("./MobileViewSelector"), {
  ssr: false,
});

const ViewSelector = () => {
  const { view, setView, selected } = useContext(ManageCalendarContext);
  const [showMobileViewSelector, setShowMobileViewSelector] = useState<
    boolean | null
  >(null);

  const onCloseMobileSelector = useCallback(() => {
    setShowMobileViewSelector(false);
  }, []);

  const onButtonClick = useCallback((event: MouseEvent<any>) => {
    const isMobile = isMobileDevice(navigator.userAgent);
    if (isMobile) {
      setShowMobileViewSelector(true);
      event.preventDefault();
      event.stopPropagation();
    }
  }, []);

  return (
    <div
      className={classNames("self-end bg-white", {
        "hidden md:block": dayjs(selected?.[0]).isValid(),
      })}
    >
      <div className="flex-center gap-2">
        <div className="flex items-center gap-2">
          <p className="text-[10px] md:text-sm tracking-[0.84px]">View:</p>

          <div
            className={classNames("self-end bg-white", {
              "hidden md:block": dayjs(selected?.[0]).isValid(),
            })}
          >
            <div className="text-right">
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button
                    onClick={onButtonClick}
                    className="inline-flex gap-2 w-full items-center justify-center bg-white px-2 md:px-4 py-2 border border-black md:border-[inherit] rounded-2xl text-xs md:text-sm text-black"
                  >
                    <p className="hidden md:block">
                      {view === CalendarViewType.MONTH ? "Month" : "Year"}
                    </p>
                    <CalendarDaysIcon className="md:hidden w-5 h-5" />
                    <ChevronDownIcon
                      className="hidden md:block -mr-1 ml-2 h-[15px] md:h-5 w-[auto]"
                      aria-hidden="true"
                    />
                  </Menu.Button>
                </div>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="z-[999] absolute w-full p-1 right-0 mt-2 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
                    <Menu.Item>
                      {({ active }) => (
                        <div
                          className={classNames(
                            `text-gray-900 text-left w-full block rounded-md px-2 py-2 text-sm cursor-pointer`,
                            {
                              "bg-carolina-blue text-white":
                                view === CalendarViewType.MONTH,
                            }
                          )}
                          onClick={() => setView(CalendarViewType.MONTH)}
                        >
                          Month
                        </div>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <div
                          className={classNames(
                            `text-gray-900 text-left w-full block rounded-md px-2 py-2 text-sm cursor-pointer`,
                            {
                              "bg-carolina-blue text-white":
                                view === CalendarViewType.YEAR,
                            }
                          )}
                          onClick={() => setView(CalendarViewType.YEAR)}
                        >
                          Year
                        </div>
                      )}
                    </Menu.Item>
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
            {showMobileViewSelector !== null && (
              <MobileViewSelector
                onClose={onCloseMobileSelector}
                isShowing={showMobileViewSelector}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewSelector;
