"use client";

import { addCalendarNote, updateCalendarNote } from "@/app/actions/calendar";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import { addServiceProviderNote } from "@/app/actions/service-providers";
import Button from "@/clients/ui/button";
import Modal from "@/clients/ui/modal";
import Textarea from "@/clients/ui/textarea";
import { Nullable, ProgressStatus } from "@/types/common";
import { Comment } from "@/types/service-providers";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
  comment?: Comment;
  onClose: () => void;
};

const EditCalendarNoteModal = ({ onClose, comment, propertyId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [error, setError] = useState<Nullable<string>>(null);
  const [note, setNote] = useState<string>(comment?.content ?? "");

  const onChangeTextInput = useCallback((event: any) => {
    const { name, value } = event.target;
    setNote(value);
    if (value.trim().length === 0) {
      setError("Note is required");
    } else {
      setError(null);
    }
  }, []);

  const onSave = useCallback(() => {
    console.log("errr", error, note);
    if (error && error?.length !== 0) {
      return;
    }

    if (note.trim().length === 0) {
      setError("Note is required");
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    if (comment?.comment_uuid) {
      updateCalendarNote(propertyId, comment.comment_uuid, {
        content: note,
      })
        .then((data) => {
          revalidateTagByName(`calendar-notes-${propertyId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success("Note updated successfully!");
          onClose();
        })
        .catch((err) => {
          console.log("error is", err);
          toast.error(err.message);
          setProgressStatus(ProgressStatus.FAILED);
        });
    } else {
      addCalendarNote(propertyId, {
        content: note,
      })
        .then((data) => {
          revalidateTagByName(`calendar-notes-${propertyId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success("Note added successfully!");
          onClose();
        })
        .catch((err) => {
          console.log("error is", err);
          toast.error(err.message);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [comment?.comment_uuid, error, note, onClose, propertyId]);

  return (
    <Modal open onClose={onClose} className="p-4">
      <p className="mb-4 font-bold text-center">
        {comment?.comment_uuid ? `Edit Note` : `Add Note`}
      </p>
      <Textarea
        className="text-sm w-full p-2"
        rows={5}
        value={note}
        onChange={onChangeTextInput}
        helperText={error ?? ""}
        error={!!error}
      />
      <div className="flex gap-2 mt-4 justify-between">
        <Button
          intent="outline"
          className="text-xs font-normal"
          onClick={onClose}
          disabled={progressStatus === ProgressStatus.LOADING}
        >
          Close
        </Button>
        <Button
          className="text-xs"
          onClick={onSave}
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save
        </Button>
      </div>
    </Modal>
  );
};

export default EditCalendarNoteModal;
