"use client";

import { useState } from "react";

import { ChevronRightIcon } from "@heroicons/react/24/outline";

import ManageCalendarHeader from "./Manage/ManageCalendarHeader";
import MobileYearView from "./MobileYearView";
import SlidePane from "../common/SlidePane";

type Props = {
  isShowing?: boolean;
  onClose: () => void;
};

const MobileViewSelector = ({ onClose, isShowing }: Props) => {
  const [yearViewSelected, setYearViewSelected] = useState<boolean>(false);
  return (
    <>
      <SlidePane isShowing={isShowing}>
        <div className="bg-white w-full h-full p-4 shadow-">
          <ManageCalendarHeader title="Calender View" onClose={onClose} />
          <div
            className="p-4 border border-black rounded-2xl my-4 flex-center-between cursor-pointer"
            onClick={() => setYearViewSelected(true)}
          >
            <p className="text-sm font-bold">By Year</p>
            <ChevronRightIcon className="h-[14px] w-[auto]" />
          </div>
          <div
            className="p-4 border border-black rounded-2xl flex-center-between cursor-pointer"
            onClick={onClose}
          >
            <p className="text-sm font-bold">By Month</p>
            <ChevronRightIcon className="h-[14px] w-[auto]" />
          </div>
        </div>
      </SlidePane>
      <SlidePane isShowing={yearViewSelected}>
        <MobileYearView onClose={() => setYearViewSelected(false)} />
      </SlidePane>
    </>
  );
};

export default MobileViewSelector;
