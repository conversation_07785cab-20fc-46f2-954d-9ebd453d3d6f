"use client";

import { useMemo } from "react";

import { getMonthYearsList } from "@/utils/calendar";

import classNames from "classnames";
import dayjs from "dayjs";
import { useSearchParams } from "next/navigation";

import ManageCalendarHeader from "./Manage/ManageCalendarHeader";
import YearViewMonthItem from "./YearViewMonthItem";
import StatusPill from "@/app/components/calendar/StatusPill";
import { BlockedType, CalendarLeasedType } from "@/types/calendar";

type Props = {
  onClose: () => void;
};

const MobileYearView = ({ onClose }: Props) => {
  const prevYearList = useMemo(() => getMonthYearsList(dayjs().year() - 1), []);
  const monthYearList = useMemo(() => getMonthYearsList(dayjs().year()), []);
  const nextYearList = useMemo(() => getMonthYearsList(dayjs().year() + 1), []);
  const nextYear2List = useMemo(
    () => getMonthYearsList(dayjs().year() + 2),
    []
  );
  const searchParams = useSearchParams();
  const hasSelectionStart = useMemo(
    () => searchParams.has("selectionStart"),
    [searchParams]
  );

  return (
    <div
      className={classNames("bg-white w-full h-full shadow-inner pb-[40px]", {
        "!pb-8": hasSelectionStart,
      })}
    >
      <div className="p-4">
        <ManageCalendarHeader
          title="Year View"
          onClose={onClose}
          onBack={onClose}
        />
      </div>

      <div className="overflow-scroll h-full p-2 pb-[50px]">
        <div className="flex-center-between mx-2 my-2">
          <p className="text-[19px] font-bold">{dayjs().year() - 1}</p>
          <div className="flex-center md:justify-end gap-2 md:gap-8">
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.LEASED}
              title="Leased"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OWNER_TIME}
              title="Pending"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OTHER}
              title="Blocked"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {prevYearList.map((_month, index) => (
            <YearViewMonthItem
              key={index}
              month={_month.month}
              year={_month.year}
            />
          ))}
        </div>
        <div className="flex-center-between mx-2 my-2">
          <p className="text-[19px] font-bold">{dayjs().year()}</p>
          <div className="flex-center md:justify-end gap-2 md:gap-8">
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.LEASED}
              title="Leased"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OWNER_TIME}
              title="Pending"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OTHER}
              title="Blocked"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {monthYearList.map((_month, index) => (
            <YearViewMonthItem
              key={index}
              month={_month.month}
              year={_month.year}
            />
          ))}
        </div>
        <div className="flex-center-between mx-2 my-2">
          <p className="text-[19px] font-bold">{dayjs().year() + 1}</p>
          <div className="flex-center md:justify-end gap-2 md:gap-8">
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.LEASED}
              title="Leased"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OWNER_TIME}
              title="Pending"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OTHER}
              title="Blocked"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {nextYearList.map((_month, index) => (
            <YearViewMonthItem
              key={index}
              month={_month.month}
              year={_month.year}
            />
          ))}
        </div>
        <div className="flex-center-between mx-2 my-2">
          <p className="text-[19px] font-bold">{dayjs().year() + 2}</p>
          <div className="flex-center md:justify-end gap-2 md:gap-8">
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.LEASED}
              title="Leased"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OWNER_TIME}
              title="Pending"
            />
            <StatusPill
              className="py-1 md:py-0"
              type={BlockedType.OTHER}
              title="Blocked"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {nextYear2List.map((_month, index) => (
            <YearViewMonthItem
              key={index}
              month={_month.month}
              year={_month.year}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileYearView;
