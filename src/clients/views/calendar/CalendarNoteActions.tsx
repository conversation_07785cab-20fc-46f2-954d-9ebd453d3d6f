'use client';

import { deleteCalendarNote } from '@/app/actions/calendar';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import { ProgressStatus } from '@/types/common';
import { Comment } from '@/types/service-providers';
import { PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import dynamic from 'next/dynamic';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

const DeleteConfirm = dynamic(
  () => import('@/clients/components/common/DeleteConfirm')
);

const EditCalendarNoteModal = dynamic(() => import('./EditCalendarNoteModal'));

type Props = {
  propertyId: number;
  comment: Comment;
};

const CalendarNoteActions = ({ comment, propertyId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [showConfirm, setShowConfirm] = useState<boolean>(false);
  const [showEdit, setShowEdit] = useState<boolean>(false);

  const onDeleteNoteConfirm = useCallback(() => {
    if (!comment?.comment_uuid) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    deleteCalendarNote(propertyId, comment.comment_uuid)
      .then((_data) => {
        revalidateTagByName(`calendar-notes-${propertyId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Note deleted successfully!');
        setShowConfirm(false);
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
        setShowConfirm(false);
      });
  }, [comment.comment_uuid, propertyId]);

  const onClickEdit = useCallback(() => {
    setShowEdit(true);
  }, []);

  const onCloseEdit = useCallback(() => {
    setShowEdit(false);
  }, []);

  const onClickDelete = useCallback(() => {
    setShowConfirm(true);
  }, []);

  const onCloseDelete = useCallback(() => {
    setShowConfirm(false);
  }, []);

  return (
    <>
      <div className="flex items-center flex-col md:flex-row gap-2">
        <span
          onClick={onClickEdit}
          className="w-6 align-middle text-center cursor-pointer"
        >
          <PencilIcon className="w-5 h-5" />
        </span>
        <span
          onClick={onClickDelete}
          className="w-6 align-middle text-center cursor-pointer"
        >
          <TrashIcon className="w-5 h-5 text-error" />
        </span>
      </div>
      {showConfirm && (
        <DeleteConfirm
          title="Are you sure, you want to delete the note?"
          progressStatus={progressStatus}
          onDelete={onDeleteNoteConfirm}
          onClose={onCloseDelete}
        />
      )}

      {showEdit && (
        <EditCalendarNoteModal
          comment={comment}
          onClose={onCloseEdit}
          propertyId={propertyId}
        />
      )}
    </>
  );
};

export default CalendarNoteActions;
