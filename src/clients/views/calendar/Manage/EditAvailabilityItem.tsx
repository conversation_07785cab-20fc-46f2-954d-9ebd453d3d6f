"use client";

import { ReactNode, memo } from "react";

import Button from "@/clients/ui/button";
import { BlockedType } from "@/types/calendar";
import { getAvailabilityTitle } from "@/utils/calendar";

import classNames from "classnames";
import dayjs from "dayjs";

import ManageCalendarHeader from "./ManageCalendarHeader";
import ManageCalendarSelectedDates from "@/app/components/calendar/ManageCalendarSelectedDates";
import { PropertyAvailability } from "@/types/calendar";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectedData: PropertyAvailability;
  onClickEdit: () => void;
};

const EditAvailabilityItem = ({
  onBack,
  onClose,
  selectedData,
  onClickEdit,
}: Props) => {
  return (
    <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-[90px] p-4 md:p-0">
      <ManageCalendarHeader
        title="Edit Booking"
        onBack={onBack}
        onClose={onClose}
      />
      <ManageCalendarSelectedDates
        selectionStart={selectedData.from_date}
        selectionEnd={selectedData.to_date}
      />
      <div className="p-4 border rounded-2xl">
        <p
          className={classNames("text-xs md:text-[10px] font-bold", {
            "text-carolina-blue": selectedData.type === BlockedType.LEASED,
            "text-pending": selectedData.type === BlockedType.PENDING,
          })}
        >
          {getAvailabilityTitle(selectedData.type)}
        </p>
        <p className="text-xs md:text-[10px]">
          {dayjs(selectedData.from_date).format(`MMM D, YYYY`)} -{" "}
          {dayjs(selectedData.to_date).format(`MMM D, YYYY`)}{" "}
        </p>
        {selectedData.renter_name && (
          <p className="text-[19px] font-bold mt-2">
            {selectedData.renter_name}
          </p>
        )}
      </div>
      <Button
        intent="secondary"
        className="rounded-lg text-sm font-normal"
        onClick={onClickEdit}
      >
        Edit Booking
      </Button>
    </div>
  );
};

export default memo(EditAvailabilityItem);
