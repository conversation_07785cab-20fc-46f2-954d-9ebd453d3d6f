'use client';

import { ReactNode, memo } from 'react';

import { ChevronLeftIcon, XMarkIcon } from '@heroicons/react/24/outline';

type Props = {
  title: string | ReactNode;
  onBack?: () => void;
  onClose?: () => void;
};

const ManageCalendarHeader = ({ onBack, onClose, title }: Props) => {
  return (
    <>
      <div className='w-full relative flex-center-center text-[19px] font-bold my-2'>
        {onBack && (
          <span onClick={onBack} className='cursor-pointer absolute left-0'>
            <ChevronLeftIcon className='w-6 h-6' />
          </span>
        )}
        <div className='w-full text-center px-6 md:px-4'>{title}</div>
        {onClose && (
          <span onClick={onClose} className='cursor-pointer absolute right-0'>
            <XMarkIcon className='w-6 h-6' />
          </span>
        )}
      </div>
    </>
  );
};

export default memo(ManageCalendarHeader);
