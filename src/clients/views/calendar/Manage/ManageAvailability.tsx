"use client";

import { ReactNode, useCallback, useMemo, useState } from "react";

import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import { checkIfTwoRangesOverlaps } from "@/utils/calendar";

import dayjs from "dayjs";

import EditAvailabilityItem from "./EditAvailabilityItem";
import ManageAvailabilityForm from "./ManageAvailabilityForm";
import ManageAvailabilityItems from "./ManageAvailabilityItems";
import ManageCalendarHeader from "./ManageCalendarHeader";
import ManageCalendarSelectedDates from "@/app/components/calendar/ManageCalendarSelectedDates";
import SlidePane from "../../common/SlidePane";
import { PropertyAvailability } from "@/types/calendar";
import { Nullable } from "@/types/common";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectionStart: string;
  selectionEnd: string;
  propertyId: number;
  selectedAvailability?: PropertyAvailability;
  availabilityData: PropertyAvailability[];
};

const ManageAvailability = ({
  onBack,
  onClose,
  selectedDatesNode,
  selectionEnd,
  selectionStart,
  propertyId,
  selectedAvailability,
  availabilityData: data,
}: Props) => {
  const [selectedId, setSelectedId] = useState<Nullable<string>>(null);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [defaultIsBooked, setDeafaultIsBooked] = useState<boolean>(false);
  const [defaultIsBlocked, setDeafaultIsBlocked] = useState<boolean>(false);

  const showAdd = useMemo(
    () =>
      data.filter((_data) =>
        checkIfTwoRangesOverlaps(
          [selectionStart, selectionEnd],
          [_data.from_date, _data.to_date]
        )
      ).length === 0,
    [data, selectionEnd, selectionStart]
  );

  const onAddBlock = useCallback(() => {
    setIsEdit(true);
    setDeafaultIsBlocked(true);
  }, []);

  const onAddBooking = useCallback(() => {
    setIsEdit(true);
    setDeafaultIsBlocked(true);
    setDeafaultIsBooked(true);
  }, []);

  const selectedData = useMemo(
    () =>
      !!selectedAvailability
        ? selectedAvailability
        : data?.find((_avail) => _avail.availability_uuid === selectedId),
    [selectedAvailability, data, selectedId]
  );

  const onSelectId = useCallback((id: string) => {
    setSelectedId(id);
  }, []);

  const onCloseEdit = useCallback(() => {
    setSelectedId(null);
  }, []);

  const onDeleteSuccessfully = useCallback(() => {
    revalidateTagByName(`property-details-${propertyId}`);
  }, [propertyId]);

  const onClickEdit = useCallback(() => {
    setIsEdit(true);
  }, []);

  return (
    <>
      <div className="p-4 md:p-0 pb-0">
        <ManageCalendarHeader
          title="Availability"
          onBack={onBack}
          onClose={onClose}
        />
      </div>
      <div className="overflow-scroll h-full flex flex-col gap-2 p-4 md:p-0 pb-[100px] md:pb-[90px]">
        {!!selectedData ? (
          <ManageCalendarSelectedDates
            selectionStart={selectedData.from_date}
            selectionEnd={selectedData.to_date}
          />
        ) : (
          selectedDatesNode
        )}
        {selectedAvailability ? (
          <ManageAvailabilityForm
            onBack={onBack}
            onClose={onClose}
            selectedDatesNode={selectedDatesNode}
            selectionStart={selectionStart}
            selectionEnd={selectionEnd}
            propertyId={propertyId}
            selectedData={selectedAvailability}
            onDeleteSuccessfully={onDeleteSuccessfully}
          />
        ) : (
          <>
            {!!data && data.length > 0 && !isEdit ? (
              <>
                <ManageAvailabilityItems
                  selectionStart={selectionStart}
                  selectionEnd={selectionEnd}
                  propertyId={propertyId}
                  data={data}
                  onSelectId={onSelectId}
                />
                {showAdd && (
                  <>
                    <Button
                      intent="outline"
                      className="rounded-lg text-sm font-normal border-black"
                      onClick={onAddBlock}
                    >
                      Add Block
                    </Button>
                    <Button
                      className="rounded-lg text-sm font-normal border-black"
                      onClick={onAddBooking}
                    >
                      Add Booking
                    </Button>
                  </>
                )}
              </>
            ) : (
              <ManageAvailabilityForm
                onBack={onBack}
                onClose={onClose}
                selectedDatesNode={selectedDatesNode}
                selectionStart={selectionStart}
                selectionEnd={selectionEnd}
                propertyId={propertyId}
                selectedData={selectedData}
                onDeleteSuccessfully={onDeleteSuccessfully}
                defaultIsBlocked={defaultIsBlocked}
                defaultIsBooked={defaultIsBooked}
              />
            )}
          </>
        )}
      </div>
      {selectedId !== null && !isEdit && (
        <SlidePane isShowing={!!selectedId} wrapperClassName="md:!inset-0">
          {selectedData && (
            <EditAvailabilityItem
              onBack={onCloseEdit}
              onClose={onCloseEdit}
              selectedDatesNode={selectedDatesNode}
              selectedData={selectedData}
              onClickEdit={onClickEdit}
            />
          )}
        </SlidePane>
      )}
    </>
  );
};

export default ManageAvailability;
