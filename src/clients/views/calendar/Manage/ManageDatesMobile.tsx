"use client";

import { ReactNode, useCallback, useMemo, useState } from "react";

import {
  CalendarIcon,
  ChevronRightIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";

import SlidePane from "@/clients/views/common/SlidePane";
import Button from "@/clients/ui/button";

import DateSelectorMobile from "./DateSelectorMobile";
import ManageAvailability from "./ManageAvailability";
import ManageBookingRules from "./ManageBookingRules";
import ManageCalendarHeader from "./ManageCalendarHeader";
import ManageRates from "./ManageRates";
import { PropertyAvailability } from "@/types/calendar";
import { Property } from "@/types/property";
import usePropertyRatesAndRules from "@/hooks/usePropertyRatesAndRules";
import { formatRateAndRules } from "@/utils/rates";
import { currencyFormatterRound } from "@/utils/common";

type Props = {
  isShowing?: boolean;
  onClose: () => void;
  onBack?: () => void;
  selectedDatesNode: ReactNode;
  selectionStart?: string;
  selectionEnd?: string;
  propertyId: number;
  propertyData: Property;
  availabilityData: PropertyAvailability[];
};

const ManageDatesMobile = ({
  isShowing,
  onClose,
  onBack,
  selectedDatesNode,
  selectionStart = "",
  selectionEnd = "",
  propertyId,
  propertyData,
  availabilityData,
}: Props) => {
  console.log("selection start and end", selectionStart, selectionEnd);
  const [showManageAvailabilityMobile, setShowManageAvailabilityMobile] =
    useState<boolean | null>(null);
  const [showManageRates, setShowManageRates] = useState<boolean | null>(null);
  const [showManageBookingRules, setShowManageBookingRules] = useState<
    boolean | null
  >(null);
  const [showDatePicker, setShowDatePicker] = useState<boolean | null>(null);
  const { data: ratesAndRules, isLoading } = usePropertyRatesAndRules(
    propertyId,
    selectionStart,
    selectionEnd
  );

  const formattedRatesAndRules = useMemo(
    () =>
      formatRateAndRules(
        ratesAndRules ?? [],
        propertyData.requirement.min_night_stay
      ),
    [propertyData.requirement.min_night_stay, ratesAndRules]
  );

  const onCloseDateSelectorMobile = useCallback(() => {
    setShowDatePicker(false);
    setTimeout(() => {
      setShowDatePicker(null);
    }, 160);
  }, []);

  return (
    <>
      <SlidePane isShowing={isShowing}>
        <div className="bg-white w-full h-full p-4 shadow-inner flex flex-col gap-2">
          <ManageCalendarHeader
            title="Manage Dates"
            onClose={onClose}
            onBack={onBack}
          />
          <div
            className="cursor-pointer"
            onClick={() => setShowDatePicker(true)}
          >
            {selectedDatesNode}
          </div>
          <Button
            intent="secondary"
            className="flex text-xs rounded-2xl font-normal w-full px-4 py-4 justify-between hover:border-black"
            onClick={() => setShowManageAvailabilityMobile(true)}
          >
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-6 h-6" />
              Availability
            </div>
            <ChevronRightIcon
              className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
              aria-hidden="true"
            />
          </Button>
          <Button
            intent="secondary"
            className="flex text-xs rounded-2xl font-normal w-full px-4 py-4 justify-between hover:border-black"
            onClick={() => setShowManageRates(true)}
          >
            <div className="text-left">
              <div className="flex items-center gap-2">
                <CurrencyDollarIcon className="w-6 h-6" />
                Rates
              </div>
              {!isLoading && formattedRatesAndRules?.weekly_amount && (
                <span className="pl-2 font-medium">
                  {currencyFormatterRound.format(
                    formattedRatesAndRules?.weekly_amount ?? 0
                  )}
                  /wk
                </span>
              )}
            </div>
            <ChevronRightIcon
              className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
              aria-hidden="true"
            />
          </Button>
          <Button
            intent="secondary"
            className="flex text-xs rounded-2xl font-normal w-full px-4 py-4 justify-between hover:border-black"
            onClick={() => setShowManageBookingRules(true)}
          >
            <div className="flex items-center gap-2">
              <ClipboardDocumentListIcon className="w-6 h-6" />
              Booking Rules
            </div>
            <ChevronRightIcon
              className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
              aria-hidden="true"
            />
          </Button>
        </div>
      </SlidePane>
      {showManageAvailabilityMobile !== null && (
        <SlidePane isShowing={showManageAvailabilityMobile}>
          <div className="bg-white w-full h-full shadow-inner">
            <ManageAvailability
              onClose={() => {
                setShowManageAvailabilityMobile(false);
                onClose();
              }}
              onBack={() => setShowManageAvailabilityMobile(false)}
              selectedDatesNode={selectedDatesNode}
              selectionStart={selectionStart}
              selectionEnd={selectionEnd}
              propertyId={propertyId}
              availabilityData={availabilityData}
            />
          </div>
        </SlidePane>
      )}
      {showDatePicker !== null && (
        <DateSelectorMobile
          isShowing={showDatePicker}
          onClose={onCloseDateSelectorMobile}
          onBack={onCloseDateSelectorMobile}
          selectionStart={selectionStart}
          selectionEnd={selectionEnd}
        />
      )}
      {showManageRates !== null && (
        <SlidePane isShowing={showManageRates}>
          <div className="bg-white w-full h-full shadow-inner">
            <ManageRates
              selectedDatesNode={selectedDatesNode}
              onClose={() => {
                setShowManageRates(false);
              }}
              onBack={() => setShowManageRates(false)}
              selectionStart={selectionStart}
              selectionEnd={selectionEnd}
              propertyId={propertyId}
              propertyData={propertyData}
            />
          </div>
        </SlidePane>
      )}
      {showManageBookingRules !== null && (
        <SlidePane isShowing={showManageBookingRules}>
          <div className="bg-white w-full h-full shadow-inner">
            <ManageBookingRules
              selectedDatesNode={selectedDatesNode}
              onClose={() => {
                setShowManageBookingRules(false);
              }}
              onBack={() => setShowManageBookingRules(false)}
              selectionStart={selectionStart}
              selectionEnd={selectionEnd}
              propertyId={propertyId}
              propertyData={propertyData}
            />
          </div>
        </SlidePane>
      )}
    </>
  );
};

export default ManageDatesMobile;
