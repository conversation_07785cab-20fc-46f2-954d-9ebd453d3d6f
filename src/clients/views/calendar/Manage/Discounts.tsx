"use client";

import { FormEvent, ReactNode, useCallback, useState } from "react";

import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import { PropertyRentalRates } from "@/types/calendar";

import classNames from "classnames";

import ManageCalendarHeader from "./ManageCalendarHeader";
import { Discount, DiscountType } from "./ManageRates";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectedDiscounts: { [x: string]: Discount };
  setSelectedDiscounts: (selected: { [x: string]: Discount }) => void;
};

const Discounts = ({
  onBack,
  onClose,
  selectedDatesNode,
  selectedDiscounts,
  setSelectedDiscounts,
}: Props) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [discounts, setDiscounts] = useState<string[]>(
    Object.keys(selectedDiscounts)
  );
  const [earlyBirdDiscount, setEarlyBirdDiscount] = useState<Discount>({
    percentage: selectedDiscounts[DiscountType.EARLY_BIRD]?.percentage ?? 0,
    months: selectedDiscounts[DiscountType.EARLY_BIRD]?.months ?? 0,
    name: DiscountType.EARLY_BIRD,
  });
  const [lastMinuteDiscount, setLastMinuteDiscount] = useState<Discount>({
    percentage: selectedDiscounts[DiscountType.LAST_MINUTE]?.percentage ?? 0,
    months: selectedDiscounts[DiscountType.LAST_MINUTE]?.months ?? 0,
    name: DiscountType.LAST_MINUTE,
  });
  const [customDiscount, setCustomDiscount] = useState<Discount>({
    percentage: selectedDiscounts[DiscountType.CUSTOM]?.percentage ?? 0,
    months: selectedDiscounts[DiscountType.CUSTOM]?.months ?? 0,
    name: DiscountType.CUSTOM,
  });

  const numberInputOnWheelPreventChange = useCallback((e: any) => {
    // Prevent the input value change
    e.target.blur();

    // Refocus immediately, on the next tick (after the current function is done)
    setTimeout(() => {
      e.target.focus();
    }, 0);
  }, []);

  const preSubmitCheck = useCallback(() => {
    const err: { [key: string]: string } = discounts.reduce((acc, curr) => {
      if (curr === DiscountType.CUSTOM) {
        acc = {
          ...acc,
          [curr]:
            customDiscount.months === 0 || customDiscount.percentage === 0
              ? "Values cannot be zero"
              : "",
        };
      } else if (curr === DiscountType.EARLY_BIRD) {
        acc = {
          ...acc,
          [curr]:
            earlyBirdDiscount.months === 0 || earlyBirdDiscount.percentage === 0
              ? "Values cannot be zero"
              : "",
        };
      } else {
        acc = {
          ...acc,
          [curr]:
            lastMinuteDiscount.months === 0 ||
            lastMinuteDiscount.percentage === 0
              ? "Values cannot be zero"
              : "",
        };
      }

      return acc;
    }, {});

    setErrors(err);
    return err;
  }, [
    customDiscount.months,
    customDiscount.percentage,
    discounts,
    earlyBirdDiscount.months,
    earlyBirdDiscount.percentage,
    lastMinuteDiscount.months,
    lastMinuteDiscount.percentage,
  ]);

  const onChangeEarlyBird = useCallback(
    (event: FormEvent<HTMLInputElement>) => {
      const { name, value } = event.currentTarget;
      setEarlyBirdDiscount({
        ...earlyBirdDiscount,
        [name]: value,
      });
      if (!discounts.includes(DiscountType.EARLY_BIRD)) {
        setDiscounts([...discounts, DiscountType.EARLY_BIRD]);
      }

      setTimeout(() => {
        preSubmitCheck();
      }, 0);
    },
    [discounts, earlyBirdDiscount, preSubmitCheck]
  );

  const onChangeLastMinute = useCallback(
    (event: FormEvent<HTMLInputElement>) => {
      const { name, value } = event.currentTarget;
      setLastMinuteDiscount({
        ...lastMinuteDiscount,
        [name]: value,
      });
      if (!discounts.includes(DiscountType.LAST_MINUTE)) {
        setDiscounts([...discounts, DiscountType.LAST_MINUTE]);
      }

      setTimeout(() => {
        preSubmitCheck();
      }, 0);
    },
    [discounts, lastMinuteDiscount, preSubmitCheck]
  );
  const onChangeCustom = useCallback(
    (event: FormEvent<HTMLInputElement>) => {
      const { name, value } = event.currentTarget;
      setCustomDiscount({
        ...customDiscount,
        [name]: value,
      });
      if (!discounts.includes(DiscountType.CUSTOM)) {
        setDiscounts([...discounts, DiscountType.CUSTOM]);
      }

      setTimeout(() => {
        preSubmitCheck();
      }, 0);
    },
    [customDiscount, discounts, preSubmitCheck]
  );

  const onSelectDiscount = useCallback(
    (discount: Discount, e: any) => {
      if (e.target.tagName === "INPUT") {
        return;
      }
      if (discounts.includes(discount.name)) {
        setDiscounts(discounts.filter((_name) => _name !== discount.name));

        return;
      }
      setDiscounts([...discounts, discount.name]);
    },
    [discounts]
  );

  const getDiscountValues = useCallback(
    (key: string) => {
      if (key === "") return "";
      if (key === DiscountType.CUSTOM) {
        return customDiscount;
      } else if (key === DiscountType.EARLY_BIRD) {
        return earlyBirdDiscount;
      } else {
        return lastMinuteDiscount;
      }
    },
    [customDiscount, earlyBirdDiscount, lastMinuteDiscount]
  );

  const onConfirm = useCallback(() => {
    if (discounts.length === 0) {
      onClose();
      return;
    }
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }

    const data = discounts.reduce((acc, curr) => {
      return (acc = {
        ...acc,
        [curr]: getDiscountValues(curr),
      });
    }, {});

    setSelectedDiscounts(data);
    onClose();
  }, [
    discounts,
    preSubmitCheck,
    setSelectedDiscounts,
    onClose,
    getDiscountValues,
  ]);

  return (
    <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-[90px] p-4 md:p-0">
      <ManageCalendarHeader
        title="Discounts"
        onBack={onBack}
        onClose={onClose}
      />
      <p className="text-xs text-center">
        You have {Object.keys(selectedDiscounts).length} active discounts for
        this date range
      </p>
      {selectedDatesNode}
      <div className="flex flex-col gap-2 md:gap-4">
        <div>
          <div
            className={classNames(
              "flex-center p-4 border rounded-2xl hover:border-black cursor-pointer",
              {
                "!border-carolina-blue": discounts.includes(
                  DiscountType.CUSTOM
                ),
              }
            )}
            onClick={(e) => onSelectDiscount(customDiscount, e)}
          >
            <p className="text-xs w-[40%]">Custom</p>
            <span className="flex-center-center w-[30%]">
              <Input
                name="percentage"
                wrapperclassName="flex-center gap-1"
                className="text-sm font-bold z-[99] w-[20px] border-0 px-0 py-0"
                value={Number(customDiscount.percentage).toString()}
                onChange={onChangeCustom}
                type="number"
                max={100}
                min={0}
                onWheel={numberInputOnWheelPreventChange}
              />
              %
            </span>
            <Input
              name="months"
              wrapperclassName="w-[15%]"
              className="text-sm font-bold z-[99] w-full border-0 px-0 py-0"
              value={Number(customDiscount.months).toString()}
              onChange={onChangeCustom}
              type="number"
              min={0}
              onWheel={numberInputOnWheelPreventChange}
            />
            <p className="w-[15%] text-[10px]">nights or more</p>
          </div>
          {errors?.[DiscountType.CUSTOM] && (
            <p className="pl-2 text-error text-[10px]">
              {errors?.[DiscountType.CUSTOM]}
            </p>
          )}
        </div>
        <div>
          <div
            className={classNames(
              "flex-center p-4 border rounded-2xl hover:border-black cursor-pointer",
              {
                "!border-carolina-blue": discounts.includes(
                  DiscountType.EARLY_BIRD
                ),
              }
            )}
            onClick={(e) => onSelectDiscount(earlyBirdDiscount, e)}
          >
            <p className="text-xs mb-0 font-bold md:mb-2 w-[40%]">
              Early bird discount
            </p>
            <span className="flex-center-center w-[30%]">
              <Input
                name="percentage"
                wrapperclassName="flex-center gap-1"
                className="text-sm font-bold z-[99] w-[20px] border-0 px-0 py-0"
                value={Number(earlyBirdDiscount.percentage).toString()}
                onChange={onChangeEarlyBird}
                type="number"
                max={100}
                min={0}
                onWheel={numberInputOnWheelPreventChange}
              />
              %
            </span>
            <Input
              name="months"
              wrapperclassName="w-[15%]"
              className="text-sm font-bold z-[99] w-full border-0 px-0 py-0"
              value={Number(earlyBirdDiscount.months).toString()}
              onChange={onChangeEarlyBird}
              type="number"
              min={0}
              onWheel={numberInputOnWheelPreventChange}
            />
            <p className="w-[15%] text-[10px]">Months before arrival</p>
          </div>
          {errors?.[DiscountType.EARLY_BIRD] && (
            <p className="pl-2 text-error text-[10px]">
              {errors?.[DiscountType.EARLY_BIRD]}
            </p>
          )}
        </div>
        <div>
          <div
            className={classNames(
              "flex-center p-4 border rounded-2xl hover:border-black cursor-pointer",
              {
                "!border-carolina-blue": discounts.includes(
                  DiscountType.LAST_MINUTE
                ),
              }
            )}
            onClick={(e) => onSelectDiscount(lastMinuteDiscount, e)}
          >
            <p className="text-xs mb-0 font-bold md:mb-2 w-[40%]">
              Last minute discount
            </p>
            <span className="flex-center-center w-[30%]">
              <Input
                name="percentage"
                wrapperclassName="flex-center gap-1"
                className="text-sm font-bold z-[99] w-[20px] border-0 px-0 py-0"
                value={Number(lastMinuteDiscount.percentage).toString()}
                onChange={onChangeLastMinute}
                type="number"
                max={100}
                min={0}
                onWheel={numberInputOnWheelPreventChange}
              />
              %
            </span>
            <Input
              name="months"
              wrapperclassName="w-[15%]"
              className="text-sm font-bold z-[99] w-full border-0 px-0 py-0"
              value={Number(lastMinuteDiscount.months).toString()}
              onChange={onChangeLastMinute}
              type="number"
              min={0}
              onWheel={numberInputOnWheelPreventChange}
            />
            <p className="w-[15%] text-[10px]">days before arrival</p>
          </div>
          {errors?.[DiscountType.LAST_MINUTE] && (
            <p className="pl-2 text-error text-[10px]">
              {errors?.[DiscountType.LAST_MINUTE]}
            </p>
          )}
        </div>
      </div>
      <Button className="rounded-lg text-sm font-normal" onClick={onConfirm}>
        Confirm
      </Button>
      <Button
        intent="secondary"
        className="rounded-lg text-sm font-normal"
        onClick={onBack}
      >
        Cancel
      </Button>
    </div>
  );
};

export default Discounts;
