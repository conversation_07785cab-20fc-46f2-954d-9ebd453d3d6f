'use client';

import { memo } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';

type Props = {
  agreementName: string;
  onRemove: () => void;
  agreementFileLink?: string;
};

const AgreementFile = ({ agreementName, onRemove, agreementFileLink }: Props) => {
  return (
    <div className='p-4 border rounded-2xl'>
      <p className='text-xs'>Rental agreement</p>
      <div className='flex-center-between'>
        {agreementFileLink ? (
          <a href={agreementFileLink} target='_blank' className='underline overflow-hidden'>
            <p className='text-xs font-bold max-w-[95%] truncate'>{agreementName}</p>
          </a>
        ) : (
          <p className='text-xs font-bold max-w-[95%] truncate'>{agreementName}</p>
        )}

        <XCircleIcon className='w-6 h-6 cursor-pointer' onClick={onRemove} />
      </div>
    </div>
  );
};

export default memo(AgreementFile);
