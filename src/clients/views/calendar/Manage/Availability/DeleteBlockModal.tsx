"use client";

import { memo } from "react";

import { Nullable, ProgressStatus } from "@/types/common";

import ManageCalendarHeader from "../ManageCalendarHeader";
import Modal from "@/clients/ui/modal";
import Button from "@/clients/ui/button";

type Props = {
  open: boolean;
  onClose: () => void;
  onDeleteConfirm: () => void;
  progressStatus: Nullable<ProgressStatus>;
};

const DeleteBlockModal = ({
  open,
  onClose,
  onDeleteConfirm,
  progressStatus,
}: Props) => {
  return (
    <Modal open={open} className="p-8">
      <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-0 p-4 md:p-0">
        <ManageCalendarHeader
          title="Are you sure you want to delete this block?"
          onClose={onClose}
        />
        <div className="flex-center-between">
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal border-black"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="rounded-lg text-sm font-normal text-error"
            intent="outline"
            isLoading={progressStatus === ProgressStatus.LOADING}
            onClick={onDeleteConfirm}
          >
            Yes, Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default memo(DeleteBlockModal);
