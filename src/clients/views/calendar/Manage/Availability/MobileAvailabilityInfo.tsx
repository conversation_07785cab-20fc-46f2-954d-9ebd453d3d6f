"use client";

import { memo, useCallback, useMemo } from "react";

import { BlockedType } from "@/types/calendar";

import classNames from "classnames";
import { PropertyAvailability } from "@/types/calendar";

type Props = {
  availabilityData: PropertyAvailability[];
  onViewEventDetailsMobile: (id?: string) => void;
};

const MobileAvailabilityInfo = ({
  availabilityData,
  onViewEventDetailsMobile,
}: Props) => {
  const isLeased = useMemo(
    () => availabilityData[0]?.type === BlockedType.LEASED,
    [availabilityData]
  );
  const isPeding = useMemo(
    () => availabilityData[0]?.type === BlockedType.PENDING,
    [availabilityData]
  );
  const isBlocked = useMemo(
    () =>
      availabilityData[0]?.type !== BlockedType.PENDING &&
      availabilityData[0]?.type !== BlockedType.LEASED,
    [availabilityData]
  );

  const onViewDetails = useCallback(() => {
    if (availabilityData.length === 1) {
      onViewEventDetailsMobile(availabilityData[0].availability_uuid);
    } else {
      onViewEventDetailsMobile();
    }
  }, [availabilityData, onViewEventDetailsMobile]);

  return (
    <>
      {availabilityData.length === 1 ? (
        <div className="text-xs flex-center-center gap-2 px-4 mb-2">
          <div
            className={classNames("w-4 h-4 rounded-full", {
              "bg-carolina-blue": isLeased,
              "bg-pending": isPeding,
              "bg-gray-blocked": isBlocked,
            })}
          />
          {isLeased ? `Leased` : isPeding ? `Pending` : `Blocked`}
          <p>
            {availabilityData[0].renter_name ?? "Renter Name"}
            <span
              className="text-carolina-blue ml-1 font-medium"
              onClick={onViewDetails}
            >
              View Details
            </span>
          </p>
        </div>
      ) : (
        <div className="text-xs text-center">
          There are multiple events.
          <span
            className="text-carolina-blue ml-1 font-medium"
            onClick={onViewDetails}
          >
            View Details
          </span>
        </div>
      )}
    </>
  );
};

export default memo(MobileAvailabilityInfo);
