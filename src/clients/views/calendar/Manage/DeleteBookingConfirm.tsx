"use client";

import { memo } from "react";

import { Nullable, ProgressStatus } from "@/types/common";

import ManageCalendarHeader from "./ManageCalendarHeader";
import Button from "@/clients/ui/button";

type Props = {
  onClose: () => void;
  onDelete: () => void;
  progressStatus: Nullable<ProgressStatus>;
  title?: string;
  confirmText?: string;
};

const DeleteBookingConfirm = ({
  onClose,
  onDelete,
  progressStatus,
  title = "Are you sure you want to delete this booking?",
  confirmText = "Yes, Delete this booking",
}: Props) => {
  return (
    <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-0 p-4 md:p-0">
      <ManageCalendarHeader title={title} onClose={onClose} />
      <Button
        className="rounded-lg text-sm font-normal"
        isLoading={progressStatus === ProgressStatus.LOADING}
        onClick={onDelete}
      >
        {confirmText}
      </Button>
      <Button
        intent="secondary"
        className="rounded-lg text-sm font-normal border-black"
        onClick={onClose}
      >
        Cancel
      </Button>
    </div>
  );
};

export default memo(DeleteBookingConfirm);
