"use client";

import { ReactNode, useCallback, useState } from "react";

import <PERSON><PERSON> from "@/clients/ui/button";
import { WeekDaysChecked } from "@/types/calendar";

import ManageCalendarHeader from "./ManageCalendarHeader";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  title: string;
  description: string;
  selectedDays: WeekDaysChecked;
  onSaveSelectedDays: (checked: WeekDaysChecked) => void;
};

const WeekDaysSelector = ({
  onBack,
  onClose,
  selectedDatesNode,
  title,
  onSaveSelectedDays,
  selectedDays,
}: Props) => {
  const [checkedDays, setCheckedDays] = useState<WeekDaysChecked>(selectedDays);

  const onCheck = useCallback(
    (event: any) => {
      const { name, checked } = event.target;

      setCheckedDays({
        ...checkedDays,
        [name]: checked,
      });
    },
    [checkedDays]
  );

  const onSave = useCallback(() => {
    const filtered = Object.entries(checkedDays).reduce((acc, curr) => {
      return curr[1] ? { ...acc, [curr[0]]: curr[1] } : acc;
    }, {});
    onSaveSelectedDays(filtered);
    onBack();
  }, [checkedDays, onBack, onSaveSelectedDays]);

  return (
    <div className="bg-white w-full h-full">
      <div className="p-4 md:p-0 pb-0">
        <ManageCalendarHeader title={title} onBack={onBack} onClose={onClose} />
      </div>

      <div className="p-4 md:p-0 h-full flex flex-col gap-2 md:gap-4 pb-[100px] md:pb-[90px] overflow-y-scroll">
        {selectedDatesNode}
        <p className="text-xs">
          Set which days renters can arrive at your property. These settings
          will affect how your property appears in search results.
        </p>
        <p className="text-sm">{title}</p>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Monday</p>
          <input
            name="monday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["monday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Tuesday</p>
          <input
            name="tuesday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["tuesday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Wednesday</p>
          <input
            name="wednesday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["wednesday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Thursday</p>
          <input
            name="thursday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["thursday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Friday</p>
          <input
            name="friday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["friday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Saturday</p>
          <input
            name="saturday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["saturday"]}
            onChange={onCheck}
          />
        </div>
        <div className="p-4 border flex-center-between rounded-2xl">
          <p className="text-xs">Sunday</p>
          <input
            name="sunday"
            type="checkbox"
            className="toggle toggle-sm toggle-info"
            checked={!!checkedDays["sunday"]}
            onChange={onCheck}
          />
        </div>
        <Button className="rounded-lg text-sm font-normal" onClick={onSave}>
          Save
        </Button>
        <Button
          intent="secondary"
          className="rounded-lg text-sm font-normal"
          onClick={onBack}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default WeekDaysSelector;
