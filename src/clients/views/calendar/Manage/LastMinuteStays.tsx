"use client";

import { FormEvent, ReactNode, useCallback, useState } from "react";

import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";

import ManageCalendarHeader from "./ManageCalendarHeader";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
};

const LastMinuteStays = ({ onBack, onClose, selectedDatesNode }: Props) => {
  const [discountPercentage, setDiscountPercentage] = useState<string>("20");
  const [minRent, setMinRent] = useState<string>("4500");
  const [minNight, setMinNight] = useState<string>("7");

  const onChangeDiscountPercentage = useCallback(
    (event: FormEvent<HTMLInputElement>) => {
      setDiscountPercentage(event.currentTarget.value);
    },
    []
  );

  const onChangeMinRent = useCallback((event: FormEvent<HTMLInputElement>) => {
    setMinRent(event.currentTarget.value);
  }, []);

  const onChangeMinNight = useCallback((event: FormEvent<HTMLInputElement>) => {
    setMinNight(event.currentTarget.value);
  }, []);

  return (
    <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 pb-[90px] p-4 md:p-0">
      <ManageCalendarHeader
        title="Last minute stays"
        onBack={onBack}
        onClose={onClose}
      />
      {selectedDatesNode}
      <div className="p-4 border flex-center-between rounded-2xl">
        <p className="text-xs font-bold w-9/12">
          Apply different requirements for last minute stays
        </p>
        <input
          name="monday"
          type="checkbox"
          className="toggle toggle-sm toggle-info"
        />
      </div>
      <div className="p-4 border flex flex-center justify-between md:flex-col md:items-start rounded-2xl gap-2">
        <p className="text-xs w-6/12 md:w-full">Discount</p>
        <span className="flex-center-center md:justify-start w-[40%]">
          <Input
            name="discountPercentage"
            wrapperclassName="flex-center gap-1"
            className="text-sm font-bold w-[20px] border-0 px-0 py-0"
            value={discountPercentage}
            onChange={onChangeDiscountPercentage}
            type="number"
            max={100}
            min={0}
          />
          %
        </span>
      </div>
      <div className="p-4 border flex flex-center justify-between md:flex-col md:items-start rounded-2xl gap-2">
        <p className="text-xs w-6/12 md:w-full">Minimum rent per booking</p>
        <span className="flex-center-center md:justify-start w-[40%] ">
          <Input
            icon={<span className="text-sm font-bold">$</span>}
            wrapperclassName="flex-center gap-1"
            className="text-sm font-bold w-[40px] border-0 px-0 py-0"
            value={minRent}
            onChange={onChangeMinRent}
            type="number"
          />
        </span>
      </div>
      <div className="p-4 border flex flex-center justify-between md:flex-col md:items-start rounded-2xl gap-2">
        <p className="text-xs w-6/12 md:w-full">
          Applies to booking requests this number of days before arrival
        </p>
        <span className="flex-center-center md:justify-start w-[40%] ">
          <Input
            wrapperclassName="flex-center gap-1"
            className="text-sm font-bold w-[40px] border-0 px-0 py-0"
            value={minNight}
            onChange={onChangeMinNight}
            type="number"
          />
        </span>
      </div>
      <div className="p-4 border flex flex-center justify-between md:flex-col md:items-start rounded-2xl gap-2">
        <p className="text-xs w-6/12 md:w-full">Minimum Night Stay</p>
        <span className="flex-center-center md:justify-start w-[40%] ">
          <Input
            wrapperclassName="flex-center gap-1"
            className="text-sm font-bold w-[40px] border-0 px-0 py-0"
            value={minNight}
            onChange={onChangeMinNight}
            type="number"
          />
        </span>
      </div>
      <Button className="rounded-lg text-sm font-normal" onClick={onBack}>
        Save
      </Button>
      <Button
        intent="secondary"
        className="rounded-lg text-sm font-normal"
        onClick={onBack}
      >
        Cancel
      </Button>
    </div>
  );
};

export default LastMinuteStays;
