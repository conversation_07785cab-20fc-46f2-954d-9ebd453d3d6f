"use client";

import { memo, useCallback, useState } from "react";

import { OptionsByWeekday } from "@/types/calendar";
import { Nullable } from "@/types/common";
import CurrencyInput from "@/clients/ui/currency-input";

type Props = {
  weekDayRates: OptionsByWeekday;
  onChangeWeekDayRate: (value: string, day: string) => void;
  numberInputOnWheelPreventChange: (e: any) => void;
};

const CustomNightlyRate = ({
  weekDayRates,
  onChangeWeekDayRate,
  numberInputOnWheelPreventChange,
}: Props) => {
  const [focusedInput, setFocusedInput] = useState<Nullable<string>>(null);

  const onChangeInput = useCallback(
    (value: string, day: string, isString?: boolean) => {
      if (isString) {
        setFocusedInput(null);
      }
      onChangeWeekDayRate(value, day);
    },
    [onChangeWeekDayRate]
  );

  return (
    <>
      {Object.keys(weekDayRates).map((_key, index) => (
        <div
          key={index}
          className="p-4 border flex rounded-2xl hover:border-black"
        >
          <p className="text-xs w-[50%] capitalize">{_key}</p>
          <CurrencyInput
            wrapperclassName="flex-center-center gap-1 w-[50%]"
            className="text-sm font-bold w-[100px] md:w-full border-0 px-0 py-0"
            isStringValue={
              isNaN(
                Number(weekDayRates?.[_key as keyof OptionsByWeekday]?.rate)
              ) && focusedInput !== _key
            }
            placeholder="Rate"
            value={
              focusedInput === _key &&
              isNaN(
                Number(weekDayRates?.[_key as keyof OptionsByWeekday]?.rate)
              )
                ? ""
                : weekDayRates?.[_key as keyof OptionsByWeekday]?.rate
            }
            onFocus={() => setFocusedInput(_key)}
            onBlur={() => setFocusedInput(null)}
            onChange={(e) =>
              onChangeInput(
                e.currentTarget.value,
                _key,
                isNaN(
                  Number(weekDayRates?.[_key as keyof OptionsByWeekday]?.rate)
                )
              )
            }
            onWheel={numberInputOnWheelPreventChange}
          />
        </div>
      ))}
    </>
  );
};

export default memo(CustomNightlyRate);
