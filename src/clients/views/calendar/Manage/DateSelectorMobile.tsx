"use client";

import { MouseEvent, useCallback, useMemo, useRef, useState } from "react";

import { CalendarViewType } from "@/types/calendar";
import { checkIfStringIsValidDate, getMonthYearsList } from "@/utils/calendar";

import dayjs, { Dayjs } from "dayjs";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import ManageCalendarHeader from "./ManageCalendarHeader";
import SlidePane from "../../common/SlidePane";
import Calendar from "@/clients/components/Calendar";
import ManageCalendarSelectedDates from "@/app/components/calendar/ManageCalendarSelectedDates";
import Button from "@/clients/ui/button";

type Props = {
  isShowing?: boolean;
  onClose: () => void;
  onBack?: () => void;
  selectionStart?: string;
  selectionEnd?: string;
};

const DateSelectorMobile = ({
  isShowing,
  onClose,
  selectionEnd,
  selectionStart,
}: Props) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const wrapperRef = useRef<null | HTMLDivElement>(null);
  const [selected, setSelected] = useState<[null | Dayjs, null | Dayjs]>([
    selectionStart && checkIfStringIsValidDate(selectionStart, "YYYY-MM-DD")
      ? dayjs(selectionStart)
      : null,
    selectionEnd && checkIfStringIsValidDate(selectionEnd, "YYYY-MM-DD")
      ? dayjs(selectionEnd)
      : null,
  ]);
  const [mouseOverDate, setMouseOverDate] = useState<null | Dayjs>(null);
  const months = useMemo(
    () => [
      ...getMonthYearsList(dayjs().year()),
      ...getMonthYearsList(dayjs().year() + 1),
      ...getMonthYearsList(dayjs().year() + 2),
    ],
    []
  );
  const areSelectedValid = useMemo(
    () => selected.every((_date) => dayjs(_date).isValid()),
    [selected]
  );

  const onSelectDate = useCallback(
    (e: MouseEvent<HTMLElement>, dateObject: Dayjs) => {
      if (selected[0]?.isSame(dateObject)) {
        setSelected([null, null]);
        return;
      }

      if (
        selected.every((_s: any) => _s === null) ||
        selected.every((_d) => dayjs(_d).isValid())
      ) {
        setSelected([dateObject, null]);
        return;
      }

      if (dateObject.isAfter(selected[0])) {
        setSelected([selected[0], dateObject]);
      } else {
        setSelected([dateObject, null]);
        return;
      }
    },
    [selected]
  );

  const onContinue = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());
    if (searchParams.has("selectionStart")) {
      params.delete("selectionStart");
    }

    if (searchParams.has("selectionEnd")) {
      params.delete("selectionEnd");
    }

    let selectionSubstring = searchParams.has("view") ? `?${params}&` : `?`;
    if (selected[0]?.isValid()) {
      selectionSubstring += `selectionStart=${selected[0].format(
        "YYYY-MM-DD"
      )}`;

      if (selected[1]?.isValid) {
        selectionSubstring += `&selectionEnd=${
          selected[1] && selected[1].format("YYYY-MM-DD")
        }`;
      }
    }

    router.push(`${pathname}${selectionSubstring}`);
    onClose();
    setSelected([null, null]);
  }, [pathname, router, searchParams, selected, onClose]);

  return (
    <>
      <SlidePane isShowing={isShowing}>
        <div className="bg-white w-full h-full p-4 pb-0 shadow-inner flex flex-col gap-2">
          <ManageCalendarHeader title="Select Date" onClose={onClose} />
          <div
            className="h-full overflow-y-scroll grid grid-cols-1 lg:grid-cols-2 gap-2 pb-[180px]"
            ref={wrapperRef}
          >
            {months.map((_month, index) => (
              <Calendar
                key={index}
                month={_month.month}
                year={_month.year}
                selected={selected}
                mouseOverDate={mouseOverDate}
                setMouseOverDate={setMouseOverDate}
                view={CalendarViewType.YEAR}
                hideHeader={_month.month === 0}
                onSelectDate={onSelectDate}
              />
            ))}
          </div>
        </div>
        <div className="w-full fixed bg-white z-[10] bottom-0 p-2 pb-4 shadow-sidebar-mobile rounded-lg rounded-b-none overflow-y-hidden">
          <div className="flex flex-col gap-2 md:gap-4">
            <ManageCalendarHeader title="Confirm Dates" onClose={onClose} />
            <ManageCalendarSelectedDates
              selectionStart={selected[0]?.format(`YYYY-MM-DD`)}
              selectionEnd={selected[1]?.format(`YYYY-MM-DD`)}
            />
            <Button
              className="text-sm rounded-[16px]"
              onClick={onContinue}
              disabled={!areSelectedValid}
            >
              Continue
            </Button>
          </div>
        </div>
      </SlidePane>
    </>
  );
};

export default DateSelectorMobile;
