"use client";

import { ReactNode, useCallback, useEffect, useMemo, useState } from "react";

import toast from "react-hot-toast";

import { revalidateTagByName } from "@/app/actions/revalidateTag";
import SlidePane from "@/clients/views/common/SlidePane";
import Button from "@/clients/ui/button";
import SwitchWithLabel from "@/clients/ui/switchWithLabel";
import {
  addCalendarAvailability,
  deleteCalendarAvailability,
  updateCalendarAvailability,
} from "@/app/actions/calendar";
import { BlockedType } from "@/types/calendar";
import { ProgressStatus } from "@/types/common";

import classNames from "classnames";

import DeleteBookingConfirm from "./DeleteBookingConfirm";
import { PropertyAvailability } from "@/types/calendar";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectionStart: string;
  selectionEnd: string;
  propertyId: number;
  selectedData?: PropertyAvailability;
  onDeleteSuccessfully?: () => void;
  defaultIsBlocked?: boolean;
  defaultIsBooked?: boolean;
};

const ManageAvailabilityForm = ({
  onBack,
  onClose,
  selectedDatesNode,
  selectionEnd,
  selectionStart,
  propertyId,
  selectedData,
  onDeleteSuccessfully,
  defaultIsBlocked,
  defaultIsBooked,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [isBlocked, setIsBlocked] = useState<boolean>(!!defaultIsBlocked);
  const [isBooked, setIsBooked] = useState<boolean>(!!defaultIsBooked);
  const [isConfirmed, setIsConfirmed] = useState<boolean>(false);
  const [showConfirm, setShowConfirm] = useState<boolean | null>(null);

  const isConfirmedAndSavedBooking = useMemo(
    () => selectedData?.type === BlockedType.LEASED,
    [selectedData?.type]
  );

  const hideDelete = useMemo(
    () =>
      !!selectedData?.brokerage ||
      new RegExp("Congdon and Coleman", "i").test(
        selectedData?.brokerage?.id.toString() ?? ""
      ),
    [selectedData?.brokerage]
  );

  const onSave = useCallback(async () => {
    setProgressStatus(ProgressStatus.LOADING);

    if (selectedData?.availability_uuid) {
      updateCalendarAvailability(propertyId, {
        availabilities: [
          {
            availability_id: selectedData?.availability_uuid ?? "",
            from_date: selectedData.from_date,
            to_date: selectedData.to_date,
            type: isBooked ? BlockedType.LEASED : BlockedType.OWNER_TIME,
            rent_amount: null,
            renter_name: null,
            custom_agent: null,
            availability: false,
            lease: null,
            agreement_url: null,
            custom_source: null,
            brokerage: null,
          },
        ],
      })
        .then((data: any) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          revalidateTagByName(`property-details-${propertyId}`);
          toast.success("Successfully updated!");
          onClose();
        })
        .catch((error) => {
          console.log("errror is", error);
          setProgressStatus(ProgressStatus.FAILED);
        });
    } else {
      addCalendarAvailability(propertyId, {
        availabilities: [
          {
            from_date: selectionStart,
            to_date: selectionEnd,
            brokerage: null,
            type: isBooked ? BlockedType.LEASED : BlockedType.OWNER_TIME,
            rent_amount: null,
            renter_name: null,
            custom_agent: null,
            availability: false,
            lease: null,
            agreement_url: null,
            custom_source: null,
          },
        ],
      })
        .then((data: any) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          revalidateTagByName(`property-details-${propertyId}`);
          toast.success("Successfully created!");
          onClose();
        })
        .catch((error) => {
          console.log("errror is", error);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [
    selectedData?.availability_uuid,
    selectedData?.from_date,
    selectedData?.to_date,
    propertyId,
    isBooked,
    onClose,
    selectionStart,
    selectionEnd,
  ]);

  const onDelete = useCallback(() => {
    if (selectedData) {
      setProgressStatus(ProgressStatus.LOADING);
      deleteCalendarAvailability(propertyId, {
        availabilities: [
          {
            from_date: selectedData.from_date,
            to_date: selectedData.to_date,
            availability: true,
            type: selectedData.type,
          },
        ],
      })
        .then((data) => {
          console.debug("data is", data);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          revalidateTagByName(`property-details-${propertyId}`);
          // onDeleteSuccessfully?.();
          toast.success("Successfully deleted!");
          onClose();
        })
        .catch((error) => {
          console.log("errror is", error);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [onClose, propertyId, selectedData]);

  useEffect(() => {
    if (selectedData) {
      setIsBlocked(!!selectedData?.type);
      setIsBooked(
        selectedData?.type === BlockedType.LEASED ||
          selectedData?.type === BlockedType.PENDING
      );
      setIsConfirmed(selectedData.type === BlockedType.LEASED);
    }
  }, [selectedData]);

  return (
    <>
      <SwitchWithLabel
        label1="Open"
        label2="Block"
        className="w-full"
        isOn={isBlocked}
        onToggle={() => setIsBlocked(!isBlocked)}
        disabled={isConfirmedAndSavedBooking}
      />
      {isBlocked && (
        <>
          <div className="p-4 border flex-center-between rounded-2xl">
            <p className="text-xs">Booked</p>
            <input
              type="checkbox"
              className={classNames("toggle toggle-sm toggle-info", {
                "opacity-50 cursor-not-allowed": isConfirmedAndSavedBooking,
              })}
              checked={isBooked}
              onChange={() =>
                isConfirmedAndSavedBooking ? undefined : setIsBooked(!isBooked)
              }
            />
          </div>
          {isBooked && (
            <a
              className="p-4 border  rounded-2xl hover:border-black text-xs font-normal flex items-center justify-between"
              target="_blank"
              href={`https://cloud.congdonandcoleman.com/lease`}
            >
              Create Lease
              <ChevronRightIcon className="w-4 h-4" />
            </a>
          )}
          <Button
            className="rounded-lg text-sm font-normal"
            isLoading={progressStatus === ProgressStatus.LOADING}
            onClick={onSave}
          >
            Save
          </Button>
          {!!selectedData && !hideDelete && (
            <Button
              intent="secondary"
              className="rounded-lg text-sm font-normal"
              onClick={() => setShowConfirm(true)}
            >
              {isBlocked && !isBooked ? "Delete block" : "Delete booking"}
            </Button>
          )}
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal border-black"
            onClick={onBack}
          >
            Cancel
          </Button>
        </>
      )}

      {showConfirm !== null && (
        <SlidePane isShowing={showConfirm} wrapperClassName="md:!inset-0">
          <DeleteBookingConfirm
            onClose={() => setShowConfirm(false)}
            onDelete={onDelete}
            progressStatus={progressStatus}
            title={
              isBlocked && !isBooked
                ? `Are you sure you want to delete this block?`
                : undefined
            }
            confirmText={
              isBlocked && !isBooked ? `Yes, Delete this block` : undefined
            }
          />
        </SlidePane>
      )}
    </>
  );
};

export default ManageAvailabilityForm;
