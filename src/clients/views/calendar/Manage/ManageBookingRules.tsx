'use client';

import {
  FormEvent,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { ChevronRightIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

import { revalidateTagByName } from '@/app/actions/revalidateTag';
import SlidePane from '@/clients/views/common/SlidePane';
import usePropertyRatesAndRules from '@/hooks/usePropertyRatesAndRules';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import { addCalendarRentalRate } from '@/app/actions/calendar';
import { WeekDaysChecked } from '@/types/calendar';
import { ProgressStatus } from '@/types/common';
import { formatCheckedDaysString } from '@/utils/bookingRules';
import {
  formatCheckinCheckoutOptionsByWeekday,
  formatRateAndRules,
  getCheckinDaysFromRates,
  getCheckoutDaysFromRates,
} from '@/utils/rates';

import classNames from 'classnames';
import Link from 'next/link';

import ManageCalendarHeader from './ManageCalendarHeader';
import WeekDaysSelector from './WeekDaysSelector';
import { Property } from '@/types/property';

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectionStart: string;
  selectionEnd: string;
  propertyId: number;
  propertyData: Property;
};

const ManageBookingRules = ({
  onBack,
  onClose,
  selectedDatesNode,
  propertyId,
  selectionStart,
  selectionEnd,
  propertyData,
}: Props) => {
  const { data, isLoading } = usePropertyRatesAndRules(
    propertyId,
    selectionStart,
    selectionEnd
  );

  const formattedRatesAndRules = useMemo(
    () =>
      formatRateAndRules(data ?? [], propertyData.requirement.min_night_stay),
    [data, propertyData.requirement.min_night_stay]
  );

  // console.log("formattedRatesAndRules", formattedRatesAndRules, data);

  const hasEmptyRates = useMemo(
    () => !isLoading && data && data.length === 0,
    [data, isLoading]
  );

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [minNight, setMinNight] = useState<string>('');
  const [openCheckinDays, setOpenCheckinDays] = useState<boolean | null>(null);
  const [openCheckoutDays, setOpenCheckoutDays] = useState<boolean | null>(
    null
  );
  const [checkinDays, setCheckinDays] = useState<WeekDaysChecked>({
    saturday: true,
  });
  const [checkoutDays, setCheckoutDays] = useState<WeekDaysChecked>({
    saturday: true,
  });

  const checkinString = useMemo(
    () => formatCheckedDaysString(checkinDays),
    [checkinDays]
  );
  const checkoutString = useMemo(
    () => formatCheckedDaysString(checkoutDays),
    [checkoutDays]
  );

  const preSubmitCheck = useCallback(
    (val?: string) => {
      let err: { [key: string]: string } = {};
      let _minimumNightsStay = !!val ? val : !!minNight ? minNight : null;
      if (!_minimumNightsStay) {
        err['minNight'] = 'Minumum night stay is required';
      } else if (Number(_minimumNightsStay) < 1) {
        err['minNight'] = 'Minumum night should be atleast 1';
      }

      setErrors(err);
      return err;
    },
    [minNight]
  );

  const onChangeText = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      setMinNight(e.currentTarget.value);
      preSubmitCheck(e.currentTarget.value);
    },
    [preSubmitCheck]
  );

  const numberInputOnWheelPreventChange = useCallback((e: any) => {
    // Prevent the input value change
    e.target.blur();

    // Refocus immediately, on the next tick (after the current function is done)
    setTimeout(() => {
      e.target.focus();
    }, 0);
  }, []);

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      rates: [
        {
          ...formattedRatesAndRules,
          from_date: selectionStart,
          to_date: selectionEnd,
          minimum_nights_stay: Number(minNight),
          amount: Number(formattedRatesAndRules?.amount),
          weekly_amount: Number(formattedRatesAndRules?.weekly_amount),
          minimum_booking_amount: formattedRatesAndRules?.minimum_booking_amount
            ? Number(formattedRatesAndRules.minimum_booking_amount)
            : null,
          options_by_weekday: formatCheckinCheckoutOptionsByWeekday(
            checkinDays,
            checkoutDays,
            propertyData.requirement.turnover_day
          ),
        },
      ],
    };

    addCalendarRentalRate(propertyId, payload)
      .then(async (_data) => {
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        revalidateTagByName(`property-details-${propertyId}`);
        toast.success('Successfully updated booking rules!');
        onClose();
      })
      .catch((err) => {
        console.log('error is', err);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    preSubmitCheck,
    formattedRatesAndRules,
    selectionStart,
    selectionEnd,
    minNight,
    checkinDays,
    checkoutDays,
    propertyData.requirement.turnover_day,
    propertyId,
    onClose,
  ]);

  useEffect(() => {
    if (!!formattedRatesAndRules?.minimum_nights_stay) {
      setMinNight(String(formattedRatesAndRules.minimum_nights_stay ?? ''));
    }
  }, [formattedRatesAndRules?.minimum_nights_stay]);

  useEffect(() => {
    if (formattedRatesAndRules?.options_by_weekday) {
      setCheckinDays(
        getCheckinDaysFromRates(formattedRatesAndRules.options_by_weekday)
      );
      setCheckoutDays(
        getCheckoutDaysFromRates(formattedRatesAndRules.options_by_weekday)
      );
    } else if (propertyData.requirement.turnover_day) {
      setCheckinDays({
        [propertyData.requirement.turnover_day.toLowerCase()]: true,
      });
      setCheckoutDays({
        [propertyData.requirement.turnover_day.toLowerCase()]: true,
      });
    }
  }, [
    formattedRatesAndRules?.options_by_weekday,
    propertyData.requirement.turnover_day,
  ]);

  return (
    <>
      <div className="p-4 md:p-0 pb-0">
        <ManageCalendarHeader
          title="Booking Rules"
          onBack={onBack}
          onClose={onClose}
        />
      </div>
      <div className="overflow-scroll h-full flex flex-col gap-2 p-4 md:p-0 pb-[100px] md:pb-[90px]">
        {selectedDatesNode}
        {isLoading ? (
          <div className="w-full h-[150px] flex-center-center">
            <div className="loading loading-spinner loading-md" />
          </div>
        ) : (
          <>
            {hasEmptyRates && (
              <p className="p-2 text-sm text-info">
                Please add rates for the selected range
              </p>
            )}
            <div>
              <div className="p-4 border flex justify-between md:justify-start md:flex-col rounded-2xl hover:border-black">
                <p className="text-xs">Minimum Night Stay</p>
                <Input
                  name="minNight"
                  value={minNight}
                  onChange={onChangeText}
                  wrapperclassName="flex-center gap-1"
                  className="text-sm font-bold w-[60px] md:w-full border-0 px-0 py-0"
                  placeholder="Nights"
                  type="number"
                  min={0}
                  onWheel={numberInputOnWheelPreventChange}
                  disabled={hasEmptyRates}
                />
              </div>
              {errors?.minNight && (
                <p className="pl-4 text-error text-[10px]">
                  {errors?.minNight}
                </p>
              )}
            </div>

            <div
              className={classNames(
                'p-4 border flex-center-between rounded-2xl hover:border-black cursor-pointer',
                {
                  'cursor-not-allowed': hasEmptyRates,
                }
              )}
              onClick={() => !hasEmptyRates && setOpenCheckinDays(true)}
            >
              <div className="break-words w-[80%]">
                <p className="text-xs">Check in days</p>
                <p className="text-xs font-bold">{checkinString}</p>
              </div>
              <ChevronRightIcon className="w-[18px] h-auto" />
            </div>
            <div
              className={classNames(
                'p-4 border flex-center-between rounded-2xl hover:border-black cursor-pointer',
                {
                  'cursor-not-allowed': hasEmptyRates,
                }
              )}
              onClick={() => !hasEmptyRates && setOpenCheckoutDays(true)}
            >
              <div className="break-words w-[80%]">
                <p className="text-xs">Check out days</p>
                <p className="text-xs font-bold">{checkoutString}</p>
              </div>
              <ChevronRightIcon className="w-[18px] h-auto" />
            </div>
            <p className="text-sm">House Rules</p>
            <Link
              href={`/property/${propertyId}/general`}
              className="p-4 border flex-center-between rounded-2xl hover:border-black cursor-pointer"
            >
              <div>
                <p className="text-xs">
                  Manage your booking rules which will apply to all dates
                </p>
              </div>
              <ChevronRightIcon className="w-[18px] h-auto" />
            </Link>
            <Button
              className="rounded-lg text-sm font-normal"
              isLoading={progressStatus === ProgressStatus.LOADING}
              onClick={onSave}
              disabled={
                hasEmptyRates || progressStatus === ProgressStatus.LOADING
              }
            >
              Save
            </Button>
            <Button
              intent="secondary"
              className="rounded-lg text-sm font-normal"
              onClick={onBack}
            >
              Cancel
            </Button>
          </>
        )}
      </div>

      {openCheckinDays !== null && (
        <SlidePane isShowing={openCheckinDays} wrapperClassName="md:!inset-0">
          <WeekDaysSelector
            onClose={() => setOpenCheckinDays(false)}
            onBack={() => setOpenCheckinDays(false)}
            selectedDatesNode={selectedDatesNode}
            onSaveSelectedDays={setCheckinDays}
            selectedDays={checkinDays}
            title="Check in days"
            description="Set which days renters can arrive at your property. These settings will affect how your property appears in search results."
          />
        </SlidePane>
      )}
      {openCheckoutDays !== null && (
        <SlidePane isShowing={openCheckoutDays} wrapperClassName="md:!inset-0">
          <WeekDaysSelector
            onClose={() => setOpenCheckoutDays(false)}
            onBack={() => setOpenCheckoutDays(false)}
            selectedDatesNode={selectedDatesNode}
            selectedDays={checkoutDays}
            onSaveSelectedDays={setCheckoutDays}
            title="Check out days"
            description="Set which days bookings at your property must end. These settings will affect how your property appears in search results."
          />
        </SlidePane>
      )}
    </>
  );
};

export default ManageBookingRules;
