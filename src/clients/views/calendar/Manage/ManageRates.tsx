"use client";

import {
  FormEvent,
  ReactNode,
  startTransition,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";

import { ChevronRightIcon } from "@heroicons/react/24/outline";
import toast from "react-hot-toast";

import { revalidateTagByName } from "@/app/actions/revalidateTag";
import SlidePane from "@/clients/views/common/SlidePane";
import Button from "@/clients/ui/button";
import CurrencyInput from "@/clients/ui/currency-input";
import { addCalendarRentalRate } from "@/app/actions/calendar";
import { OptionsByWeekday, PropertyRentalRatePayload } from "@/types/calendar";
import { ProgressStatus } from "@/types/common";
import {
  formatOptionsByWeekdayForRates,
  formatRateAndRules,
  getWeekdayKeys,
} from "@/utils/rates";

import { uniqBy } from "rambda";

import CustomNightlyRate from "./CustomNightlyRate";
import Discounts from "./Discounts";
import ManageCalendarHeader from "./ManageCalendarHeader";
import usePropertyRatesAndRules from "@/hooks/usePropertyRatesAndRules";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";
import { Property } from "@/types/property";
import dayjs from "dayjs";

export type Discount = {
  percentage: number;
  months: number;
  name: DiscountType;
};

export enum DiscountType {
  EARLY_BIRD = "EARLY_BIRD",
  LAST_MINUTE = "LAST_MINUTE",
  CUSTOM = "CUSTOM",
}

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  selectionStart: string;
  selectionEnd: string;
  propertyId: number;
  propertyData: Property;
};

const ManageRates = ({
  onBack,
  onClose,
  selectedDatesNode,
  selectionEnd,
  selectionStart,
  propertyId,
  propertyData,
}: Props) => {
  const { onSuccessfullyUpdateRates } = useContext(ManageCalendarContext);
  const { data, setData, isLoading, fetchData } = usePropertyRatesAndRules(
    propertyId,
    selectionStart,
    selectionEnd
  );

  const formattedRatesAndRules = useMemo(
    () =>
      formatRateAndRules(data ?? [], propertyData.requirement.min_night_stay),
    [data, propertyData.requirement.min_night_stay]
  );

  const hasEmptyRates = useMemo(
    () => !isLoading && data && data.length === 0,
    [data, isLoading]
  );
  const isLessThanWeek = useMemo(
    () => dayjs(selectionEnd).add(1, "day").diff(selectionStart, "days") < 7,
    [selectionEnd, selectionStart]
  );

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isDayWiseRate, setIsDayWiseRate] = useState(false);
  const [rate, setRate] = useState<string>("");
  const [propertyWeeklyRent, setPropertyWeeklyRent] = useState<string>("");
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [minimumRent, setMinimumRent] = useState<string>("");
  const [openDiscountDropdown, setOpenDiscountDropdown] = useState<
    boolean | null
  >(null);
  const [selectedDiscounts, setSelectedDiscounts] = useState<{
    [x: string]: Discount;
  }>({});
  const [weekDayRates, setWeekDayRates] = useState<OptionsByWeekday>(
    getWeekdayKeys(selectionStart, selectionEnd).reduce(
      (acc, curr) => (acc = { ...acc, [curr]: { rate: "" } }),
      {}
    )
  );

  const preSubmitCheck = useCallback(() => {
    let err: { [key: string]: string } = {};
    if (isLessThanWeek && rate === "") {
      err["rate"] = "Rate is required";
    }

    if (!isLessThanWeek && propertyWeeklyRent === "") {
      err["propertyWeeklyRent"] = "Weekly rent is required";
    }

    if (
      isDayWiseRate &&
      Object.values(weekDayRates).some((_val) => _val.rate === "") &&
      rate === ""
    ) {
      err["weekDayRates"] = "Rates for all days are required";
    }

    setErrors(err);
    return err;
  }, [isDayWiseRate, isLessThanWeek, propertyWeeklyRent, rate, weekDayRates]);

  const numberInputOnWheelPreventChange = useCallback((e: any) => {
    // Prevent the input value change
    e.target.blur();

    // Refocus immediately, on the next tick (after the current function is done)
    setTimeout(() => {
      e.target.focus();
    }, 0);
  }, []);

  const onChangeRate = useCallback((e: FormEvent<HTMLInputElement>) => {
    const number = e.currentTarget.value.replace(/,/g, "");
    if (isNaN(Number(number))) {
      return;
    }
    setRate(number);
    setPropertyWeeklyRent((Number(number) * 7).toFixed(2));
  }, []);

  const onChangeWeeklyRate = useCallback((e: FormEvent<HTMLInputElement>) => {
    const number = e.currentTarget.value.replace(/,/g, "");
    if (isNaN(Number(number))) {
      return;
    }
    setPropertyWeeklyRent(number);
    setRate((Number(number) / 7).toFixed(2));
  }, []);

  const onChangeMinimumRent = useCallback((e: FormEvent<HTMLInputElement>) => {
    const number = e.currentTarget.value.replace(/,/g, "");
    if (isNaN(Number(number))) {
      return;
    }
    setMinimumRent(number);
  }, []);

  const onClickDiscount = useCallback(() => {
    setOpenDiscountDropdown(true);
  }, []);

  const onChangeWeekDayRate = useCallback(
    (value: string, day: string) => {
      const number = value.replace(/,/g, "");
      if (isNaN(Number(number))) {
        return;
      }
      setWeekDayRates({
        ...weekDayRates,
        [day]: { rate: number },
      });
    },
    [weekDayRates]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    const payload: PropertyRentalRatePayload = {
      amount: Number(rate),
      weekly_amount: Number(propertyWeeklyRent),
      from_date: selectionStart,
      to_date: selectionEnd,
      options_by_weekday:
        !!propertyData.requirement.turnover_day &&
        hasEmptyRates &&
        !isDayWiseRate
          ? {
              [propertyData.requirement.turnover_day.toLowerCase()]: {
                allow_checkin: true,
                allow_checkout: true,
                update_rate: false,
              },
            }
          : formatOptionsByWeekdayForRates(
              weekDayRates,
              formattedRatesAndRules?.options_by_weekday ?? {},
              Number(rate),
              hasEmptyRates
                ? (
                    propertyData.requirement?.turnover_day ?? "Saturday"
                  )?.toLowerCase()
                : undefined,
              isDayWiseRate
            ),
      early_bird_discount: selectedDiscounts[DiscountType.EARLY_BIRD]
        ? Number(selectedDiscounts[DiscountType.EARLY_BIRD].percentage)
        : null,
      early_bird_discount_months: selectedDiscounts[DiscountType.EARLY_BIRD]
        ? Number(selectedDiscounts[DiscountType.EARLY_BIRD].months)
        : null,
      custom_discount: selectedDiscounts[DiscountType.CUSTOM]
        ? Number(selectedDiscounts[DiscountType.CUSTOM].percentage)
        : null,
      custom_discount_days: selectedDiscounts[DiscountType.CUSTOM]
        ? Number(selectedDiscounts[DiscountType.CUSTOM].months)
        : null,
      last_minute_discount: selectedDiscounts[DiscountType.LAST_MINUTE]
        ? Number(selectedDiscounts[DiscountType.LAST_MINUTE].percentage)
        : null,
      last_minute_rule_days: selectedDiscounts[DiscountType.LAST_MINUTE]
        ? Number(selectedDiscounts[DiscountType.LAST_MINUTE].months)
        : null,
    };

    if (
      formattedRatesAndRules?.minimum_nights_stay ||
      propertyData.requirement.min_night_stay
    ) {
      payload.minimum_nights_stay =
        formattedRatesAndRules?.minimum_nights_stay ??
        propertyData.requirement.min_night_stay;
    }
    if (minimumRent && Number(minimumRent) > 0) {
      payload.minimum_booking_amount = Number(minimumRent);
    }
    addCalendarRentalRate<Property>(propertyId, { rates: [payload] })
      .then((data) => {
        startTransition(() => onSuccessfullyUpdateRates(data?.rates ?? []));
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        revalidateTagByName(`property-details-${propertyId}`);
        toast.success("Successfully updated rental rates!");
        onClose();
      })
      .catch((err) => {
        console.log("error is", err);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    preSubmitCheck,
    rate,
    propertyWeeklyRent,
    selectionStart,
    selectionEnd,
    propertyData.requirement.turnover_day,
    propertyData.requirement.min_night_stay,
    hasEmptyRates,
    isDayWiseRate,
    weekDayRates,
    formattedRatesAndRules?.options_by_weekday,
    formattedRatesAndRules?.minimum_nights_stay,
    selectedDiscounts,
    minimumRent,
    propertyId,
    onClose,
    onSuccessfullyUpdateRates,
  ]);

  useEffect(() => {
    if (!!formattedRatesAndRules) {
      console.log("formattedRatesAndRules", formattedRatesAndRules);
      setPropertyWeeklyRent(
        String(formattedRatesAndRules?.weekly_amount ?? "")
      );
      setRate(String(formattedRatesAndRules.amount ?? ""));
      setMinimumRent(
        String(formattedRatesAndRules?.minimum_booking_amount ?? "")
      );
      // setIsDayWiseRate(
      //   Object.values(formattedRatesAndRules?.options_by_weekday ?? {}).every(
      //     (_d) => isNaN(Number(_d.rate))
      //   ) ||
      //     uniqBy(
      //       (_x) => _x.rate,
      //       Object.values(formattedRatesAndRules?.options_by_weekday ?? {})
      //     ).length !== 1
      // );
      setWeekDayRates({
        ...(formattedRatesAndRules?.options_by_weekday ?? {}),
      });
      const discountMap: { [x: string]: Discount } = {};
      if (
        formattedRatesAndRules?.custom_discount &&
        formattedRatesAndRules?.custom_discount_days
      ) {
        discountMap[DiscountType.CUSTOM] = {
          percentage: formattedRatesAndRules?.custom_discount,
          months: formattedRatesAndRules?.custom_discount_days,
          name: DiscountType.CUSTOM,
        };
      }

      if (
        formattedRatesAndRules?.early_bird_discount &&
        formattedRatesAndRules?.early_bird_discount_months
      ) {
        discountMap[DiscountType.EARLY_BIRD] = {
          percentage: formattedRatesAndRules?.early_bird_discount,
          months: formattedRatesAndRules?.early_bird_discount_months,
          name: DiscountType.EARLY_BIRD,
        };
      }

      if (
        formattedRatesAndRules?.last_minute_discount &&
        formattedRatesAndRules?.last_minute_rule_days
      ) {
        discountMap[DiscountType.LAST_MINUTE] = {
          percentage: formattedRatesAndRules?.last_minute_discount,
          months: formattedRatesAndRules?.last_minute_rule_days,
          name: DiscountType.LAST_MINUTE,
        };
      }

      setSelectedDiscounts(discountMap);
    }
  }, [formattedRatesAndRules]);

  return (
    <>
      <div className="p-4 md:p-0 pb-0">
        <ManageCalendarHeader title="Rates" onBack={onBack} onClose={onClose} />
      </div>
      <div className="overflow-scroll h-full flex flex-col gap-2 p-4 md:p-0 pb-[100px] md:pb-[90px]">
        {selectedDatesNode}
        {isLoading ? (
          <div className="w-full h-[150px] flex-center-center">
            <div className="loading loading-spinner loading-md" />
          </div>
        ) : (
          <>
            {isLessThanWeek ? (
              <>
                <div>
                  <div className="p-4 border flex justify-between md:justify-start md:flex-col rounded-2xl hover:border-black">
                    <p className="text-xs mb-0 md:mb-2">Nightly Rate</p>
                    <CurrencyInput
                      wrapperclassName="flex-center gap-1"
                      className="text-sm font-bold w-[100px] md:w-full border-0 px-0 py-0"
                      placeholder="Rate per night"
                      name="rate"
                      value={rate}
                      onChange={onChangeRate}
                    />
                  </div>
                  {errors?.rate && (
                    <p className="pl-4 text-error text-[10px]">
                      {errors?.rate}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <>
                <div>
                  <div className="p-4 border flex justify-between md:justify-start md:flex-col rounded-2xl hover:border-black">
                    <p className="text-xs mb-0 md:mb-2">Weekly Rate</p>
                    <CurrencyInput
                      wrapperclassName="flex-center gap-1"
                      className="text-sm font-bold w-[100px] md:w-full border-0 px-0 py-0"
                      placeholder="Rate per week"
                      name="propertyWeeklyRent"
                      value={propertyWeeklyRent}
                      onChange={onChangeWeeklyRate}
                    />
                  </div>
                  {errors?.rate && (
                    <p className="pl-4 text-error text-[10px]">
                      {errors?.rate}
                    </p>
                  )}
                </div>
                <div className="p-4 border flex justify-between md:justify-start md:flex-col rounded-2xl hover:border-black">
                  <p className="text-xs mb-0 md:mb-2">Nightly Rate</p>
                  <p className="text-sm font-bold">
                    ${" "}
                    {rate
                      ? Number(Number(rate).toFixed(2)).toLocaleString("en-US")
                      : ""}
                  </p>
                </div>
              </>
            )}
            <div>
              <div className="p-4 border  rounded-2xl hover:border-black">
                <div className="flex justify-between md:justify-start md:flex-col">
                  <p className="text-xs mb-0 md:mb-2">
                    Minimum Rent per Booking
                  </p>
                  <CurrencyInput
                    wrapperclassName="flex-center gap-1"
                    className="text-sm font-bold w-[100px] md:w-full border-0 px-0 py-0"
                    placeholder="Minimum Rent"
                    name="minimumRent"
                    value={minimumRent}
                    onChange={onChangeMinimumRent}
                  />
                </div>

                <p className="text-[10px] text-roman-silver mt-2">
                  Any booking during this period will be for this amount or
                  more.
                </p>
              </div>
              {errors?.minimumRent && (
                <p className="pl-4 text-error text-[10px]">
                  {errors?.minimumRent}
                </p>
              )}
            </div>
            <div className="p-4 border flex-center-between rounded-2xl">
              <p className="text-xs">Customize rate by night of the week</p>
              <input
                type="checkbox"
                className="toggle toggle-sm toggle-info"
                checked={isDayWiseRate}
                onChange={() => setIsDayWiseRate(!isDayWiseRate)}
              />
            </div>
            {errors?.weekDayRates && isDayWiseRate && (
              <p className="pl-4 text-error text-[10px]">
                {errors?.weekDayRates}
              </p>
            )}
            {isDayWiseRate && (
              <CustomNightlyRate
                weekDayRates={weekDayRates}
                onChangeWeekDayRate={onChangeWeekDayRate}
                numberInputOnWheelPreventChange={
                  numberInputOnWheelPreventChange
                }
              />
            )}
            <div
              className="p-4 border rounded-2xl hover:border-black cursor-pointer"
              onClick={onClickDiscount}
            >
              <div className="flex-center-between">
                <p className="text-sm font-bold">Discounts</p>
                <ChevronRightIcon className="w-[18px] h-auto" />
              </div>
              <p className="text-xs">
                You have {Object.keys(selectedDiscounts).length} discounts
                active for this date range
              </p>
            </div>
            <Button
              className="rounded-lg text-sm font-normal"
              isLoading={progressStatus === ProgressStatus.LOADING}
              onClick={onSave}
            >
              Save
            </Button>
            <Button
              intent="secondary"
              className="rounded-lg text-sm font-normal"
              onClick={onBack}
            >
              Cancel
            </Button>
          </>
        )}
      </div>
      {openDiscountDropdown !== null && (
        <SlidePane
          isShowing={openDiscountDropdown}
          wrapperClassName="md:!inset-0"
        >
          <Discounts
            onClose={() => setOpenDiscountDropdown(false)}
            onBack={() => setOpenDiscountDropdown(false)}
            selectedDatesNode={selectedDatesNode}
            selectedDiscounts={selectedDiscounts}
            setSelectedDiscounts={setSelectedDiscounts}
          />
        </SlidePane>
      )}
    </>
  );
};

export default ManageRates;
