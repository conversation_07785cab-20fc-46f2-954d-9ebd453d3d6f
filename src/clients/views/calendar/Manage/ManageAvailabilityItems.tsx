"use client";

import { memo } from "react";

import { BlockedType } from "@/types/calendar";
import { getAvailabilityTitle } from "@/utils/calendar";

import classNames from "classnames";
import dayjs from "dayjs";
import { PropertyAvailability } from "@/types/calendar";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

type Props = {
  data: PropertyAvailability[];
  selectionStart?: string;
  selectionEnd?: string;
  propertyId: number;
  onSelectId: (id: string) => void;
};

const ManageAvailabilityItems = ({
  data,
  selectionStart,
  selectionEnd,
  propertyId,
  onSelectId,
}: Props) => {
  return (
    <>
      {data.map((_avail, index) => (
        <div
          key={index}
          className="p-4 border rounded-2xl hover:border-black cursor-pointer flex items-center"
          onClick={() => onSelectId(_avail.availability_uuid)}
        >
          <div className="flex-grow">
            <p
              className={classNames("text-xs md:text-[10px] font-bold", {
                "text-black-60": _avail.type === BlockedType.OWNER_TIME,
                "text-carolina-blue": _avail.type === BlockedType.LEASED,
                "text-pending": _avail.type === BlockedType.PENDING,
              })}
            >
              {getAvailabilityTitle(_avail.type)}
            </p>
            <p className="text-xs md:text-[10px]">
              {dayjs(_avail.from_date).format(`MMM D, YYYY`)} -{" "}
              {dayjs(_avail.to_date).format(`MMM D, YYYY`)}
            </p>
            {_avail.renter_name && (
              <p className="text-[19px] font-bold mt-2">{_avail.renter_name}</p>
            )}
          </div>
          <ChevronRightIcon className="w-4 h-4" />
        </div>
      ))}
    </>
  );
};

export default memo(ManageAvailabilityItems);
