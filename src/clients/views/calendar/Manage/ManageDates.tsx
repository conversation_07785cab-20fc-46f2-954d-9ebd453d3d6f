"use client";

import {
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";

import {
  CalendarIcon,
  ChevronRightIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";

import SlidePane from "@/clients/views/common/SlidePane";
import Button from "@/clients/ui/button";
import { getAvailabilityDataForRange } from "@/utils/calendar";

import dynamic from "next/dynamic";

import MobileAvailabilityInfo from "./Availability/MobileAvailabilityInfo";
import ManageBookingRules from "./ManageBookingRules";
import ManageCalendarHeader from "./ManageCalendarHeader";
import { PropertyAvailability } from "@/types/calendar";
import { Property } from "@/types/property";
import usePropertyRatesAndRules from "@/hooks/usePropertyRatesAndRules";
import { formatRateAndRules } from "@/utils/rates";
import { currencyFormatterRound } from "@/utils/common";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";
import dayjs from "dayjs";

const ManageAvailability = dynamic(() => import("./ManageAvailability"), {
  ssr: false,
});
const ManageDatesMobile = dynamic(() => import("../Manage/ManageDatesMobile"), {
  ssr: false,
});
const ManageRates = dynamic(() => import("../Manage/ManageRates"), {
  ssr: false,
});

type Props = {
  selectedDatesNode: ReactNode;
  propertyId: number;
  propertyData: Property;
  availabilityData: PropertyAvailability[];
};

const ManageDates = ({
  selectedDatesNode,
  propertyId,
  propertyData,
  availabilityData,
}: Props) => {
  const {
    selected,
    setSelected,
    selectedAvailabilityId,
    setSelectedAvailabilityId,
  } = useContext(ManageCalendarContext);
  const selectedAvailability = useMemo(
    () =>
      availabilityData?.find(
        (_a) => _a.availability_uuid === selectedAvailabilityId
      ),
    [availabilityData, selectedAvailabilityId]
  );
  const [showManageAvailability, setShowManageAvailability] = useState<
    boolean | null
  >(null);
  const [showManageDatesMobile, setShowManageDatesMobile] = useState<
    boolean | null
  >(null);
  const [showManageRates, setShowManageRates] = useState<boolean | null>(null);
  const [showManageBookingRules, setShowManageBookingRules] = useState<
    boolean | null
  >(null);
  const availabilityDataForSelectedRange = useMemo(
    () =>
      getAvailabilityDataForRange(availabilityData, selected[0], selected[1]),
    [availabilityData, selected]
  );

  const { data: ratesAndRules, isLoading } = usePropertyRatesAndRules(
    propertyId,
    dayjs(selected[0]).isValid() ? dayjs(selected[0]).format("YYYY-MM-DD") : "",
    dayjs(selected[1]).isValid() ? dayjs(selected[1]).format("YYYY-MM-DD") : ""
  );

  const formattedRatesAndRules = useMemo(
    () =>
      formatRateAndRules(
        ratesAndRules ?? [],
        propertyData.requirement.min_night_stay
      ),
    [propertyData.requirement.min_night_stay, ratesAndRules]
  );

  const onCloseManageDatesMobile = useCallback(() => {
    setSelected([null, null]);
    setShowManageDatesMobile(false);
  }, [setSelected]);

  const onBackAvailability = useCallback(() => {
    if (selectedAvailability?.availability_uuid) {
      setSelectedAvailabilityId(null);
    }
    setShowManageAvailability(false);
  }, [selectedAvailability?.availability_uuid, setSelectedAvailabilityId]);

  const onClose = useCallback(() => {
    setSelected([null, null]);
    setSelectedAvailabilityId(null);
  }, [setSelected, setSelectedAvailabilityId]);

  const onViewEventDetailsMobile = useCallback(
    (uuid?: string) => {
      setShowManageDatesMobile(true);
      if (uuid) {
        setSelectedAvailabilityId(uuid);
      }
    },
    [setSelectedAvailabilityId]
  );

  useEffect(() => {
    if (selectedAvailability?.availability_uuid) {
      setShowManageAvailability(true);
    }
  }, [selectedAvailability?.availability_uuid]);

  return (
    <>
      <div className="flex flex-col gap-2 md:gap-4">
        <ManageCalendarHeader title="Manage Dates" onClose={onClose} />
        {selectedDatesNode}
        {availabilityDataForSelectedRange.length > 0 && (
          <div className="md:hidden my-1">
            <MobileAvailabilityInfo
              availabilityData={availabilityDataForSelectedRange}
              onViewEventDetailsMobile={onViewEventDetailsMobile}
            />
          </div>
        )}
        <Button
          className="md:hidden rounded-lg text-sm"
          onClick={() => setShowManageDatesMobile(true)}
        >
          Manage Dates
        </Button>
        <Button
          intent="secondary"
          className="hidden md:flex text-xs rounded-2xl font-normal w-full px-4 md:px-2 py-4 justify-between hover:border-black"
          onClick={() => setShowManageAvailability(true)}
        >
          <div className="flex items-center gap-2">
            <CalendarIcon className="w-6 h-6" />
            Availability
          </div>
          <ChevronRightIcon
            className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
            aria-hidden="true"
          />
        </Button>
        <Button
          intent="secondary"
          className="hidden md:flex text-xs rounded-2xl font-normal w-full px-4 md:px-2 py-4 justify-between hover:border-black"
          onClick={() => setShowManageRates(true)}
        >
          <div className="text-left">
            <div className="flex items-center gap-2">
              <CurrencyDollarIcon className="w-6 h-6" />
              Rates
            </div>
            {!isLoading && formattedRatesAndRules?.weekly_amount && (
              <span className="pl-2 font-medium">
                {currencyFormatterRound.format(
                  formattedRatesAndRules?.weekly_amount ?? 0
                )}
                /wk
              </span>
            )}
          </div>
          <ChevronRightIcon
            className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
            aria-hidden="true"
          />
        </Button>
        <Button
          intent="secondary"
          className="hidden md:flex text-xs rounded-2xl font-normal w-full px-4 md:px-2 py-4 justify-between hover:border-black"
          onClick={() => setShowManageBookingRules(true)}
        >
          <div className="flex items-center gap-2">
            <ClipboardDocumentListIcon className="w-6 h-6" />
            Booking Rules
          </div>
          <ChevronRightIcon
            className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
            aria-hidden="true"
          />
        </Button>
      </div>

      {showManageDatesMobile !== null && (
        <ManageDatesMobile
          isShowing={showManageDatesMobile}
          onClose={onCloseManageDatesMobile}
          onBack={onCloseManageDatesMobile}
          selectedDatesNode={selectedDatesNode}
          propertyId={propertyId}
          propertyData={propertyData}
          availabilityData={availabilityDataForSelectedRange}
          selectionStart={selected?.[0]?.format("YYYY-MM-DD") ?? ""}
          selectionEnd={selected?.[1]?.format("YYYY-MM-DD") ?? ""}
        />
      )}

      {showManageAvailability !== null && (
        <SlidePane isShowing={showManageAvailability}>
          <ManageAvailability
            onClose={onClose}
            onBack={onBackAvailability}
            selectedDatesNode={selectedDatesNode}
            selectionStart={selected?.[0]?.format("YYYY-MM-DD") ?? ""}
            selectionEnd={selected?.[1]?.format("YYYY-MM-DD") ?? ""}
            propertyId={propertyId}
            selectedAvailability={selectedAvailability}
            availabilityData={availabilityDataForSelectedRange}
          />
        </SlidePane>
      )}

      {showManageRates !== null && (
        <SlidePane isShowing={showManageRates}>
          <ManageRates
            onBack={() => setShowManageRates(false)}
            onClose={onClose}
            selectedDatesNode={selectedDatesNode}
            selectionStart={selected?.[0]?.format("YYYY-MM-DD") ?? ""}
            selectionEnd={selected?.[1]?.format("YYYY-MM-DD") ?? ""}
            propertyId={propertyId}
            propertyData={propertyData}
          />
        </SlidePane>
      )}

      {showManageBookingRules !== null && (
        <SlidePane isShowing={showManageBookingRules}>
          <ManageBookingRules
            onBack={() => setShowManageBookingRules(false)}
            onClose={onClose}
            selectedDatesNode={selectedDatesNode}
            selectionStart={selected?.[0]?.format("YYYY-MM-DD") ?? ""}
            selectionEnd={selected?.[1]?.format("YYYY-MM-DD") ?? ""}
            propertyId={propertyId}
            propertyData={propertyData}
          />
        </SlidePane>
      )}
    </>
  );
};

export default ManageDates;
