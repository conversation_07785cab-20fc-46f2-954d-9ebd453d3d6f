"use client";

import { saveCalendarLink } from "@/app/actions/calendar";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import { ProgressStatus } from "@/types/common";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
};

const CalendarLinkForm = ({ propertyId }: Props) => {
  const [error, setError] = useState<string>("");
  const [link, setLink] = useState<string>("");
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const preSubmitCheck = useCallback(
    (value?: string) => {
      let err = "";
      const linkVal = value ? value : link;
      if (!linkVal || linkVal === "") {
        err = "Link is required";
      }

      if (linkVal && linkVal !== "" && !linkVal?.split("?")[0].match(/.ics/g)) {
        err = "Link should be an .ics file URL";
      }
      setError(err);
      return err;
    },
    [link]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      setLink(value);
      preSubmitCheck(value);
    },
    [preSubmitCheck]
  );

  const onSave = useCallback(() => {
    const _error = preSubmitCheck();
    if (_error) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    saveCalendarLink({
      listing: propertyId,
      url: link,
    })
      .then((data) => {
        revalidateTagByName(`property-details-${propertyId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Calendar link successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [link, preSubmitCheck, propertyId]);

  return (
    <div className="w-full flex flex-col md:flex-row gap-2 border p-4 rounded-lg">
      <div className="flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Calendar Link</p>
        <div className="w-[70%]">
          <Input
            name="address"
            className="text-xs p-2 w-full"
            placeholder="Calendar Link"
            value={link}
            onChange={onChangeTextInput}
            helperText={error}
            error={!!error}
          />
        </div>
      </div>
      <div className="w-full md:w-[120px] flex items-center justify-end">
        <Button
          onClick={onSave}
          className="w-[120px] text-xs"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save/Update
        </Button>
      </div>
    </div>
  );
};

export default CalendarLinkForm;
