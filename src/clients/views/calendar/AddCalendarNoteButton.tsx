"use client";

import But<PERSON> from "@/clients/ui/button";
import { Comment } from "@/types/service-providers";
import dynamic from "next/dynamic";
import { useCallback, useState } from "react";

const EditCalendarNoteModal = dynamic(() => import("./EditCalendarNoteModal"));

type Props = {
  propertyId: number;
};

const AddCalendarNoteButton = ({ propertyId }: Props) => {
  const [showEdit, setShowEdit] = useState<boolean>(false);

  const onClickAdd = useCallback(() => {
    setShowEdit(true);
  }, []);

  const onCloseAdd = useCallback(() => {
    setShowEdit(false);
  }, []);

  return (
    <>
      <Button onClick={onClickAdd} className="!text-xs min-w-[120px]">
        + Add Note
      </Button>

      {showEdit && (
        <EditCalendarNoteModal onClose={onCloseAdd} propertyId={propertyId} />
      )}
    </>
  );
};

export default AddCalendarNoteButton;
