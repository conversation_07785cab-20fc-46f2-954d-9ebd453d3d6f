"use client";

import { memo, useMemo } from "react";

import { getFirstDayOfMonth, getNumberOfDaysInMonth } from "@/utils/calendar";

import classNames from "classnames";
import dayjs from "dayjs";
import localeData from "dayjs/plugin/localeData";

export const TOTAL_SLOTS_IN_CALENDAR = 42;

dayjs.extend(localeData);
const MONTHS = dayjs.months();

type Props = {
  month: number;
  year: number;
};

const YearViewMonthItem = ({ month, year }: Props) => {
  const numberOfDaysInMonth = useMemo(
    () => getNumberOfDaysInMonth(year, month),
    [year, month]
  );
  const firstDayOfMonth = useMemo(
    () => getFirstDayOfMonth(year, month),
    [year, month]
  );
  const numberOfDaysLastMonth = useMemo(
    () =>
      month > 0
        ? getNumberOfDaysInMonth(year, month - 1)
        : getNumberOfDaysInMonth(year - 1, 11),
    [year, month]
  );

  return (
    <div className="Calendar">
      <p className="text-center text-xs">{MONTHS[month]}</p>

      <div className="mt-2 flex">
        <div className="grid flex-grow grid-cols-7 gap-0">
          {Array(firstDayOfMonth)
            .fill(0)
            .map((_, index) => (
              <div
                className="bg-white border aspect-square rounded-full"
                key={`${MONTHS[month - 1]}/${year}- ${
                  index + (numberOfDaysLastMonth - firstDayOfMonth) + 1
                }`}
              />
            ))}

          {Array(numberOfDaysInMonth)
            .fill(0)
            .map((_, index) => (
              <div
                key={`${MONTHS[month]}/${year}- ${index + 1}`}
                className={classNames(
                  "bg-white border aspect-square rounded-full",
                  {
                    "!bg-carolina-blue": index > 0 && index < 15,
                    "!bg-pending": index > 24 && index < 28,
                    "!bg-outline": index > 16 && index < 22,
                  }
                )}
              />
            ))}

          {Array(
            (TOTAL_SLOTS_IN_CALENDAR - firstDayOfMonth - numberOfDaysInMonth) %
              7
          )
            .fill(0)
            .map((_, index) => (
              <div
                className="bg-white border aspect-square rounded-full"
                key={`${MONTHS[month + 1]}/${year}- ${index + 1}`}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

export default memo(YearViewMonthItem);
