"use client";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";
import ManageDates from "@/clients/views/calendar/Manage/ManageDates";
import { PropertyAvailability } from "@/types/calendar";
import { Property } from "@/types/property";
import { useContext } from "react";
import dayjs from "dayjs";
import classNames from "classnames";
import ManageCalendarSelectedDates from "@/app/components/calendar/ManageCalendarSelectedDates";

type Props = {
  propertyId: number;
  propertyData: Property;
  availabilityData: PropertyAvailability[];
};

const CalendarSidebar = ({
  propertyId,
  propertyData,
  availabilityData,
}: Props) => {
  const { selected, selectedAvailabilityId } = useContext(
    ManageCalendarContext
  );
  return (
    <div
      className={classNames(
        `fixed md:relative bg-white z-[10] w-[100%] md:w-[210px] xl:w-[260px] bottom-0 md:bottom-[initial] md:block shadow-sidebar-mobile md:shadow-none rounded-lg rounded-b-none md:rounded-0 md:min-h-full overflow-y-hidden md:overflow-y-scroll`,
        {
          hidden:
            !dayjs(selected?.[0]).isValid() && !dayjs(selected?.[1]).isValid(),
        }
      )}
    >
      {dayjs(selected?.[0]).isValid() ||
      dayjs(selected?.[1]).isValid() ||
      selectedAvailabilityId ? (
        <ManageDates
          propertyId={propertyId}
          propertyData={propertyData}
          availabilityData={availabilityData}
          selectedDatesNode={
            <ManageCalendarSelectedDates
              selectionStart={selected?.[0]?.format("YYYY-MM-DD") ?? ""}
              selectionEnd={selected?.[1]?.format("YYYY-MM-DD") ?? ""}
            />
          }
        />
      ) : (
        <p className="text-xs py-4">Please select a date range to manage</p>
      )}
    </div>
  );
};

export default CalendarSidebar;
