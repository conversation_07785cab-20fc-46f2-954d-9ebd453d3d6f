"use client";

import { validateCalendar } from "@/app/actions/calendar";
import Button from "@/clients/ui/button";
import { memo, useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
};

const ValidateButton = ({ propertyId }: Props) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const onValidate = useCallback(() => {
    setIsSubmitting(true);
    validateCalendar(propertyId)
      .then((data) => {
        console.log(data);
        setIsSubmitting(false);
        toast.success("Calendar validated.");
      })
      .catch((err) => {
        console.log(err);
        setIsSubmitting(false);
        toast.error("Failed to validate calendar");
      });
  }, [propertyId]);

  return (
    <Button
      className="text-xs border-black border px-4 py-2 font-bold rounded mr-2"
      intent="outline"
      onClick={onValidate}
      disabled={isSubmitting}
    >
      {isSubmitting ? "Validating..." : "Validate"}
    </Button>
  );
};

export default memo(ValidateButton);
