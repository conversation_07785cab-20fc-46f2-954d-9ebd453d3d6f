"use client";

import { useCallback, useContext, useMemo } from "react";

import { CalendarViewType } from "@/types/calendar";

import classNames from "classnames";
import dayjs from "dayjs";
import localeData from "dayjs/plugin/localeData";
import { ManageCalendarContext } from "@/clients/contexts/ManageCalendarContext";

dayjs.extend(localeData);
const MONTHS = dayjs.months();

const CalendarHeader = () => {
  const { currentMonth, currentYear, setCurrentYear, view } = useContext(
    ManageCalendarContext
  );
  const yearsArray = useMemo(
    () => [
      dayjs().year() - 1,
      dayjs().year(),
      dayjs().year() + 1,
      dayjs().year() + 2,
    ],
    []
  );

  const onSelectYear = useCallback(
    (year: number) => {
      setCurrentYear(year);
    },
    [setCurrentYear]
  );

  return (
    <>
      {view === CalendarViewType.YEAR ? (
        <>
          <div className="flex text-[19px] font-bold gap-4">
            {yearsArray.map((_year, index) => (
              <p
                className={classNames("relative text-[#CCC] cursor-pointer", {
                  "text-black": _year === currentYear,
                })}
                key={index}
                onClick={() => onSelectYear(_year)}
              >
                {_year}
                {_year === currentYear && (
                  <span className="absolute left-[50%] -translate-x-[50%] bottom-0 mt-1.5 w-7/12 h-[1px] bg-black" />
                )}
              </p>
            ))}
          </div>
        </>
      ) : (
        <>
          <p className="invisible tracking-[1.68px] font-bold text-base">
            {MONTHS[currentMonth]} {currentYear}
          </p>
        </>
      )}
    </>
  );
};

export default CalendarHeader;
