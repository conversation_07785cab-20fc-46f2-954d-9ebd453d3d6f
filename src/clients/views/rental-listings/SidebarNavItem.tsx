'use client';

import classNames from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { memo, useMemo } from 'react';
import { twMerge } from 'tailwind-merge';

type Props = {
  href: string;
  title: string;
  className?: string;
  pagePath: string;
};

const SidebarNavItem = ({ href, title, className = '', pagePath }: Props) => {
  const pathname = usePathname();

  const isActive = useMemo(() => {
    const fragments = pathname.split('/');
    const subpage = fragments?.[3] ?? '';
    if (pagePath === '' && subpage === '') return true;

    const pathSegment = pagePath.replace('/', '').toLowerCase();
    const currentSubpage = subpage.toLowerCase();

    return pathSegment !== '' && currentSubpage.includes(pathSegment);
  }, [pagePath, pathname]);
  return (
    <Link
      href={href}
      className={twMerge(
        classNames('px-4 py-2 text-sm', {
          'bg-white rounded-[10px] rounded-r-none text-[#2C3E50] font-bold':
            isActive,
        }),
        className
      )}
    >
      {title}
    </Link>
  );
};

export default memo(SidebarNavItem);
