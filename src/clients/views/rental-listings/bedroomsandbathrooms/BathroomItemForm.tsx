"use client";

import { BathroomPayload } from "@/utils/bathrooms";
import { BathroomFormErrors } from "./BathroomsForm";
import Checkbox from "@/clients/ui/checkbox";
import { useCallback, useMemo } from "react";
import Select from "@/clients/ui/select";
import { ItemType, Property } from "@/types/property";
import FormHelperText from "@/app/ui/form-helper-text";
import { TrashIcon } from "@heroicons/react/24/outline";

type Props = {
  index: number;
  bathroomData: BathroomPayload;
  // floorLevelTypes: ItemType[];
  bathroomTypes: ItemType[];
  onChangeBathroomData: (index: number, data: BathroomPayload) => void;
  errors: BathroomFormErrors;
  property: Property;
  onDeleteBathroomClicked: (index: number) => void;
};

const BathroomItemForm = ({
  index,
  bathroomData,
  errors,
  onChangeBathroomData,
  bathroomTypes,
  property,
  onDeleteBathroomClicked,
}: Props) => {
  const bedroomOptions = useMemo(
    () =>
      property?.bedrooms?.map((_bd, index) => ({
        id: _bd.bedroom_uuid,
        name: `Bedroom ${index + 1}`,
      })),
    [property?.bedrooms]
  );

  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChangeBathroomData(index, {
        ...bathroomData,
        [name]: checked,
      });
    },
    [bathroomData, index, onChangeBathroomData]
  );

  const onTogglePrivateBedroom = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChangeBathroomData(index, {
        ...bathroomData,
        [name]: checked,
        bedroom: !checked ? null : bathroomData.bedroom,
      });
    },
    [bathroomData, index, onChangeBathroomData]
  );

  const onChangeBathroomType = useCallback(
    (value: { id: number | string }) => {
      onChangeBathroomData(index, {
        ...bathroomData,
        type: Number(value.id),
      });
    },
    [bathroomData, index, onChangeBathroomData]
  );

  const onChangeAssociatedBed = useCallback(
    (value: { id: number | string }) => {
      onChangeBathroomData(index, {
        ...bathroomData,
        bedroom: value.id as string,
      });
    },
    [bathroomData, index, onChangeBathroomData]
  );

  return (
    <div className="py-2">
      <div className="flex items-center justify-between p-2 mt-4 bg-gray-20">
        <p className="text-sm font-bold">Bathroom {index + 1}</p>
        <TrashIcon
          onClick={() => onDeleteBathroomClicked(index)}
          className="w-5 h-5 cursor-pointer text-error"
        />
      </div>
      <hr />
      <div className="flex items-center gap-6 flex-wrap my-4">
        <div className="flex items-center gap-2">
          <p className="text-xs">Bidet</p>
          <Checkbox
            name="bidet"
            checked={bathroomData?.bidet}
            onChange={onToggleCheckobox}
          />
        </div>
        <div className="flex items-center gap-2">
          <p className="text-xs">Combination Tub</p>
          <Checkbox
            name="combination_tub"
            checked={bathroomData?.combination_tub}
            onChange={onToggleCheckobox}
          />
        </div>
        <div className="flex items-center gap-2">
          <p className="text-xs">Toilet</p>
          <Checkbox
            name="toilet"
            checked={bathroomData?.toilet}
            onChange={onToggleCheckobox}
          />
        </div>
        <div className="flex items-center gap-2">
          <p className="text-xs">Tub</p>
          <Checkbox
            name="tub"
            checked={bathroomData?.tub}
            onChange={onToggleCheckobox}
          />
        </div>
        <div className="flex items-center gap-2">
          <p className="text-xs">Shower</p>
          <Checkbox
            name="shower"
            checked={bathroomData?.shower}
            onChange={onToggleCheckobox}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-[90%]">
        <p className="w-[50%] text-xs">Bathroom Type</p>
        <div className="w-[50%]">
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            placeholder="Please select"
            options={bathroomTypes}
            value={bathroomData.type}
            onChange={onChangeBathroomType}
          />
        </div>
      </div>
      <div className="flex items-center gap-2 w-full">
        <p className="text-xs">Private to a bedroom?</p>
        <input
          type="checkbox"
          name="checked"
          className="toggle toggle-info toggle-sm"
          checked={bathroomData?.checked}
          onChange={onTogglePrivateBedroom}
        />
      </div>
      {bathroomData.checked && (
        <>
          <div className="pt-2 flex items-center gap-2 w-[90%]">
            <p className="w-[50%] text-xs">Associated with which Bedroom?</p>
            <div className="w-[50%]">
              <Select
                className="text-xs font-bold w-full px-2 py-2"
                bodyClassName="max-h-[200px] overflow-y-scroll"
                placeholder="Please select"
                options={bedroomOptions}
                value={bathroomData.bedroom}
                onChange={onChangeAssociatedBed}
              />
            </div>
          </div>
          {!!errors[index]?.attach_bedroom && (
            <FormHelperText error>
              {errors[index]?.attach_bedroom}
            </FormHelperText>
          )}
        </>
      )}
    </div>
  );
};

export default BathroomItemForm;
