"use client";

import Select from "@/clients/ui/select";
import { ItemType } from "@/types/property";
import { BedroomPayload } from "@/utils/bedrooms";
import { TrashIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import { useCallback, useState } from "react";
import { BedroomFormErrors } from "./BedroomsForm";
import FormHelperText from "@/app/ui/form-helper-text";

const SELECT_OPTIONS = Array(20)
  .fill(1)
  .map((_, index) => ({ id: index + 1, name: (index + 1).toString() }));

type Props = {
  index: number;
  bedroomData: BedroomPayload;
  floorLevelTypes: ItemType[];
  bedTypes: ItemType[];
  onChangeBedroomData: (index: number, data: BedroomPayload) => void;
  errors: BedroomFormErrors;
  onDeleteBedroomClicked: (index: number) => void;
};

const BedroomItemForm = ({
  index,
  floorLevelTypes,
  bedTypes,
  bedroomData,
  onChangeBedroomData,
  errors,
  onDeleteBedroomClicked,
}: Props) => {
  const onChangeFloor = useCallback(
    (value: { id: number | string; name: string }) => {
      onChangeBedroomData(index, {
        ...bedroomData,
        floor_level: Number(value.id),
      });
    },
    [bedroomData, index, onChangeBedroomData]
  );

  const onChangeBedType = useCallback(
    (id: number, bedIndex: number) => {
      onChangeBedroomData(index, {
        ...bedroomData,
        beds: [
          ...bedroomData.beds.slice(0, bedIndex),
          {
            type: id,
            number: bedroomData.beds[bedIndex].number,
          },
          ...bedroomData.beds.slice(bedIndex + 1),
        ],
      });
    },
    [bedroomData, index, onChangeBedroomData]
  );

  const onChangeBedCount = useCallback(
    (count: number, bedIndex: number) => {
      onChangeBedroomData(index, {
        ...bedroomData,
        beds: [
          ...bedroomData.beds.slice(0, bedIndex),
          {
            type: bedroomData.beds[bedIndex].type,
            number: count,
          },
          ...bedroomData.beds.slice(bedIndex + 1),
        ],
      });
    },
    [bedroomData, index, onChangeBedroomData]
  );

  const onAddBed = useCallback(() => {
    onChangeBedroomData(index, {
      ...bedroomData,
      beds: [
        ...bedroomData.beds,
        {
          type: null,
          number: 1,
        },
      ],
    });
  }, [bedroomData, index, onChangeBedroomData]);

  const onRemoveBed = useCallback(
    (bedIndex: number) => {
      onChangeBedroomData(index, {
        ...bedroomData,
        beds: [
          ...bedroomData.beds.slice(0, bedIndex),
          ...bedroomData.beds.slice(bedIndex + 1),
        ],
      });
    },
    [bedroomData, index, onChangeBedroomData]
  );

  return (
    <div className="py-3">
      <div className="flex items-center justify-between p-2 mt-4 bg-gray-20">
        <p className="text-sm font-bold">Bedroom {index + 1}</p>
        <TrashIcon
          onClick={() => onDeleteBedroomClicked(index)}
          className={classNames("w-5 h-5 cursor-pointer text-error", {})}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Floor Level</p>
        <div className="w-[50%]">
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            placeholder="Please select"
            options={floorLevelTypes}
            value={bedroomData?.floor_level}
            onChange={onChangeFloor}
          />
        </div>
      </div>
      {!!errors[index]?.floor_level && (
        <div className="my-1">
          <FormHelperText error>{errors[index]?.floor_level}</FormHelperText>
        </div>
      )}
      {bedroomData.beds?.map((_bed, _bedIndex) => (
        <div key={_bedIndex}>
          <div className="py-2 flex items-center gap-2 w-full">
            <p className="w-[20%] text-xs">Type of bed</p>
            <div className="flex-grow flex items-center gap-2">
              <Select
                className="text-xs font-bold w-full px-2 py-2"
                bodyClassName="max-h-[200px] overflow-y-scroll"
                placeholder="Please select"
                options={bedTypes}
                value={_bed.type}
                onChange={(value: { id: number | string }) =>
                  onChangeBedType(Number(value.id), _bedIndex)
                }
              />
              <Select
                className="text-xs font-bold w-full px-2 py-2"
                bodyClassName="max-h-[200px] overflow-y-scroll"
                placeholder="Please select"
                options={SELECT_OPTIONS}
                value={_bed.number}
                onChange={(value: { id: number | string }) =>
                  onChangeBedCount(Number(value.id), _bedIndex)
                }
              />
            </div>
            <TrashIcon
              onClick={() => onRemoveBed(_bedIndex)}
              className={classNames("w-5 h-5 cursor-pointer text-error", {
                invisible: _bedIndex === 0,
              })}
            />
          </div>
          {!!errors[index]?.beds?.[_bedIndex] && (
            <FormHelperText error>
              {errors[index]?.beds?.[_bedIndex]}
            </FormHelperText>
          )}
        </div>
      ))}
      <span
        className="underline text-xs font-medium py-2 cursor-pointer"
        onClick={onAddBed}
      >
        + Add bed type
      </span>
    </div>
  );
};

export default BedroomItemForm;
