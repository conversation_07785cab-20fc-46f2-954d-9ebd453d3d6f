"use client";

import Input from "@/clients/ui/input";
import { FormValues } from "./AmenitiesFormWrapper";
import { useCallback } from "react";
import MultiSelect from "@/clients/ui/multi-select";
import Select from "@/clients/ui/select";
import AmenityItem from "./AmenityItem";

const SELECT_OPTIONS = Array(101)
  .fill(1)
  .map((_, index) => ({ id: index, name: index.toString() }));

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
  acTypes: { id: number; name: string }[];
  heatingTypes: { id: number; name: string }[];
};

const EssentialsForm = ({
  formState,
  onChange,
  errors,
  acTypes,
  heatingTypes,
}: Props) => {
  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeACType = useCallback(
    (type: { id: any; name: string }) => {
      const types = formState?.ac_types.includes(type.id)
        ? formState?.ac_types?.filter((_t) => _t !== type.id)
        : [...formState.ac_types, type.id];
      onChange(types, "ac_types");
    },
    [formState.ac_types, onChange]
  );

  const onRemoveACType = useCallback(
    (id: any) => {
      const types = formState?.ac_types?.filter((_t) => _t !== id);
      onChange(types, "ac_types");
    },
    [formState?.ac_types, onChange]
  );

  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold p-2 mt-6 bg-gray-20">Essentials</p>
      <AmenityItem
        isChecked={formState.air_conditioning}
        onCheck={onToggleCheckobox}
        name="air_conditioning"
        title="Air Conditioning"
      >
        <MultiSelect
          className="text-xs font-bold w-full px-2 py-2"
          placeholder="A/C type"
          options={acTypes}
          value={formState.ac_types}
          isClearable
          onRemove={onRemoveACType}
          onChange={onChangeACType}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.clothes_dryer}
        onCheck={onToggleCheckobox}
        name="clothes_dryer"
        title="Dryer (clothes)"
      />

      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.fan}
        onCheck={onToggleCheckobox}
        name="fan"
        title="Fans"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder=""
          name="fan_quantity"
          options={SELECT_OPTIONS}
          value={formState?.fan_quantity ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.fireplace}
        onCheck={onToggleCheckobox}
        name="fireplace"
        title="Fireplaces (working)"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder=""
          name="fireplace_quantity"
          options={SELECT_OPTIONS}
          value={formState?.fireplace_quantity ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        title="Hair Dryer"
        isChecked={formState.hair_dryer}
        name="hair_dryer"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Heating"
        isChecked={formState.heating}
        name="heating"
        onCheck={onToggleCheckobox}
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder=""
          name="heating_type"
          options={heatingTypes}
          value={formState?.heating_type ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        title="Iron"
        isChecked={formState.iron}
        name="iron"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Iron Board"
        isChecked={formState.ironing_board}
        name="ironing_board"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Linens Provided"
        isChecked={formState.linen}
        name="linen"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Outdoor Furniture"
        isChecked={formState.outdoor_furniture}
        name="outdoor_furniture"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Towels Provided"
        isChecked={formState.towel}
        name="towel"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Washing Machine"
        isChecked={formState.washing_machine}
        name="washing_machine"
        onCheck={onToggleCheckobox}
      />
      <hr className="my-1" />
      <AmenityItem
        title="Wifi"
        isChecked={formState.wifi}
        name="wifi"
        onCheck={onToggleCheckobox}
      />
      {formState.wifi && (
        <>
          <hr className="my-1" />
          <div className="py-2 flex items-center gap-2 w-full">
            <p className="w-[50%] text-xs">WIFI Name</p>
            <div className="w-[50%]">
              <Input
                name="wifi_network_name"
                className="text-xs p-2 w-full"
                placeholder="WIFI Name"
                value={formState?.wifi_network_name ?? ""}
                onChange={onChangeTextInput}
                helperText={errors?.wifi_network_name ?? ""}
                error={!!errors?.wifi_network_name?.length}
              />
            </div>
          </div>
          <hr className="my-1" />
          <div className="py-2 flex items-center gap-2 w-full">
            <p className="w-[50%] text-xs">WIFI PW</p>
            <div className="w-[50%]">
              <Input
                name="wifi_password"
                className="text-xs p-2 w-full"
                placeholder="WIFI PW"
                value={formState?.wifi_password ?? ""}
                onChange={onChangeTextInput}
                helperText={errors?.wifi_password ?? ""}
                error={!!errors?.wifi_password?.length}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default EssentialsForm;
