"use client";

import { useCallback } from "react";
import { FormValues } from "./AmenitiesFormWrapper";
import Select from "@/clients/ui/select";
import AmenityItem from "./AmenityItem";

const SELECT_OPTIONS = Array(101)
  .fill(1)
  .map((_, index) => ({ id: index, name: index.toString() }));

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
  tvServices: { id: number; name: string }[];
};

const EntertainmentForm = ({
  formState,
  onChange,
  errors,
  tvServices,
}: Props) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold p-2 mt-6 bg-gray-20">Entertainment</p>
      <AmenityItem
        isChecked={formState.bluetooth_speaker}
        onCheck={onToggleCheckobox}
        name="bluetooth_speaker"
        title="Bluetooth Speakers"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.book}
        onCheck={onToggleCheckobox}
        name="book"
        title="Books"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.dvd_player}
        onCheck={onToggleCheckobox}
        name="dvd_player"
        title="DVD Player"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.tv}
        onCheck={onToggleCheckobox}
        name="tv"
        title="TV"
        leftClassName="w-[20%]"
        rightClassName="w-[80%] flex items-center gap-2"
      >
        <>
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            name="tv_service"
            placeholder=""
            options={tvServices}
            value={formState?.tv_service ?? 0}
            onChange={onChangeSelect}
          />
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            name="tv_quantity"
            placeholder=""
            options={SELECT_OPTIONS}
            value={formState?.tv_quantity ?? 0}
            onChange={onChangeSelect}
          />
        </>
      </AmenityItem>
    </>
  );
};

export default EntertainmentForm;
