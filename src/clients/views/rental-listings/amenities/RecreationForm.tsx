"use client";

import { useCallback } from "react";
import { FormValues } from "./AmenitiesFormWrapper";
import Select from "@/clients/ui/select";
import Checkbox from "@/clients/ui/checkbox";
import AmenityItem from "./AmenityItem";

const SELECT_OPTIONS = Array(101)
  .fill(1)
  .map((_, index) => ({ id: index, name: index.toString() }));

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
  poolTypes: { id: number; name: string }[];
};

const RecreationForm = ({ formState, onChange, errors, poolTypes }: Props) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold p-2 mt-6 bg-gray-20">Recreation</p>
      <AmenityItem
        isChecked={formState.beach_chair}
        onCheck={onToggleCheckobox}
        name="beach_chair"
        title="Beach Chairs"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="beach_chair_quantity"
          placeholder=""
          options={SELECT_OPTIONS}
          value={formState?.beach_chair_quantity ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.beach_towel}
        onCheck={onToggleCheckobox}
        name="beach_towel"
        title="Beach Towels"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="beach_towel_quantity"
          placeholder=""
          options={SELECT_OPTIONS}
          value={formState?.beach_towel_quantity ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.beach_umbrella}
        onCheck={onToggleCheckobox}
        name="beach_umbrella"
        title="Beach Umbrella"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.bicycle}
        onCheck={onToggleCheckobox}
        name="bicycle"
        title="Bycicles"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.cooler}
        onCheck={onToggleCheckobox}
        name="cooler"
        title="Cooler"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.gym}
        onCheck={onToggleCheckobox}
        name="gym"
        title="Fitness Room / Gym"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.hot_tub_or_spa}
        onCheck={onToggleCheckobox}
        name="hot_tub_or_spa"
        title="Hot Tub / Spa"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.jacuzzi}
        onCheck={onToggleCheckobox}
        name="jacuzzi"
        title="Jacuzzi"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.outdoor_shower}
        onCheck={onToggleCheckobox}
        name="outdoor_shower"
        title="Outdoor Shower"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.pool}
        onCheck={onToggleCheckobox}
        name="pool"
        title="Pool"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="pool_type"
          placeholder=""
          options={poolTypes}
          value={formState?.pool_type ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
    </>
  );
};

export default RecreationForm;
