"use client";

import { MouseEvent, useCallback } from "react";
import { FormValues } from "./AmenitiesFormWrapper";
import Select from "@/clients/ui/select";
import AmenityItem from "./AmenityItem";

const SELECT_OPTIONS = Array(101)
  .fill(1)
  .map((_, index) => ({ id: index, name: index.toString() }));

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
  grillTypes: { id: number; name: string }[];
  coffeeMakerTypes: { id: number; name: string }[];
  toasterTypes: { id: number; name: string }[];
};

const KitchenAndDiningForm = ({
  formState,
  onChange,
  errors,
  grillTypes,
  coffeeMakerTypes,
  toasterTypes,
}: Props) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold p-2 mt-6 bg-gray-20">
        Kitchen and Dining
      </p>
      <AmenityItem
        isChecked={formState.bbq_tool}
        onCheck={onToggleCheckobox}
        name="bbq_tool"
        title="BBQ Tools"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.blender}
        onCheck={onToggleCheckobox}
        name="blender"
        title="Blender"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.coffee_maker}
        onCheck={onToggleCheckobox}
        name="coffee_maker"
        title="Coffee Maker"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="coffee_maker_type"
          placeholder=""
          options={coffeeMakerTypes}
          value={formState?.coffee_maker_type ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.counter_seating}
        onCheck={onToggleCheckobox}
        name="counter_seating"
        title="Counter Seating"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.dining_area}
        onCheck={onToggleCheckobox}
        name="dining_area"
        title="Dining Area"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.dining_room}
        onCheck={onToggleCheckobox}
        name="dining_room"
        title="Dining Room"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.dishwasher}
        onCheck={onToggleCheckobox}
        name="dishwasher"
        title="Dishwasher"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.dish_or_cup_or_utensil}
        onCheck={onToggleCheckobox}
        name="dish_or_cup_or_utensil"
        title="Dishes, Cups & Utensils"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.food_processor}
        onCheck={onToggleCheckobox}
        name="food_processor"
        title="Food Processor"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.grill}
        onCheck={onToggleCheckobox}
        name="grill"
        title="Grill / BBQ"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="grill_type"
          placeholder=""
          options={grillTypes}
          value={formState?.grill_type ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.ice_maker}
        onCheck={onToggleCheckobox}
        name="ice_maker"
        title="Ice Maker"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.ice_tray}
        onCheck={onToggleCheckobox}
        name="ice_tray"
        title="Ice Trays"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.lobster_pot}
        onCheck={onToggleCheckobox}
        name="lobster_pot"
        title="Lobster Pot"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.lobster_utensil}
        onCheck={onToggleCheckobox}
        name="lobster_utensil"
        title="Lobster Utensils"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.microwave}
        onCheck={onToggleCheckobox}
        name="microwave"
        title="Microwave"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.outdoor_dining_area}
        onCheck={onToggleCheckobox}
        name="outdoor_dining_area"
        title="Outdoor Dining"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="seating"
          placeholder=""
          options={SELECT_OPTIONS}
          value={formState?.seating ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.oven}
        onCheck={onToggleCheckobox}
        name="oven"
        title="Oven"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.pot_or_pan}
        onCheck={onToggleCheckobox}
        name="pot_or_pan"
        title="Pots & Pan"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.refrigerator}
        onCheck={onToggleCheckobox}
        name="refrigerator"
        title="Refrigerator"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.safe}
        onCheck={onToggleCheckobox}
        name="safe"
        title="Safe"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.stove}
        onCheck={onToggleCheckobox}
        name="stove"
        title="Stove"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.tea_kettle}
        onCheck={onToggleCheckobox}
        name="tea_kettle"
        title="Tea Kettle"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.toaster}
        onCheck={onToggleCheckobox}
        name="toaster"
        title="Toaster"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="toaster_type"
          placeholder=""
          options={toasterTypes}
          value={formState?.toaster_type ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
    </>
  );
};

export default KitchenAndDiningForm;
