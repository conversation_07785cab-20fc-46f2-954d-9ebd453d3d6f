"use client";

import useForm from "@/hooks/useForm";
import { Nullable, ProgressStatus } from "@/types/common";
import { Property } from "@/types/property";
import { useCallback, useState } from "react";
import Button from "@/clients/ui/button";
import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";
import EssentialsForm from "./EssentialsForm";
import KitchenAndDiningForm from "./KitchenAndDiningForm";
import RecreationForm from "./RecreationForm";
import EntertainmentForm from "./EntertainmentForm";
import OutdoorsForm from "./OutdoorsForm";

type Props = {
  property: Property;
  acTypes: { id: number; name: string }[];
  heatingTypes: { id: number; name: string }[];
  coffeeMakerTypes: { id: number; name: string }[];
  stoveTypes: { id: number; name: string }[];
  toasterTypes: { id: number; name: string }[];
  grillTypes: { id: number; name: string }[];
  poolTypes: { id: number; name: string }[];
  tvServices: { id: number; name: string }[];
};

export type FormValues = {
  // Essentials
  air_conditioning: boolean;
  ac_types: number[];
  clothes_dryer: boolean;
  fan: boolean;
  fan_quantity: Nullable<number>;
  fireplace: boolean;
  fireplace_quantity: Nullable<number>;
  heating: boolean;
  heating_type: Nullable<number>;
  hair_dryer: boolean;
  iron: boolean;
  ironing_board: boolean;
  linen: boolean;
  outdoor_furniture: boolean;
  towel: boolean;
  washing_machine: boolean;
  wifi: boolean;
  wifi_network_name: Nullable<string>;
  wifi_password: Nullable<string>;
  // Kitchen & Dining
  bbq_tool: boolean;
  blender: boolean;
  coffee_maker: boolean;
  coffee_maker_type: Nullable<number>;
  counter_seating: boolean;
  dining_area: boolean;
  dining_room: boolean;
  dishwasher: boolean;
  dish_or_cup_or_utensil: boolean;
  food_processor: boolean;
  grill: boolean;
  grill_type: Nullable<number>;
  ice_maker: boolean;
  ice_tray: boolean;
  lobster_pot: boolean;
  lobster_utensil: boolean;
  microwave: boolean;
  outdoor_dining_area: boolean;
  seating: Nullable<number>;
  oven: boolean;
  pot_or_pan: boolean;
  refrigerator: boolean;
  safe: boolean;
  stove: boolean;
  tea_kettle: boolean;
  toaster: boolean;
  toaster_type: Nullable<number>;
  // Recreation
  beach_chair: boolean;
  beach_chair_quantity: number;
  beach_towel: boolean;
  beach_towel_quantity: number;
  beach_umbrella: boolean;
  bicycle: boolean;
  cooler: boolean;
  gym: boolean;
  hot_tub_or_spa: boolean;
  jacuzzi: boolean;
  outdoor_shower: boolean;
  pool: boolean;
  pool_type: Nullable<number>;
  // Entertainment
  bluetooth_speaker: boolean;
  book: boolean;
  dvd_player: boolean;
  tv: boolean;
  tv_quantity: number;
  tv_service: Nullable<number>;
  // Outdoors
  deck: boolean;
  lawn_or_garden: boolean;
  parking: boolean;
  parking_quantity: Nullable<number>;
  patio: boolean;
  porch: boolean;
};

const AmenitiesFormWrapper = ({
  property,
  acTypes,
  heatingTypes,
  grillTypes,
  poolTypes,
  tvServices,
  coffeeMakerTypes,
  toasterTypes,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      air_conditioning: property.air_conditioning,
      ac_types: property?.ac_types.map((_a) => _a.id) ?? [],
      clothes_dryer: property.clothes_dryer,
      fan: property.fan,
      fan_quantity: property.fan_quantity,
      fireplace: property.fireplace,
      fireplace_quantity: property.fireplace_quantity,
      heating: property.heating,
      heating_type: property?.heating_type?.id,
      hair_dryer: property.hair_dryer,
      iron: property.iron,
      ironing_board: property.ironing_board,
      linen: property.linen,
      outdoor_furniture: property.outdoor_furniture,
      towel: property.towel,
      washing_machine: property.washing_machine,
      wifi: property.wifi,
      wifi_network_name: property.wifi_network_name,
      wifi_password: property.wifi_password,
      bbq_tool: property.bbq_tool,
      blender: property.blender,
      coffee_maker: property?.coffee_maker,
      coffee_maker_type: property?.coffee_maker_type?.id,
      counter_seating: property.counter_seating,
      dining_area: property.dining_area,
      dining_room: property.dining_room,
      dishwasher: property.dishwasher,
      dish_or_cup_or_utensil: property.dish_or_cup_or_utensil,
      food_processor: property.food_processor,
      grill: property.grill,
      grill_type: property?.grill_type?.id,
      ice_maker: property.ice_maker,
      ice_tray: property.ice_tray,
      lobster_pot: property.lobster_pot,
      lobster_utensil: property.lobster_utensil,
      microwave: property.microwave,
      outdoor_dining_area: property.outdoor_dining_area,
      seating: property?.seating ?? 0,
      oven: property.oven,
      pot_or_pan: property.pot_or_pan,
      refrigerator: property.refrigerator,
      safe: property.safe,
      stove: property.stove,
      tea_kettle: property.tea_kettle,
      toaster: property.toaster,
      toaster_type: property?.toaster_type?.id ?? null,
      beach_chair: property.beach_chair,
      beach_chair_quantity: property.beach_chair_quantity,
      beach_towel: property.beach_towel,
      beach_towel_quantity: property.beach_towel_quantity,
      beach_umbrella: property.beach_umbrella,
      bicycle: property.bicycle,
      cooler: property.cooler,
      gym: property.gym,
      hot_tub_or_spa: property.hot_tub_or_spa,
      jacuzzi: property.jacuzzi,
      outdoor_shower: property.outdoor_shower,
      pool: property.pool,
      pool_type: property?.pool_type?.id,
      bluetooth_speaker: property.bluetooth_speaker,
      book: property.book,
      dvd_player: property.dvd_player,
      tv: property.tv,
      tv_quantity: property.tv_quantity,
      tv_service: property?.tv_service?.id,
      deck: property.deck,
      lawn_or_garden: property.lawn_or_garden,
      parking: property.parking,
      parking_quantity: property.parking_quantity,
      patio: property.patio,
      porch: property.porch,
    },
    {}
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      ...formState,
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id]);

  return (
    <div className="md:border p-2 md:p-4 rounded-lg">
      <div className="mb-2 md:mb-4 w-full flex items-center justify-between">
        <p className="text-sm font-bold">Amenities</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="w-full h-min">
          <EssentialsForm
            formState={formState}
            errors={errors}
            onChange={onChange}
            acTypes={acTypes}
            heatingTypes={heatingTypes}
          />
        </div>
        <div className="w-full h-min">
          <KitchenAndDiningForm
            formState={formState}
            errors={errors}
            onChange={onChange}
            grillTypes={grillTypes}
            coffeeMakerTypes={coffeeMakerTypes}
            toasterTypes={toasterTypes}
          />
        </div>
        <div className="w-full h-min">
          <RecreationForm
            formState={formState}
            errors={errors}
            onChange={onChange}
            poolTypes={poolTypes}
          />
          <EntertainmentForm
            formState={formState}
            errors={errors}
            onChange={onChange}
            tvServices={tvServices}
          />
          <OutdoorsForm
            formState={formState}
            errors={errors}
            onChange={onChange}
          />
        </div>
      </div>
    </div>
  );
};

export default AmenitiesFormWrapper;
