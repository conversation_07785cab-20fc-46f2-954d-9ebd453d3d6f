"use client";

import { useCallback } from "react";
import { FormValues } from "./AmenitiesFormWrapper";
import Select from "@/clients/ui/select";
import AmenityItem from "./AmenityItem";

const SELECT_OPTIONS = Array(101)
  .fill(1)
  .map((_, index) => ({ id: index, name: index.toString() }));

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
};

const OutdoorsForm = ({ formState, onChange, errors }: Props) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold p-2 mt-6 bg-gray-20">Outdoors</p>
      <AmenityItem
        isChecked={formState.deck}
        onCheck={onToggleCheckobox}
        name="deck"
        title="Deck"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.lawn_or_garden}
        onCheck={onToggleCheckobox}
        name="lawn_or_garden"
        title="Lawn / Garden"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.parking}
        onCheck={onToggleCheckobox}
        name="parking"
        title="Parking Off Street"
      >
        <Select
          className="text-xs font-bold w-full px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          name="parking_quantity"
          placeholder=""
          options={SELECT_OPTIONS}
          value={formState?.parking_quantity ?? 0}
          onChange={onChangeSelect}
        />
      </AmenityItem>
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.patio}
        onCheck={onToggleCheckobox}
        name="patio"
        title="Patio"
      />
      <hr className="my-1" />
      <AmenityItem
        isChecked={formState.porch}
        onCheck={onToggleCheckobox}
        name="porch"
        title="Porch"
      />
    </>
  );
};

export default OutdoorsForm;
