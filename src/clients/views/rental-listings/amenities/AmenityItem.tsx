"use client";

import Checkbox from "@/clients/ui/checkbox";
import classNames from "classnames";
import { memo } from "react";
import { twMerge } from "tailwind-merge";

type Props = {
  title: string;
  isChecked?: boolean;
  name: string;
  onCheck: any;
  children?: React.ReactNode;
  leftClassName?: string;
  rightClassName?: string;
};

const AmenityItem = ({
  title,
  name,
  isChecked,
  onCheck,
  children,
  leftClassName = "",
  rightClassName = "",
}: Props) => {
  return (
    <div
      className={classNames("py-2 flex items-center gap-2 w-full", {
        "min-h-[50px]": !!children,
      })}
    >
      <div
        className={twMerge("w-[50%] flex items-center gap-2", leftClassName)}
      >
        <Checkbox name={name} checked={isChecked} onChange={onCheck} />
        <p className="flex-grow text-xs">{title}</p>
      </div>

      <div className={twMerge("w-[50%] flex", rightClassName)}>
        {isChecked && children}
      </div>
    </div>
  );
};

export default memo(AmenityItem);
