"use client";

import ImageSlider from "../ImageSlider";
import { useCallback, useState } from "react";

type Props = {
  children: React.ReactNode;
  images: string[];
};

const OverviewPhotosWrapper = ({ children, images }: Props) => {
  const [selected, setSelected] = useState<number>(0);

  const onClickedImage = useCallback((e: any) => {
    const imageIndex = e.target.getAttribute("id");
    setSelected(Number(imageIndex));
  }, []);

  return (
    <>
      <div className="w-full md:w-[50%]">
        <ImageSlider images={images} selectedIndex={selected} />
      </div>
      <div className="w-full md:w-[50%]" onClick={onClickedImage}>
        {children}
      </div>
    </>
  );
};

export default OverviewPhotosWrapper;
