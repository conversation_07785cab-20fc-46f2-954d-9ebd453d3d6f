"use client";

import {
  deactivateListing,
  hideListing,
  publishListing,
  updatePropertyDetails,
} from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import MultiSelect from "@/clients/ui/multi-select";
import Select from "@/clients/ui/select";
import useForm from "@/hooks/useForm";
import { Nullable, ProgressStatus } from "@/types/common";
import { ItemType, Property } from "@/types/property";
import fetcher from "@/utils/swr/fetcher";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";

type Props = {
  propertyId: number;
  property: Property;
};

export type FormValues = {
  publish_type: string;
  priorities: Nullable<number>;
  other_rental_firms: number[];
  virtual_tour_link: Nullable<string>;
  deactivated_reason: Nullable<string>;
};

export enum PUBLISH_TYPE {
  PUBLISH = "publish",
  DEACTIVATE = "deactivate",
  HIDE = "hide",
}

const PublishAndDistributionForm = ({ propertyId, property }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const { data: propertyPriorities, isLoading } = useSWR(
    "enum-property-priority",
    () => fetcher<ItemType[]>("enum-property-priority")
  );
  const { data: otherRentals, isLoading: isloadingOtherRentals } = useSWR(
    "enum-company",
    () => fetcher<ItemType[]>("enum-company")
  );

  const { formState, pristine, errors, onChange, preSubmitCheck } =
    useForm<FormValues>(
      {
        publish_type: property.publish
          ? PUBLISH_TYPE.PUBLISH
          : property?.deleted_at
          ? PUBLISH_TYPE.DEACTIVATE
          : PUBLISH_TYPE.HIDE,
        priorities: property.priority?.id,
        other_rental_firms: property?.other_rental_firms ?? [],
        virtual_tour_link: property.virtual_tour_link ?? "",
        deactivated_reason: "",
      },
      {
        deactivated_reason: (_v, _n, _value) => {
          if (
            _v.publish_type === PUBLISH_TYPE.DEACTIVATE &&
            _value.trim().length === 0
          ) {
            return `Deactivate reason name is required.`;
          }
        },
      }
    );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  const onChangeRentalFirms = useCallback(
    (type: { id: any; name: string }) => {
      const types = formState?.other_rental_firms.includes(type.id)
        ? formState?.other_rental_firms?.filter((_t) => _t !== type.id)
        : [...formState.other_rental_firms, type.id];
      onChange(types, "other_rental_firms");
    },
    [formState.other_rental_firms, onChange]
  );

  const onRemoveRentalFirm = useCallback(
    (id: any) => {
      const types = formState?.other_rental_firms?.filter((_t) => _t !== id);
      onChange(types, "other_rental_firms");
    },
    [formState?.other_rental_firms, onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    let payload = {
      other_rental_firms: formState?.other_rental_firms ?? [],
      virtual_tour_link: formState?.virtual_tour_link,
      priority: formState.priorities ?? null,
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${propertyId}`);
        toast.success("Property details successfully updated!");
        if (formState.publish_type === PUBLISH_TYPE.PUBLISH) {
          publishListing(propertyId)
            .then((data) => {
              revalidateTagByName(`property-details-${propertyId}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success("Property Published successfully updated!");
            })
            .catch((err) => {
              console.log("error is", err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else if (formState.publish_type === PUBLISH_TYPE.HIDE) {
          hideListing(propertyId)
            .then((data) => {
              revalidateTagByName(`property-details-${propertyId}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success("Property hidden from Public successfully!");
            })
            .catch((err) => {
              console.log("error is", err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else if (formState.publish_type === PUBLISH_TYPE.DEACTIVATE) {
          deactivateListing(propertyId, {
            deactivated_reason: formState.deactivated_reason ?? "",
          })
            .then((data) => {
              revalidateTagByName(`property-details-${propertyId}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success("Property deactivated successfully!");
            })
            .catch((err) => {
              console.log("error is", err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
        }
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id, propertyId]);

  return (
    <div className="w-full md:w-1/2 p-2 md:p-4 border rounded-[10px] h-min">
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Publishing & Distribution</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save/Update
        </Button>
      </div>
      <div className="my-2 py-2">
        <Select
          name="publish_type"
          value={formState.publish_type}
          className="text-xs font-bold px-4 py-2"
          onChange={onChangeSelect}
          options={[
            {
              name: "Publish",
              id: "publish",
            },
            {
              name: "Hide from Public",
              id: "hide",
            },
            {
              name: "Deactivate",
              id: "deactivate",
            },
          ]}
        />
      </div>
      {formState.publish_type === PUBLISH_TYPE.DEACTIVATE && (
        <div className="mb-2 flex items-center gap-2 w-full">
          <p className="w-[30%] text-xs">Text reason for deactivation</p>
          <div className="w-[70%]">
            <Input
              className="text-xs p-2 w-full"
              name="deactivated_reason"
              value={formState?.deactivated_reason ?? ""}
              onChange={onChangeTextInput}
              placeholder="Deactivated reason"
              helperText={errors.deactivated_reason}
              error={!!errors.deactivated_reason}
            />
          </div>
        </div>
      )}
      <div className="mb-2 flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Listing Priority</p>
        <Select
          name="priorities"
          className="text-xs font-bold w-[70%] px-2 py-2"
          onChange={onChangeSelect}
          value={formState.priorities}
          options={propertyPriorities}
        />
      </div>
      <div className="mb-2 flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Other Rental Firms</p>
        <MultiSelect
          className="text-xs font-bold w-[70%] px-2 py-2"
          value={property.other_rental_firms}
          options={otherRentals}
          isClearable
          onChange={onChangeRentalFirms}
          onRemove={onRemoveRentalFirm}
        />
      </div>
      <div className="mb-2 flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Virtual Tour Link</p>
        <div className="w-[70%]">
          <Input
            className="text-xs p-2 w-full"
            name="virtual_tour_link"
            value={formState?.virtual_tour_link ?? ""}
            onChange={onChangeTextInput}
          />
        </div>
      </div>
    </div>
  );
};

export default PublishAndDistributionForm;
