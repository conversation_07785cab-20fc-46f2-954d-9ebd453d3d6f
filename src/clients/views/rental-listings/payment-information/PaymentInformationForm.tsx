"use client";

import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import useForm from "@/hooks/useForm";
import { Nullable, ProgressStatus } from "@/types/common";
import { Property } from "@/types/property";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  property: Property;
};

type FormValues = {
  first_name: Nullable<string>;
  last_name: Nullable<string>;
  street1: Nullable<string>;
  street2: Nullable<string>;
  city: Nullable<string>;
  state: Nullable<string>;
  country: Nullable<string>;
  zip_code: Nullable<string>;
};

const PaymentInformationForm = ({ property }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>({
    first_name: property.payment_information?.first_name ?? "",
    last_name: property.payment_information?.last_name ?? "",
    street1: property.payment_information?.street1 ?? "",
    street2: property.payment_information?.street2 ?? "",
    city: property.payment_information?.city ?? "",
    state: property.payment_information?.state ?? "",
    country: property.payment_information?.country ?? "",
    zip_code: property.payment_information?.zip_code ?? "",
  });

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      payment_information: {
        ...property.payment_information,
        ...formState,
      },
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    formState,
    preSubmitCheck,
    property.listing_id,
    property.payment_information,
  ]);
  return (
    <>
      <div className="mb-2 md:mb-4 w-full flex items-center justify-between">
        <p className="text-sm font-bold">Payment Information</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">First Name</p>
        <div className="w-[70%]">
          <Input
            name="first_name"
            className="text-xs p-2 w-full"
            placeholder="First Name"
            rows={3}
            value={formState?.first_name ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.first_name ?? ""}
            error={!!errors?.first_name?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">Last Name</p>
        <div className="w-[70%]">
          <Input
            name="last_name"
            className="text-xs p-2 w-full"
            placeholder="Last Name"
            rows={3}
            value={formState?.last_name ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.last_name ?? ""}
            error={!!errors?.last_name?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">Street Address 1</p>
        <div className="w-[70%]">
          <Input
            name="street1"
            className="text-xs p-2 w-full"
            placeholder="Street Address 1"
            rows={3}
            value={formState?.street1 ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.street1 ?? ""}
            error={!!errors?.street1?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">Street Address 2</p>
        <div className="w-[70%]">
          <Input
            name="street2"
            className="text-xs p-2 w-full"
            placeholder="Street Address 2"
            rows={3}
            value={formState?.street2 ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.street2 ?? ""}
            error={!!errors?.street2?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">City</p>
        <div className="w-[70%]">
          <Input
            name="city"
            className="text-xs p-2 w-full"
            placeholder="City"
            rows={3}
            value={formState?.city ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.city ?? ""}
            error={!!errors?.city?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">State</p>
        <div className="w-[70%]">
          <Input
            name="state"
            className="text-xs p-2 w-full"
            placeholder="State"
            rows={3}
            value={formState?.state ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.state ?? ""}
            error={!!errors?.state?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">Country</p>
        <div className="w-[70%]">
          <Input
            name="country"
            className="text-xs p-2 w-full"
            placeholder="Country"
            rows={3}
            value={formState?.country ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.country ?? ""}
            error={!!errors?.country?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs pt-2">Zip code</p>
        <div className="w-[70%]">
          <Input
            name="zip_code"
            className="text-xs p-2 w-full"
            placeholder="Zip code"
            rows={3}
            value={formState?.zip_code ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.zip_code ?? ""}
            error={!!errors?.zip_code?.length}
          />
        </div>
      </div>
    </>
  );
};

export default PaymentInformationForm;
