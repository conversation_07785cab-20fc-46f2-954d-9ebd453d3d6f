'use client';
import Input from '@/clients/ui/input';
import { Nullable } from '@/types/common';
import {
  ServiceProviderCompany,
  ServiceProviderType,
} from '@/types/service-providers';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import debounce from 'lodash/debounce';

type Props = {
  placeholder?: string;
  className?: string;
  bodyClassName?: string;
  options?: ServiceProviderCompany[];
  value?: Nullable<string | number>;
  onSelect?: (id: number) => void;
  companyName?: string;
  type?: ServiceProviderType;
  fetchData?: (query?: string) => void;
  isFetchingData?: boolean;
};

const ServiceProviderSelect = ({
  className = '',
  bodyClassName = '',
  placeholder = '',
  value,
  onSelect,
  companyName,
  type,
  fetchData,
  options,
  isFetchingData,
}: Props) => {
  const [query, setQuery] = useState('');
  const [isUpwardDir, setIsUpwardDir] = useState<boolean>(false);
  const elementRef = useRef<null | HTMLDivElement>(null);
  const containerRef = useRef<null | HTMLDivElement>(null);
  const selectedRef = useRef<null | HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const selected = useMemo(
    () => options?.find((_o) => _o.id === value),
    [options, value]
  );

  const fetchDataDebounced = debounce((val = '') => {
    fetchData?.(val);
  }, 500);

  const onChangeQuery = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      setQuery(value);
      fetchDataDebounced(value);
    },
    [fetchDataDebounced]
  );

  const onClose = useCallback(() => {
    setIsOpen(false);
    setQuery('');
  }, []);

  useEffect(() => {
    if (elementRef && isOpen) {
      setIsUpwardDir(
        (elementRef.current?.getBoundingClientRect().top ?? 0) >
          (2 * window.innerHeight) / 3
      );
    }
  }, [value, isOpen]);

  useEffect(() => {
    if (selectedRef && containerRef && isOpen) {
      containerRef?.current?.scrollTo(0, selectedRef.current?.offsetTop ?? 0);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      fetchData?.('');
    }
  }, [fetchData, isOpen]);

  return (
    <>
      <div
        ref={elementRef}
        className={classNames(
          twMerge(
            'relative px-4 py-2 border rounded-md flex items-center justify-between cursor-pointer',
            className
          )
        )}
        onClick={() => setIsOpen(true)}
      >
        {isOpen ? (
          <Input
            placeholder="Start typing provider name..."
            className="border-0 text-xs text-center p-0"
            wrapperclassName="z-[111] w-[90%]"
            value={query}
            onChange={onChangeQuery}
          />
        ) : selected || companyName ? (
          <p className="w-[90%]">
            {selected
              ? !selected.company_name || selected.company_name === ''
                ? selected.contact_name
                : selected.company_name
              : companyName}
          </p>
        ) : (
          <p className="w-[90%]">Click to select</p>
        )}
        {isOpen ? (
          <ChevronUpIcon className="w-auto h-[14px]" />
        ) : (
          <ChevronDownIcon className="w-auto h-[14px]" />
        )}
        {isOpen && (
          <>
            <div
              ref={containerRef}
              className={twMerge(
                classNames(
                  'absolute left-0 right-0 bg-white shadow-card py-2 rounded-md z-[999]',
                  {
                    'top-[34px]': !isUpwardDir,
                    'bottom-[34px]': isUpwardDir,
                  }
                ),
                bodyClassName
              )}
            >
              {isFetchingData && (
                <span className="loading loading-spinner loading-md"></span>
              )}
              {options?.map((_option, index) => (
                <div
                  ref={selected?.id === _option.id ? selectedRef : null}
                  key={index}
                  onClick={(e) => {
                    onSelect?.(_option.id);
                    onClose();
                    e.stopPropagation();
                  }}
                  className={classNames(
                    'hover:bg-gray-100 py-2 pl-4 font-normal',
                    {
                      'text-carolina-blue font-medium':
                        selected?.id === _option.id,
                    }
                  )}
                >
                  {!_option.company_name || _option.company_name === ''
                    ? _option.contact_name
                    : _option.company_name}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
      {isOpen && (
        <div
          className="drawer-overlay fixed inset-0 z-[99]"
          onClick={onClose}
        />
      )}
    </>
  );
};

export default ServiceProviderSelect;
