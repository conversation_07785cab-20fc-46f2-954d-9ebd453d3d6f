"use client";

import { Comment } from "@/types/service-providers";
import { TrashIcon } from "@heroicons/react/24/outline";
import dayjs from "dayjs";
import { memo } from "react";

type Props = {
  comments: Comment[];
  onDelete?: (id: string) => void;
  userId?: number;
};

const ServiceProviderNotesTable = ({ comments, onDelete, userId }: Props) => {
  return (
    <div className="overflow-x-auto">
      <table className="table text-xs text-center">
        <thead className="bg-white border-t">
          <tr>
            <th className="text-black-60 font-normal w-[10%] text-left">
              Date
            </th>
            <th className="text-black-60 font-normal w-[20%] text-left">
              Posted by
            </th>
            <th className="text-black-60 font-normal w-[70%] text-left">
              Note
            </th>
          </tr>
        </thead>
        <tbody>
          {comments.map((_comment, index) => (
            <tr key={index}>
              <td className="text-left align-top">
                {dayjs(_comment.date).format("MM/DD/YYYY")}
              </td>
              <td className="text-left align-top">{_comment.posted_by}</td>
              <td className="text-left align-top flex gap-2">
                <p className="flex-grow">{_comment.content}</p>
                {userId === _comment.user && (
                  <span
                    className="w-[20%] align-middle cursor-pointer"
                    onClick={() => onDelete?.(_comment.comment_uuid)}
                  >
                    <TrashIcon className="w-5 h-5 text-error" />
                  </span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default memo(ServiceProviderNotesTable);
