"use client";

import But<PERSON> from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import { useCallback, useState } from "react";
import { ProgressStatus } from "@/types/common";
import useForm from "@/hooks/useForm";
import {
  ServiceProviderCompany,
  ServiceProviderType,
} from "@/types/service-providers";
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from "@/constants/pattern";
import {
  addServiceProviderComapany,
  updateServiceProviderComapany,
} from "@/app/actions/service-providers";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";
import Select from "@/clients/ui/select";
import FormHelperText from "@/app/ui/form-helper-text";
import classNames from "classnames";

export const ServiceProviderTypeOptions = [
  {
    id: ServiceProviderType.ALARM_COMPANY,
    name: ServiceProviderType.ALARM_COMPANY,
  },
  {
    id: ServiceProviderType.CARETAKER,
    name: ServiceProviderType.CARETAKER,
  },
  {
    id: ServiceProviderType.CLEANER,
    name: ServiceProviderType.CLEANER,
  },
  {
    id: ServiceProviderType.ELECTRICIAN,
    name: ServiceProviderType.ELECTRICIAN,
  },
  {
    id: ServiceProviderType.LANDSCAPER,
    name: ServiceProviderType.LANDSCAPER,
  },
  {
    id: ServiceProviderType.TRASH_SERVICE,
    name: ServiceProviderType.TRASH_SERVICE,
  },
  {
    id: ServiceProviderType.PLUMBER,
    name: ServiceProviderType.PLUMBER,
  },
  {
    id: ServiceProviderType.OTHER,
    name: ServiceProviderType.OTHER,
  },
];

type Props = {
  type?: ServiceProviderType;
  selectedCompany?: ServiceProviderCompany;
  onClose?: () => void;
};

type FormValues = {
  company_name?: string;
  contact_name: string;
  email: string;
  work_phone: string;
  cell_phone: string;
  alarm_code: string;
  type: string;
};

const ServiceProviderCompanyForm = ({
  type,
  selectedCompany,
  onClose,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
    onSetFormDefaultState,
    onResetForm,
  } = useForm<FormValues>(
    {
      company_name: selectedCompany?.company_name ?? "",
      contact_name: selectedCompany?.contact_name ?? "",
      email: selectedCompany?.email ?? "",
      work_phone: selectedCompany?.work_phone ?? "",
      cell_phone: selectedCompany?.cell_phone ?? "",
      alarm_code: selectedCompany?.alarm_code ?? "",
      type: type ?? selectedCompany?.type ?? "",
    },
    {
      contact_name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Contact name is required.`;
        }
      },
      type: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Service Provider type is required.`;
        }
      },
      work_phone: (_v, _n, _value: string) => {
        if (!!_value && !_value.match(PHONE_NUMBER_PATTERN)) {
          return "Invalid phone number";
        }
      },
      cell_phone: (_v, _n, _value: string) => {
        if (!!_value && !_value.match(PHONE_NUMBER_PATTERN)) {
          return "Invalid phone number";
        }
      },
      email: (_v, _n, _value) => {
        if (_value.trim().length > 0 && !_value.match(EMAIL_PATTERN)) {
          return "Enter valid email";
        }
      },
    }
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);

    if (selectedCompany?.id) {
      updateServiceProviderComapany(selectedCompany.id, {
        ...formState,
        company_name: formState?.company_name ?? "",
        type: formState.type as ServiceProviderType,
      })
        .then((data) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          revalidateTagByName(`property-service-provider-companies`);
          toast.success("Successfully updated!");
          onClose?.();
        })
        .catch((error) => {
          console.log("errror is", error);
          setProgressStatus(ProgressStatus.FAILED);
        });
    } else {
      addServiceProviderComapany({
        ...formState,
        company_name: formState?.company_name ?? "",
        type: formState.type as ServiceProviderType,
      })
        .then((data) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          revalidateTagByName(`property-service-provider-companies`);
          toast.success("Successfully added!");
          onResetForm();
        })
        .catch((error) => {
          console.log("errror is", error);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [formState, onClose, onResetForm, preSubmitCheck, selectedCompany]);

  return (
    <>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Company Name</p>
        <Input
          name="company_name"
          className="text-xs p-2 w-full text-center"
          placeholder="Company Name"
          wrapperclassName="w-[50%]"
          value={formState.company_name}
          onChange={onChangeTextInput}
          helperText={errors?.company_name ?? ""}
          error={!!errors?.company_name?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Contact Name</p>
        <Input
          name="contact_name"
          className="text-xs p-2 w-full text-center"
          placeholder="Contact Name"
          wrapperclassName="w-[50%]"
          value={formState.contact_name}
          onChange={onChangeTextInput}
          helperText={errors?.contact_name ?? ""}
          error={!!errors?.contact_name?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Email</p>
        <Input
          name="email"
          className="text-xs p-2 w-full text-center"
          placeholder="Email"
          wrapperclassName="w-[50%]"
          value={formState.email}
          onChange={onChangeTextInput}
          helperText={errors?.email ?? ""}
          error={!!errors?.email?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Work Phone</p>
        <Input
          name="work_phone"
          className="text-xs p-2 w-full text-center"
          placeholder="Work Phone"
          wrapperclassName="w-[50%]"
          value={formState.work_phone}
          onChange={onChangeTextInput}
          helperText={errors?.work_phone ?? ""}
          error={!!errors?.work_phone?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Cell Phone</p>
        <Input
          name="cell_phone"
          className="text-xs p-2 w-full text-center"
          placeholder="Cell Phone"
          wrapperclassName="w-[50%]"
          value={formState.cell_phone}
          onChange={onChangeTextInput}
          helperText={errors?.cell_phone ?? ""}
          error={!!errors?.cell_phone?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Service Provider type</p>
        <div className="relative w-[50%]">
          <Select
            name="type"
            placeholder="Select a type"
            className={classNames("text-xs p-2 text-center capitalize w-full", {
              "border-error": !!errors?.type?.length,
            })}
            value={formState.type}
            onChange={onChangeSelect}
            options={ServiceProviderTypeOptions}
          />
          {!!errors?.type?.length && (
            <div className="ml-2 absolute">
              <FormHelperText error>{errors?.type ?? ""}</FormHelperText>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-2 my-2 justify-end">
        <Button intent="outline" className="text-xs font-normal">
          Clear
        </Button>
        <Button
          className="text-xs"
          onClick={onSave}
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          {selectedCompany?.id ? `Update` : `Save`}
        </Button>
      </div>
    </>
  );
};

export default ServiceProviderCompanyForm;
