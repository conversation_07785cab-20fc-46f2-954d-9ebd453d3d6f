"use client";

import <PERSON><PERSON> from "@/clients/ui/button";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Nullable, ProgressStatus } from "@/types/common";
import {
  PropertyServiceProvider,
  ServiceProviderCompany,
  ServiceProviderPayload,
  ServiceProviderType,
} from "@/types/service-providers";
import AddNoteModal from "./AddNoteModal";
import DeleteConfirm from "@/clients/components/common/DeleteConfirm";
import {
  addServiceProviderData,
  deleteServiceProviderNote,
  updateServiceProviderData,
} from "@/app/actions/service-providers";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";
import ServiceProviderNotesTable from "./ServiceProviderNotesTable";
import MultiSelect from "@/clients/ui/multi-select";
import Input from "@/clients/ui/input";

const PICKUP_DAYS_OPTIONS = [
  {
    id: "sunday",
    name: "Sunday",
  },
  {
    id: "monday",
    name: "Monday",
  },
  {
    id: "tuesday",
    name: "Tuesday",
  },
  {
    id: "wednesday",
    name: "Wednesday",
  },
  {
    id: "thursday",
    name: "Thursday",
  },
  {
    id: "friday",
    name: "Friday",
  },
  {
    id: "saturday",
    name: "Saturday",
  },
];

type Props = {
  serviceProviderId?: string;
  data?: PropertyServiceProvider;
  userId: number;
  listingId: number;
  type: ServiceProviderType;
  selectedCompany?: ServiceProviderCompany;
  onClose?: () => void;
};

const ServiceProviderItemPreviewForm = ({
  serviceProviderId,
  data,
  userId,
  listingId,
  type,
  selectedCompany,
  onClose,
}: Props) => {
  const [deleteSelected, setDeleteSelected] = useState<Nullable<string>>(null);
  const [showNoteModal, setShowNoteModal] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [pickupDays, setPickupDays] = useState<string[]>([]);
  const [alarmCode, setAlarmCode] = useState<string>("");
  const showButtons = useMemo(
    () =>
      !!selectedCompany ||
      pickupDays !== (data?.pickup_days ?? []) ||
      alarmCode.trim() !== (data?.alarm_code?.trim() ?? ""),
    [
      alarmCode,
      data?.alarm_code,
      data?.pickup_days,
      pickupDays,
      selectedCompany,
    ]
  );

  const contactName = useMemo(
    () =>
      selectedCompany ? selectedCompany?.contact_name : data?.contact_name,
    [data?.contact_name, selectedCompany]
  );

  const onDeleteNoteConfirm = useCallback(() => {
    if (!deleteSelected || !serviceProviderId) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    deleteServiceProviderNote(serviceProviderId, deleteSelected)
      .then((_data) => {
        revalidateTagByName(`property-service-providers-${listingId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Note deleted successfully!");
        setDeleteSelected(null);
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [deleteSelected, listingId, serviceProviderId]);

  const onClickDelete = useCallback((id: string) => {
    setDeleteSelected(id);
  }, []);

  const onCloseDelete = useCallback(() => {
    setDeleteSelected(null);
  }, []);

  const onChangeTextInput = useCallback((event: any) => {
    const { value } = event.target;

    setAlarmCode(value);
  }, []);

  const onChangePickupDays = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      const newPickupDays = pickupDays.includes(value.id.toString())
        ? pickupDays.filter((_d) => _d !== value.id)
        : [...pickupDays, value.id];
      setPickupDays(newPickupDays as any);
    },
    [pickupDays]
  );

  const onSave = useCallback(() => {
    if (!selectedCompany && !serviceProviderId) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);

    let payload: ServiceProviderPayload = selectedCompany
      ? {
          contact_name: selectedCompany?.contact_name ?? null,
          email: selectedCompany?.email ?? null,
          cell_phone: selectedCompany?.cell_phone ?? null,
          work_phone: selectedCompany?.work_phone ?? null,
          company: selectedCompany?.id ?? null,
          listing: listingId,
          type,
        }
      : {
          contact_name: data?.contact_name ?? null,
          email: data?.email ?? null,
          cell_phone: data?.cell_phone ?? null,
          work_phone: data?.work_phone ?? null,
          company: data?.company ?? null,
          listing: listingId,
          type,
        };

    if (type === ServiceProviderType.TRASH_SERVICE && pickupDays.length > 0) {
      payload.pickup_days = pickupDays;
    }

    if (
      type === ServiceProviderType.ALARM_COMPANY &&
      alarmCode.trim().length > 0
    ) {
      payload.alarm_code = alarmCode;
    }

    if (selectedCompany?.id !== data?.company && serviceProviderId) {
      updateServiceProviderData(serviceProviderId, payload)
        .then((data) => {
          revalidateTagByName(`property-service-providers-${listingId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success("Service provider data updated successfully!");
        })
        .catch((err) => {
          console.log("error is", err);
          toast.error(err.message);
          setProgressStatus(ProgressStatus.FAILED);
        });
    } else {
      addServiceProviderData(payload)
        .then((data) => {
          revalidateTagByName(`property-service-providers-${listingId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success("Service provider data added successfully!");
        })
        .catch((err) => {
          console.log("error is", err);
          toast.error(err.message);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [
    alarmCode,
    data?.cell_phone,
    data?.company,
    data?.contact_name,
    data?.email,
    data?.work_phone,
    listingId,
    pickupDays,
    selectedCompany,
    serviceProviderId,
    type,
  ]);

  useEffect(() => {
    if (data) {
      setAlarmCode(data?.alarm_code ?? "");
      setPickupDays(data?.pickup_days ?? []);
    }
  }, [data]);

  useEffect(() => {
    if (selectedCompany) {
      setAlarmCode("");
      setPickupDays([]);
    }
  }, [selectedCompany]);

  return (
    <>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Contact Name</p>
        <p className="text-xs p-2 w-[50%]">{contactName}</p>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Email</p>
        <p className="text-xs p-2 w-[50%]">{data?.email ?? "N/A"}</p>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Work Phone</p>
        <p className="text-xs p-2 w-[50%]">{data?.work_phone ?? "N/A"}</p>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Cell Phone</p>
        <p className="text-xs p-2 w-[50%]">{data?.cell_phone ?? "N/A"}</p>
      </div>

      {type === ServiceProviderType.ALARM_COMPANY && (
        <div className="py-2 flex items-center gap-2 w-full">
          <p className="w-[50%] text-xs">Alarm code</p>
          <Input
            name="alarm_code"
            className="text-xs p-2 w-full text-center"
            placeholder="Alarm code"
            wrapperclassName="w-[50%]"
            value={alarmCode ?? ""}
            onChange={onChangeTextInput}
          />
        </div>
      )}

      {type === ServiceProviderType.TRASH_SERVICE && (
        <div className="py-2 flex items-center gap-2 w-full">
          <p className="w-[50%] text-xs">Pickup Days</p>
          <MultiSelect
            className="text-xs p-2 text-center capitalize w-[50%]"
            placeholder="Pickup days"
            value={pickupDays}
            onChange={onChangePickupDays}
            options={PICKUP_DAYS_OPTIONS}
          />
        </div>
      )}

      {serviceProviderId && (
        <>
          <div className="py-2 flex items-center justify-between gap-2 w-full">
            <p className="text-sm font-bold">Notes</p>
            <Button
              intent="outline"
              className="border-black text-xs"
              onClick={() => setShowNoteModal(true)}
            >
              + Add a Note
            </Button>
          </div>
          <ServiceProviderNotesTable
            comments={data?.comments ?? []}
            userId={userId}
            onDelete={onClickDelete}
          />
        </>
      )}

      {showButtons && (
        <div className="flex gap-2 my-2 justify-end">
          <Button
            intent="outline"
            className="text-xs font-normal"
            onClick={onClose}
          >
            Close
          </Button>
          <Button
            className="text-xs"
            onClick={onSave}
            disabled={progressStatus === ProgressStatus.LOADING}
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            {serviceProviderId ? `Update` : `Save`}
          </Button>
        </div>
      )}
      {showNoteModal && (
        <AddNoteModal
          serviceProviderId={data?.sp_uuid ?? ""}
          onClose={() => setShowNoteModal(false)}
        />
      )}

      {!!deleteSelected && (
        <DeleteConfirm
          progressStatus={progressStatus}
          onDelete={onDeleteNoteConfirm}
          onClose={onCloseDelete}
        />
      )}
    </>
  );
};

export default ServiceProviderItemPreviewForm;
