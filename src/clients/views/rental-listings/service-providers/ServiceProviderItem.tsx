"use client";

import {
  PropertyServiceProvider,
  ServiceProviderCompany,
  ServiceProviderType,
} from "@/types/service-providers";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Nullable } from "@/types/common";
import ServiceProviderSelect from "./ServicePorviderSelect";
import { getServiceProviderCompanies } from "@/app/actions/service-providers";
import ServiceProviderItemPreviewForm from "./ServiceProviderItemPreviewForm";

type Props = {
  data?: PropertyServiceProvider;
  title: string;
  userId: number;
  listingId: number;
  type: ServiceProviderType;
  isEditCompany?: boolean;
};

const ServiceProviderItem = ({
  title,
  data,
  userId,
  listingId,
  type,
}: Props) => {
  const [selectedId, setSelectedId] = useState<Nullable<number>>(null);
  const [isFetchingData, setIsFetchingData] = useState<boolean>(false);
  const [options, setOptions] = useState<ServiceProviderCompany[]>([]);

  const selected = useMemo(
    () => options?.find((_o) => _o.id === selectedId),
    [options, selectedId]
  );

  const onClose = useCallback(() => {
    setSelectedId(null);
  }, []);

  const fetchData = useCallback(
    async (query?: string) => {
      try {
        setIsFetchingData(true);
        const { results } = await getServiceProviderCompanies<{
          count: number;
          next: string;
          results: ServiceProviderCompany[];
        }>(query, type);
        setOptions(results);
        setIsFetchingData(false);
      } catch (error) {
        console.log("Failed to fetch data", error);
        setIsFetchingData(false);
      }
    },
    [type]
  );

  const onSelect = useCallback((id: number) => {
    setSelectedId(id);
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="border p-4 rounded-lg">
      <div className="flex items-center gap-2">
        <p className="w-[50%] text-sm font-bold">{title}</p>
        <div className="w-[50%]">
          <ServiceProviderSelect
            className="text-xs px-4 py-2 text-center w-full"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            options={options}
            value={selected ? selected.id : data?.company}
            companyName={data?.company_name ?? ""}
            onSelect={onSelect}
            fetchData={fetchData}
            type={type}
            isFetchingData={isFetchingData}
          />
        </div>
      </div>
      {(selected || data?.sp_uuid) && (
        <ServiceProviderItemPreviewForm
          serviceProviderId={data?.sp_uuid}
          userId={userId}
          listingId={listingId}
          type={type}
          selectedCompany={selected}
          data={data}
          onClose={onClose}
        />
      )}
    </div>
  );
};

export default ServiceProviderItem;
