"use client";

import { updatePropertyDetails, uploadImage } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import { ProgressStatus } from "@/types/common";
import { ImageUploaded, PropertyImage } from "@/types/property";
import dynamic from "next/dynamic";
import { DragEvent, useCallback, useState } from "react";
import toast from "react-hot-toast";
import PhotosUploadingDialog from "./PhotosUploadingDialog";

type Props = {
  children: React.ReactNode;
  propertyId: number;
  images: PropertyImage[];
};

const PhotosDropWrapper = ({ children, images, propertyId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const onDropped = useCallback(
    async (e: DragEvent<HTMLDivElement>) => {
      e.stopPropagation();
      e.preventDefault();

      const dt = e.dataTransfer;
      const files = dt.files;
      const imageUploadedArray = [];
      for (const _file of files) {
        if (_file && _file.type.startsWith("image/")) {
          setProgressStatus(ProgressStatus.LOADING);
          try {
            const formdata = new FormData();
            formdata.append("file", _file);
            const imageUploaded = await uploadImage<ImageUploaded>(formdata);
            if (imageUploaded) {
              imageUploadedArray.push(imageUploaded);
            }
          } catch (error) {
            console.log("errror is", error);
            toast.error("Failed to upload property image, please try again!");
          }
        }
      }

      if (imageUploadedArray.length > 0) {
        const payload = {
          images: [
            ...images.map((_image, index) => ({
              ..._image,
              order: index + 1,
            })),
            ...imageUploadedArray.map((_upload, index) => ({
              url: _upload.url,
              small_url: _upload.small_url,
              alt_text: "",
              order: images.length + index + 1,
              caption: "",
              object: _upload.object_uuid,
            })),
          ],
        };

        updatePropertyDetails(propertyId, payload)
          .then((data) => {
            revalidateTagByName(`property-details-${propertyId}`);
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            toast.success("Property image successfully uploaded!");
          })
          .catch((err) => {
            console.log("error is", err);
            toast.error(err.message);
            setProgressStatus(ProgressStatus.FAILED);
          });
      }
    },
    [images, propertyId]
  );

  return (
    <div onDrop={onDropped} onDragOver={(event) => event.preventDefault()}>
      {children}
      {progressStatus === ProgressStatus.LOADING && (
        <PhotosUploadingDialog open />
      )}
    </div>
  );
};

export default PhotosDropWrapper;
