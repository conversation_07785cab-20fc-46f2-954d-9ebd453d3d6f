"use client";

import { BarsArrowDownIcon } from "@heroicons/react/24/outline";
import { useCallback, useState } from "react";
import { PropertyImage } from "@/types/property";
import dynamic from "next/dynamic";
import But<PERSON> from "@/clients/ui/button";

const SortPhotosModal = dynamic(() => import("./SortPhotosModal"));

type Props = {
  photos: PropertyImage[];
  propertyId: number;
};
const SortPhotosButton = ({ photos, propertyId }: Props) => {
  const [show, setShow] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShow(!show);
  }, [show]);

  return (
    <>
      <Button
        onClick={onToggle}
        className="!text-xs min-w-[120px] h-8 flex items-center gap-x-1"
        intent="secondary"
      >
        Sort Photos
        <BarsArrowDownIcon className="w-5 h-5 cursor-pointer" />
      </Button>
      {show && (
        <SortPhotosModal
          photos={photos}
          onClose={onToggle}
          propertyId={propertyId}
        />
      )}
    </>
  );
};

export default SortPhotosButton;
