"use client";

import { memo, useCallback, useState, useRef, useEffect } from "react";

import Image from "next/image";
import { PropertyImage } from "@/types/property";
import Modal from "@/clients/ui/modal";

type Props = {
  open: boolean;
  onClose: () => void;
  images: PropertyImage[];
  currentIndex?: number;
};

const SWIPE_THRESHOLD = 75;
const transparentPlaceholder =
  "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==";

const PhotosCarousel = ({ open, images, onClose, currentIndex = 0 }: Props) => {
  const [current, setCurrent] = useState(currentIndex);
  const [dragOffset, setDragOffset] = useState(0);
  const startXRef = useRef<number | null>(null);
  const isDraggingRef = useRef(false);

  const onNext = useCallback(() => {
    setCurrent((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  }, [images.length]);

  const onPrev = useCallback(() => {
    setCurrent((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  }, [images.length]);

  const handlePointerDown = useCallback(
    (e: React.PointerEvent<HTMLDivElement>) => {
      startXRef.current = e.clientX;
      isDraggingRef.current = true;
    },
    []
  );

  const handlePointerMove = useCallback(
    (e: React.PointerEvent<HTMLDivElement>) => {
      if (!isDraggingRef.current || startXRef.current === null) return;
      const delta = e.clientX - startXRef.current;
      setDragOffset(delta);
    },
    []
  );

  const handlePointerUp = useCallback(
    (e: React.PointerEvent<HTMLDivElement>) => {
      if (!isDraggingRef.current || startXRef.current === null) return;

      const delta = e.clientX - startXRef.current;

      if (delta > SWIPE_THRESHOLD) {
        onPrev();
      } else if (delta < -SWIPE_THRESHOLD) {
        onNext();
      }

      setDragOffset(0);
      startXRef.current = null;
      isDraggingRef.current = false;
    },
    [onPrev, onNext]
  );

  const handlePointerLeave = useCallback(() => {
    // In case user drags out of bounds without releasing
    if (isDraggingRef.current) {
      setDragOffset(0);
      startXRef.current = null;
      isDraggingRef.current = false;
    }
  }, []);

  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") {
        onPrev();
      } else if (e.key === "ArrowRight") {
        onNext();
      } else if (e.key === "Escape") {
        onClose();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [open, onNext, onPrev, onClose]);

  return (
    <Modal
      open={open}
      onClose={onClose}
      showClose
      className="p-2 rounded-none max-w-max relative bg-white"
    >
      <div
        id="PhotosCarousel"
        className="carousel w-full md:w-[90vw] min-h-[30vh] md:h-[90vh] overflow-hidden select-none touch-pan-y"
        onPointerDown={handlePointerDown}
        onPointerMove={handlePointerMove}
        onPointerUp={handlePointerUp}
        onPointerLeave={handlePointerLeave}
      >
        {images.map((_image, index) => {
          const isActive = index == current;
          return (
            <div
              key={_image.image_uuid}
              className={`carousel-item-${index} w-full m-auto transition-all ease-in-out duration-300 ${
                isActive ? "block" : "hidden"
              }`}
              style={{
                transform: isActive ? `translateX(${dragOffset}px)` : "none",
              }}
            >
              <Image
                src={_image.url}
                alt={_image?.caption ?? "listing image"}
                width={0}
                height={0}
                sizes="100vw"
                className="w-full h-[90dvh] object-contain select-none pointer-events-none"
                draggable={false}
                loading="lazy"
                placeholder="blur"
                blurDataURL={transparentPlaceholder}
              />
            </div>
          );
        })}
      </div>

      <div className="absolute left-5 right-5 top-1/2 flex -translate-y-1/2 transform justify-between z-10">
        <button onClick={onPrev} className="btn btn-circle">
          ❮
        </button>
        <button onClick={onNext} className="btn btn-circle">
          ❯
        </button>
      </div>
    </Modal>
  );
};

export default memo(PhotosCarousel);
