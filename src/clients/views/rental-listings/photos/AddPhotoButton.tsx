"use client";

import { updatePropertyDetails, uploadImage } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import { ProgressStatus } from "@/types/common";
import { ImageUploaded, PropertyImage } from "@/types/property";
import { aRandomString } from "@/utils/common";
import { FormEvent, useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
  images: PropertyImage[];
};

const AddPhotoButton = ({ images, propertyId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onImageSelect = useCallback(
    async (e: FormEvent<HTMLInputElement>) => {
      const target = e.currentTarget;
      const file = (target.files as FileList)[0];
      try {
        setProgressStatus(ProgressStatus.LOADING);

        const formdata = new FormData();
        formdata.append("file", file);
        const imageUploaded = await uploadImage<ImageUploaded>(formdata);
        if (imageUploaded) {
          const payload = {
            images: [
              ...images.map((_image, index) => ({
                ..._image,
                order: index + 1,
              })),
              {
                url: imageUploaded.url,
                small_url: imageUploaded.small_url,
                alt_text: "",
                order: images.length + 1,
                caption: "",
                object: imageUploaded.object_uuid,
              },
            ],
          };

          updatePropertyDetails(propertyId, payload)
            .then((data) => {
              revalidateTagByName(`property-details-${propertyId}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success("Property image successfully uploaded!");
            })
            .catch((err) => {
              console.log("error is", err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        }
      } catch (error) {
        console.log("errror is", error);
        toast.error(
          "Failed to upload agreement property image, please try again!"
        );
        setProgressStatus(ProgressStatus.FAILED);
      }
    },
    [images, propertyId]
  );

  return (
    <div className="relative cursor-pointer">
      <input
        type="file"
        className="absolute inset-0 cursor-pointer opacity-0"
        onChange={onImageSelect}
        accept="image/png, image/gif, image/jpeg"
      />
      <Button
        className="!text-xs min-w-[120px]"
        disabled={progressStatus === ProgressStatus.LOADING}
        isLoading={progressStatus === ProgressStatus.LOADING}
      >
        Add Photo
      </Button>
    </div>
  );
};

export default AddPhotoButton;
