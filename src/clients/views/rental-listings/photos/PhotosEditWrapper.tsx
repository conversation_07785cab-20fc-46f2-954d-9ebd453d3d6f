"use client";

import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Card from "@/app/ui/card";
import DeleteConfirm from "@/clients/components/common/DeleteConfirm";
import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import useForm from "@/hooks/useForm";
import { Nullable, ProgressStatus } from "@/types/common";
import { PropertyImage } from "@/types/property";
import { TrashIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import PhotosCarousel from "./PhotosCarousel";

type Props = {
  propertyId: number;
  children: React.ReactNode;
  images: PropertyImage[];
  isAdmin?: boolean;
  virtual_tour_link: Nullable<string>;
};

type FormValues = {
  caption: Nullable<string>;
  alt_text: Nullable<string>;
};

const PhotosEditWrapper = ({
  children,
  images,
  isAdmin,
  propertyId,
  virtual_tour_link,
}: Props) => {
  const [confirmDeleteId, setConfirmDeleteId] =
    useState<Nullable<string>>(null);
  const [virtualTourLink, setVirtualTourLink] = useState<string>(
    virtual_tour_link ?? ""
  );
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [selected, setSelected] = useState<Nullable<number>>(0);
  const [showCarousel, setShowCarousel] = useState<boolean>(false);

  const selectedImage = useMemo(
    () => (selected !== null ? images?.[selected] : null),
    [images, selected]
  );

  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
    setFormState,
  } = useForm<FormValues>({
    caption: selectedImage?.caption ?? null,
    alt_text: selectedImage?.alt_text ?? null,
  });

  const onChangeVirtualTour = useCallback((event: any) => {
    const { name, value } = event.target;

    setVirtualTourLink(value);
  }, []);

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onClickedImage = useCallback((e: any) => {
    const imageIndex = e.target.getAttribute("id");
    setSelected(Number(imageIndex));
  }, []);

  const onDeleteClick = useCallback((uuid: string) => {
    setConfirmDeleteId(uuid);
  }, []);

  const onToggleAllPhotos = useCallback(() => {
    setShowCarousel((_o) => !_o);
  }, []);

  const onDeleteConfirm = useCallback(() => {
    if (!confirmDeleteId) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    updatePropertyDetails(propertyId, {
      images: images.filter((_i) => _i.image_uuid != confirmDeleteId),
    })
      .then((data) => {
        revalidateTagByName(`property-details-${propertyId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Image delete successfully!");
        setConfirmDeleteId(null);
        setSelected(0);
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [confirmDeleteId, images, propertyId]);

  const onSave = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);

    const payload = {
      images: images.map((_image, index) => {
        if (index.toString() === selected?.toString()) {
          return {
            ..._image,
            alt_text: formState.alt_text,
            caption: formState.caption,
          };
        }

        return _image;
      }),
    };

    updatePropertyDetails(propertyId, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${propertyId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Image details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, images, propertyId, selected]);

  useEffect(() => {
    if (selectedImage) {
      setFormState({
        alt_text: selectedImage.alt_text,
        caption: selectedImage.caption,
      });
    }
  }, [selectedImage, setFormState]);

  return (
    <>
      <hr />
      <div className="py-2 flex items-center gap-2 w-full lg:w-[60%] my-2">
        <p className="w-[20%] text-xs">Virtual Tour Link:</p>
        <div className="w-[60%]">
          <Input
            name="address"
            className="text-xs p-2 w-full"
            placeholder="Virtual Tour Link"
            value={virtualTourLink}
            onChange={onChangeVirtualTour}
          />
        </div>
        <Button className="!text-xs w-[20%]">Save</Button>
      </div>
      <hr />
      {selectedImage && (
        <div className="flex flex-col md:flex-row gap-4 my-4">
          <Image
            alt="property image"
            src={selectedImage.url ?? ""}
            width={0}
            height={0}
            sizes="50vw"
            className="h-[200px] md:h-[300px] w-full md:w-[auto] md:min-w-[600px] object-contain rounded-[10px] shadow-card"
            onClick={onToggleAllPhotos}
          />
          <Card className="w-full md:w-[50%] flex flex-col justify-between">
            <div>
              <Input
                name="caption"
                className="text-xs p-2 w-full mb-2"
                placeholder="Add a note about this photo"
                value={formState.caption ?? ""}
                onChange={onChangeTextInput}
              />
              {isAdmin && (
                <Input
                  name="alt_text"
                  className="text-xs p-2 w-full"
                  placeholder="Alt Text (for SEO)"
                  value={formState.alt_text ?? ""}
                  onChange={onChangeTextInput}
                />
              )}
            </div>
            <div className="flex items-center justify-between mt-4">
              <Button
                intent="outline"
                icon={<TrashIcon className="w-4 h-4 text-error" />}
                onClick={() => onDeleteClick(selectedImage.image_uuid)}
              ></Button>

              <Button
                onClick={onSave}
                className="!text-xs min-w-[120px]"
                disabled={progressStatus === ProgressStatus.LOADING}
                isLoading={progressStatus === ProgressStatus.LOADING}
              >
                Save / Update
              </Button>
            </div>
          </Card>
        </div>
      )}
      <div onClick={onClickedImage}>{children}</div>
      {!!confirmDeleteId && (
        <DeleteConfirm
          onClose={() => setConfirmDeleteId(null)}
          onDelete={onDeleteConfirm}
          progressStatus={progressStatus}
        />
      )}
      {showCarousel && (
        <PhotosCarousel
          open
          images={images}
          onClose={onToggleAllPhotos}
          currentIndex={selected ?? 0}
        />
      )}
    </>
  );
};

export default PhotosEditWrapper;
