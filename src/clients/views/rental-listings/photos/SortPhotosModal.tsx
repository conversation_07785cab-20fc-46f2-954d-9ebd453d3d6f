"use client";

import isEmpty from "lodash/isEmpty";
import { useCallback, useMemo, useState } from "react";
import React from "react";

import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { arrayMove, SortableContext } from "@dnd-kit/sortable";

import SortablePhotoItem from "./SortablePhotoItem";
import { Nullable, ProgressStatus } from "@/types/common";
import { PropertyImage } from "@/types/property";
import Button from "@/clients/ui/button";
import Modal from "@/clients/ui/modal";
import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
  photos: PropertyImage[];
  onClose: () => void;
};

const SortPhotosModal = ({ photos, onClose, propertyId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [activeId, setActiveId] = useState<Nullable<string | number>>(null);
  const [items, setItems] = useState<PropertyImage[]>([]);
  const getIndex = useCallback(
    (id: string | number) =>
      items.findIndex((_item) => _item.image_uuid === id),
    [items]
  );
  const activeIndex = useMemo(
    () => (activeId ? getIndex(activeId) : -1),
    [activeId, getIndex]
  );

  const sensors = useSensors(
    useSensor(MouseSensor),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  );

  const onSave = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);

    const payload = {
      images: items.map((_image, index) => ({
        ..._image,
        order: index + 1,
      })),
    };

    updatePropertyDetails(propertyId, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${propertyId}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Images successfully reordered!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [items, propertyId]);

  const handleDragEnd = ({ over }: any) => {
    if (over) {
      setActiveId(over.id);
      const overIndex = getIndex(over.id);
      if (activeIndex !== overIndex) {
        setItems((items) => arrayMove(items, activeIndex, overIndex));
        setActiveId(null);
      }
    }
  };

  React.useEffect(() => {
    if (!isEmpty(photos)) {
      setItems([...photos].sort((a, b) => a.order - b.order));
    } else {
      setItems([]);
    }
  }, [photos]);

  console.log("the items ", items);

  return (
    <Modal open className="w-full md:w-[80%] max-w-[1200px] p-4">
      <h1 className="font-bold">Sort Photos</h1>
      <hr className="mt-2 mb-4" />
      <div className="overflow-y-scroll h-[80vh]">
        <DndContext
          id="NR_property-listing-sortable"
          sensors={sensors}
          onDragEnd={handleDragEnd}
          onDragStart={({ active }) => {
            if (!active) {
              return;
            }

            setActiveId(active.id);
          }}
          onDragCancel={() => setActiveId(null)}
        >
          <SortableContext items={items.map((_item) => _item.image_uuid)}>
            <div className="flex items-center flex-wrap gap-[10px] xl:gap-3">
              {items?.map((_item) => (
                <SortablePhotoItem key={_item.image_uuid} item={_item} />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>

      <div className="flex items-center justify-between mt-2 gap-2">
        <Button onClick={onClose} intent="outline" className="text-sm">
          Cancel
        </Button>
        <Button
          onClick={onSave}
          className="text-sm w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save
        </Button>
      </div>
    </Modal>
  );
};

export default SortPhotosModal;
