import { memo } from "react";

import { defaultAnimateLayoutChanges, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import Image from "next/image";
import { PropertyImage } from "@/types/property";

type Props = {
  item: PropertyImage;
};

const SortablePhotoItem = ({ item }: Props) => {
  const {
    attributes,
    isDragging,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({
    id: item.image_uuid,
    animateLayoutChanges: defaultAnimateLayoutChanges,
  });

  const style = {
    transition,
    transform: CSS.Translate.toString(transform),
  };

  return (
    <div
      className="w-[calc(50%-5px)] md:w-[calc(33%-4px)] xl:w-[calc(25%-9px)] h-[100px] md:h-[160px] xl:h-[180px]"
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <Image
        src={item.url}
        loading="lazy"
        alt="images"
        width={0}
        height={0}
        sizes="128px"
        className="w-full h-full"
      />
      {isDragging && <div className="inner-border" />}
    </div>
  );
};

export default memo(SortablePhotoItem);
