"use client";

import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useState } from "react";

type Props = {
  images: string[];
  selectedIndex: number;
};

const ImageSlider = ({ images, selectedIndex }: Props) => {
  const [current, setCurrent] = useState<number>(selectedIndex);
  const currentImage = useMemo(() => images[current], [current, images]);

  const onClickLeft = useCallback(() => {
    if (current === 0) {
      setCurrent(images.length - 1);
    } else {
      setCurrent(current - 1);
    }
    setCurrent(1);
  }, [images, current]);

  const onClickRight = useCallback(() => {
    if (current === images.length - 1) {
      setCurrent(0);
    } else {
      setCurrent(current + 1);
    }
  }, [images, current]);

  useEffect(() => {
    setCurrent(selectedIndex);
  }, [selectedIndex]);

  return (
    <div className="h-[200px] md:h-[280px] w-full relative text-white">
      <div
        onClick={onClickLeft}
        className="absolute left-5 top-[50%] -translate-y-[50%] cursor-pointer rounded-full p-2 bg-backdrop-40"
      >
        <ChevronLeftIcon className="h-6 w-auto " />
      </div>

      <Image
        alt="property image"
        src={currentImage ?? ""}
        width={0}
        height={0}
        sizes="50vw"
        className="h-[200px] md:h-[280px] w-full object-cover rounded-[10px]"
        priority={current === 0}
      />
      <div
        onClick={onClickRight}
        className="absolute right-5 top-[50%] -translate-y-[50%] cursor-pointer rounded-full p-2 bg-backdrop-40"
      >
        <ChevronRightIcon className="h-6 w-auto" />
      </div>
      <p className="text-sm font-bold absolute bottom-4 left-[50%] -translate-x-[50%] bg-backdrop-40 rounded-xl px-2 py-[2px]">
        {current + 1} of {images.length}
      </p>
    </div>
  );
};

export default ImageSlider;
