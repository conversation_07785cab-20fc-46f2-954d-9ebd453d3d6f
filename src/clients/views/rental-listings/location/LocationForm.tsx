"use client";

import { getAreas, updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Checkbox from "@/clients/ui/checkbox";
import Input from "@/clients/ui/input";
import Select from "@/clients/ui/select";
import useForm from "@/hooks/useForm";
import { ProgressStatus } from "@/types/common";
import { Property, PropertyArea } from "@/types/property";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";

type Props = {
  property: Property;
};

type FormValues = {
  address: string;
  distance_to_beach: string;
  distance_to_the_hub: string;
  walk_to_beach?: boolean;
  waterfront?: boolean;
  water_view?: boolean;
  area: number;
};

const LocationForm = ({ property }: Props) => {
  const { data: areasPyload, isLoading } = useSWR("areas", () =>
    getAreas<{ results: PropertyArea[] }>()
  );
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      address: property?.address ?? "",
      distance_to_beach: property.distance_to_beach ?? "",
      distance_to_the_hub: property?.distance_to_the_hub ?? "",
      walk_to_beach: property?.walk_to_beach,
      waterfront: property?.waterfront,
      water_view: property?.water_view,
      area: property.area.id,
    },
    {
      address: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Name is required.`;
        }
      },
    }
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeArea = useCallback(
    (area: { id: string | number; name: string }) => {
      onChange(area.id, "area");
    },
    [onChange]
  );

  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      onChange(checked, name);
    },
    [onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    updatePropertyDetails(property.listing_id, formState)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id]);

  return (
    <div className="w-full xl:w-[50%] 2xl:w-[40%] border p-4 rounded-lg h-min">
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Location Details</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Property Address</p>
        <div className="w-[50%]">
          <Input
            name="address"
            className="text-xs p-2 w-full"
            placeholder="Property Address"
            value={formState.address}
            onChange={onChangeTextInput}
            helperText={errors?.address ?? ""}
            error={!!errors?.address?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Area</p>
        <Select
          className="text-xs font-bold w-[50%] px-2 py-2 text-center"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder="Area"
          options={areasPyload?.results ?? []}
          value={formState?.area}
          onChange={onChangeArea}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Walk to beach</p>
        <div className="w-[50%] flex">
          <Checkbox
            className="m-auto"
            name="walk_to_beach"
            checked={formState?.walk_to_beach}
            onChange={onToggleCheckobox}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Waterfront</p>
        <div className="w-[50%] flex">
          <Checkbox
            className="m-auto"
            name="waterfront"
            checked={formState?.waterfront}
            onChange={onToggleCheckobox}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Water Views</p>
        <div className="w-[50%] flex">
          <Checkbox
            className="m-auto"
            name="water_view"
            checked={formState?.water_view}
            onChange={onToggleCheckobox}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Distance to nearest beach (miles)</p>
        <div className="w-[50%]">
          <Input
            className="text-xs p-2 w-full"
            name="distance_to_beach"
            value={formState.distance_to_beach}
            onChange={onChangeTextInput}
            helperText={errors?.distance_to_beach ?? ""}
            error={!!errors?.distance_to_beach?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Distance to C&C office (miles)</p>
        <div className="w-[50%]">
          <Input
            className="text-xs p-2 w-full"
            name="distance_to_the_hub"
            value={formState.distance_to_the_hub}
            onChange={onChangeTextInput}
            helperText={errors?.distance_to_the_hub ?? ""}
            error={!!errors?.distance_to_the_hub?.length}
          />
        </div>
      </div>
    </div>
  );
};

export default LocationForm;
