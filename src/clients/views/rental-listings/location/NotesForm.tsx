"use client";

import { addPropertyComment } from "@/app/actions/property";
import Button from "@/clients/ui/button";
import Textarea from "@/clients/ui/textarea";
import { Nullable, ProgressStatus } from "@/types/common";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  propertyId: number;
};

const NotesForm = ({ propertyId }: Props) => {
  const [error, setError] = useState<string>("");
  const [note, setNote] = useState<string>("");
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onChangeTextInput = useCallback((event: any) => {
    const { value } = event.target;

    setNote(value);
  }, []);

  const preSubmitCheck = useCallback(() => {
    let err = "";
    if (note.trim().length === 0) {
      err = "Note is required.";
    }

    setError(err);
    return err;
  }, [note]);

  const clearForm = useCallback(() => {
    setError("");
    setNote("");
  }, []);

  const onSave = useCallback(() => {
    const _error = preSubmitCheck();
    console.log("the _error is", _error);
    if (_error !== "") {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    addPropertyComment(propertyId, {
      content: note,
    })
      .then((data) => {
        // revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Note added successfully!");
        clearForm();
      })
      .catch((err) => {
        console.log("error is", err);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [clearForm, note, preSubmitCheck, propertyId]);

  return (
    <>
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Notes</p>
        <Button
          onClick={onSave}
          className="!text-xs"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <Textarea
        className="w-full"
        value={note}
        onChange={onChangeTextInput}
        error={!!error}
        helperText={error ?? ""}
      />
    </>
  );
};

export default NotesForm;
