"use client";

import { FormEvent, useCallback } from "react";
import { FormValues } from "./GeneralInfoFormWrapper";
import Input from "@/clients/ui/input";
import Select from "@/clients/ui/select";
import CurrencyInput from "@/clients/ui/currency-input";
import CheckinCheckoutWrapper from "./CheckinCheckoutWrapper";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import dayjs from "dayjs";

const WEEKDAYS = [
  {
    id: "Sunday",
    name: "Sunday",
  },
  {
    id: "Monday",
    name: "Monday",
  },
  {
    id: "Tuesday",
    name: "Tuesday",
  },
  {
    id: "Wednesday",
    name: "Wednesday",
  },
  {
    id: "Thursday",
    name: "Thursday",
  },
  {
    id: "Friday",
    name: "Friday",
  },
  {
    id: "Saturday",
    name: "Saturday",
  },
];

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
};

const CheckinCheckoutForm = ({ formState, onChange, errors }: Props) => {
  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeTurnoverday = useCallback(
    (capacity: { id: string | number; name: string }) => {
      onChange(capacity.id, "turnover_day");
    },
    [onChange]
  );

  const onChangeChargeFee = useCallback(
    (capacity: { id: string | number; name: string }) => {
      onChange(capacity.id === "yes", "charge_community_impact_fee");
    },
    [onChange]
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, "");
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  const onChangeCheckinCheckoutTime = useCallback(
    (value: string, isCheckin?: boolean) => {
      onChange(value, isCheckin ? "checkin_time" : "checkout_time");
    },
    [onChange]
  );

  return (
    <>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Check-in Time</p>
        <CheckinCheckoutWrapper
          time={formState.checkin_time}
          isCheckin
          onChangeCheckinCheckoutTime={onChangeCheckinCheckoutTime}
        >
          <div className="rounded-md border md:border-[inherit] p-2 text-xs font-bold md:flex-center-between">
            {dayjs(formState?.checkin_time, "hh:mm:ss").format("hh:mm A") ??
              "NA"}
            <ChevronRightIcon className="hidden md:block h-4 w-4" />
          </div>
        </CheckinCheckoutWrapper>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Check-out Time</p>
        <CheckinCheckoutWrapper
          time={formState.checkout_time}
          onChangeCheckinCheckoutTime={onChangeCheckinCheckoutTime}
        >
          <div className="rounded-md border md:border-[inherit] p-2 text-xs font-bold md:flex-center-between">
            {dayjs(formState?.checkout_time, "hh:mm:ss").format("hh:mm A") ??
              "NA"}
            <ChevronRightIcon className="hidden md:block h-4 w-4" />
          </div>
        </CheckinCheckoutWrapper>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Turnover Day</p>
        <Select
          className="text-xs font-bold w-[50%] px-2 py-2"
          bodyClassName="min-h-[220px] overflow-y-scroll"
          placeholder="Turnover day"
          options={WEEKDAYS}
          value={formState?.turnover_day ?? ""}
          onChange={onChangeTurnoverday}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Security Deposit (%)</p>
        <Input
          name="security_deposit_percentage"
          wrapperclassName="w-[50%]"
          className="text-xs p-2 w-full"
          placeholder="Security Deposit %"
          value={formState?.security_deposit_percentage ?? ""}
          onChange={onChangeNumeric}
          helperText={errors?.security_deposit_percentage ?? ""}
          error={!!errors?.security_deposit_percentage?.length}
          pattern="\d*"
          inputMode="numeric"
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Minimum Security Deposit</p>
        <CurrencyInput
          name="min_security_deposit"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Minimum Security Deposit"
          value={formState?.min_security_deposit ?? ""}
          onChange={onChangeNumeric}
          helperText={errors?.min_security_deposit ?? ""}
          error={!!errors?.min_security_deposit?.length}
          pattern="\d*"
          inputMode="numeric"
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Cleaning Hours included</p>
        <Input
          name="cleaning_hours"
          wrapperclassName="w-[50%]"
          className="text-xs p-2 w-full"
          placeholder="-"
          value={formState?.cleaning_hours ?? ""}
          onChange={onChangeNumeric}
          helperText={errors?.cleaning_hours ?? ""}
          error={!!errors?.cleaning_hours?.length}
          pattern="\d*"
          inputMode="numeric"
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Comission %</p>
        <Input
          name="commission_percentage"
          wrapperclassName="w-[50%]"
          className="text-xs p-2 w-full"
          placeholder="-"
          value={formState?.commission_percentage ?? ""}
          onChange={onChangeNumeric}
          helperText={errors?.commission_percentage ?? ""}
          error={!!errors?.commission_percentage?.length}
          pattern="\d*"
          inputMode="numeric"
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Charge Community Impact Fee</p>
        <Select
          className="text-xs font-bold w-[50%] px-2 py-2"
          bodyClassName="-top-[85px]"
          placeholder="Turnover day"
          options={[
            {
              id: "yes",
              name: "Yes",
            },
            {
              id: "no",
              name: "No",
            },
          ]}
          value={formState?.charge_community_impact_fee ? "yes" : "no"}
          onChange={onChangeChargeFee}
        />
      </div>
    </>
  );
};

export default CheckinCheckoutForm;
