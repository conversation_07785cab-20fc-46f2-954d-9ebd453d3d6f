"use client";

import { ReactNode, useCallback, useState } from "react";

import dynamic from "next/dynamic";
import dayjs from "dayjs";

const TimePickerModal = dynamic(() => import("../../common/TimePickerModal"));

type Props = {
  children: ReactNode;
  time: string;
  isCheckin?: boolean;
  onChangeCheckinCheckoutTime: (value: string, isCheckin?: boolean) => void;
};

const CheckinCheckoutWrapper = ({
  children,
  time,
  isCheckin,
  onChangeCheckinCheckoutTime,
}: Props) => {
  const [show, setShow] = useState<boolean | null>(null);

  const onClick = useCallback(() => {
    setShow(true);
  }, []);

  const onCloseDesktop = useCallback(() => {
    setShow(false);
  }, []);

  const onSave = useCallback(
    (_time: Date) => {
      onChangeCheckinCheckoutTime(dayjs(_time).format("HH:mm:ss"), isCheckin);
      setShow(false);
    },
    [isCheckin, onChangeCheckinCheckoutTime]
  );

  return (
    <>
      <div onClick={onClick} className="cursor-pointer w-[50%]">
        {children}
      </div>
      {show && (
        <TimePickerModal
          open={show}
          onClose={onCloseDesktop}
          time={time}
          title={isCheckin ? "Check in time" : "Check out time"}
          isCheckin={isCheckin}
          onSave={onSave}
        />
      )}
    </>
  );
};

export default CheckinCheckoutWrapper;
