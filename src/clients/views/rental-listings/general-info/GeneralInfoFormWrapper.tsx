'use client';

import useForm from '@/hooks/useForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { Property } from '@/types/property';
import { useCallback, useState } from 'react';
import GeneralInfoForm from './GeneralInfoForm';
import Button from '@/clients/ui/button';
import HouseRulesForm, { PETS } from './HouseRulesForm';
import CheckinCheckoutForm from './CheckinCheckoutForm';
import { PHONE_NUMBER_PATTERN } from '@/constants/pattern';
import {
  deactivateListing,
  hideListing,
  publishListing,
  updatePropertyDetails,
} from '@/app/actions/property';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import toast from 'react-hot-toast';
import { PUBLISH_TYPE } from '../overview/PublishAndDistributionForm';

type Props = {
  property: Property;
};

export type FormValues = {
  key_number: Nullable<string>;
  house_phone: Nullable<string | number>;
  occupancy_tax_number: Nullable<string>;
  short_term_rental_permit_number: Nullable<string>;
  year_built: Nullable<number>;
  year_renovated: Nullable<number>;
  capacity: number;
  min_night_stay: number;
  pet_allow: string;
  pet_fee: string;
  checkin_time: string;
  checkout_time: string;
  turnover_day: Nullable<string>;
  security_deposit_percentage: Nullable<number>;
  min_security_deposit: Nullable<number>;
  cleaning_hours: number;
  cleaning_fee: number;
  commission_percentage: Nullable<number>;
  charge_community_impact_fee: boolean;
  publish_type: string;
  deactivated_reason: Nullable<string>;
};

const GeneralInfoFormWrapper = ({ property }: Props) => {
  console.log({ property });
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      key_number: property?.key_number ?? null,
      house_phone: property?.house_phone ?? null,
      occupancy_tax_number: property?.occupancy_tax_number ?? null,
      short_term_rental_permit_number:
        property?.short_term_rental_permit_number ?? null,
      year_built: property?.year_built ?? null,
      year_renovated: property?.year_renovated ?? null,
      capacity: property.capacity ?? 1,
      min_night_stay: property.requirement.min_night_stay ?? 1,
      pet_allow: property.requirement?.pet_allow ?? '',
      pet_fee: property.requirement?.pet_fee ?? '',
      checkin_time: property.requirement?.checkin_time ?? '',
      checkout_time: property.requirement?.checkout_time ?? '',
      turnover_day: property.requirement?.turnover_day ?? null,
      security_deposit_percentage: property.requirement
        ?.security_deposit_percentage
        ? property.requirement?.security_deposit_percentage * 100
        : null,
      min_security_deposit: property.requirement?.min_security_deposit ?? null,
      cleaning_hours: property.requirement?.cleaning_hours ?? 1,
      cleaning_fee: property.requirement?.cleaning_fee ?? 0,
      commission_percentage: property.requirement?.commission_percentage
        ? property.requirement.commission_percentage * 100
        : null,
      charge_community_impact_fee:
        property.requirement?.charge_community_impact_fee,
      publish_type: property.publish
        ? PUBLISH_TYPE.PUBLISH
        : property?.deleted_at
        ? PUBLISH_TYPE.DEACTIVATE
        : PUBLISH_TYPE.HIDE,
      deactivated_reason: property?.deactivated_reason ?? '',
    },
    {
      capacity: (_v, _n, _value) => {
        if (!_value) {
          return `Capacity is required.`;
        }
      },
      house_phone: (_v, _n, _value: string) => {
        if (!!_value && !_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      security_deposit_percentage: (_v, _n, _value: number) => {
        if (!!_value && _value > 100) {
          return 'Cannot be greater than 100';
        }
      },
      commission_percentage: (_v, _n, _value: number) => {
        if (!!_value && _value > 100) {
          return 'Cannot be greater than 100';
        }
      },
      deactivated_reason: (_v, _n, _value) => {
        if (
          _v.publish_type === PUBLISH_TYPE.DEACTIVATE &&
          _value.trim().length === 0
        ) {
          return `Deactivate reason name is required.`;
        }
      },
    }
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      key_number: formState.key_number,
      house_phone: formState.house_phone,
      occupancy_tax_number: formState.occupancy_tax_number,
      short_term_rental_permit_number:
        formState.short_term_rental_permit_number,
      year_built: formState.year_built,
      year_renovated: formState.year_renovated,
      capacity: formState.capacity,
      requirement: {
        ...property.requirement,
        min_night_stay: formState.min_night_stay,
        pet_allow: formState.pet_allow,
        pet_allow_label: PETS.find((_p) => _p.id === formState.pet_allow)?.name,
        pet_fee: formState.pet_fee,
        checkin_time: formState.checkin_time,
        checkout_time: formState.checkout_time,
        turnover_day: formState.turnover_day,
        security_deposit_percentage: formState.security_deposit_percentage
          ? formState.security_deposit_percentage / 100
          : null,
        min_security_deposit: formState.min_security_deposit,
        cleaning_hours: formState.cleaning_hours,
        cleaning_fee: formState.cleaning_fee,
        commission_percentage: formState.commission_percentage
          ? formState.commission_percentage / 100
          : null,
        charge_community_impact_fee: formState.charge_community_impact_fee,
      },
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Property details successfully updated!');
        if (formState.publish_type === PUBLISH_TYPE.PUBLISH) {
          publishListing(property.listing_id)
            .then((data) => {
              revalidateTagByName(`property-details-${property.listing_id}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success('Property Published successfully updated!');
            })
            .catch((err) => {
              console.log('error is', err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else if (formState.publish_type === PUBLISH_TYPE.HIDE) {
          hideListing(property.listing_id)
            .then((data) => {
              revalidateTagByName(`property-details-${property.listing_id}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success('Property hidden from Public successfully!');
            })
            .catch((err) => {
              console.log('error is', err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else if (formState.publish_type === PUBLISH_TYPE.DEACTIVATE) {
          deactivateListing(property.listing_id, {
            deactivated_reason: formState.deactivated_reason ?? '',
          })
            .then((data) => {
              revalidateTagByName(`property-details-${property.listing_id}`);
              setProgressStatus(ProgressStatus.SUCCESSFUL);
              toast.success('Property deactivated successfully!');
            })
            .catch((err) => {
              console.log('error is', err);
              toast.error(err.message);
              setProgressStatus(ProgressStatus.FAILED);
            });
        } else {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id, property.requirement]);

  return (
    <>
      <div className="p-2 mb-2 md:mb-4 w-full flex items-center justify-between bg-white">
        <p className="text-sm font-bold">General Information</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="w-full md:border p-2 md:p-4 rounded-lg h-min">
          <GeneralInfoForm
            formState={formState}
            errors={errors}
            onChange={onChange}
          />
        </div>
        <div className="w-full md:border p-2 md:p-4 rounded-lg h-min">
          <HouseRulesForm
            formState={formState}
            errors={errors}
            onChange={onChange}
          />
        </div>
        <div className="w-full md:border p-2 md:p-4 rounded-lg h-min">
          <p className="text-sm font-bold">Check-in and Check-out</p>
          <CheckinCheckoutForm
            formState={formState}
            errors={errors}
            onChange={onChange}
          />
        </div>
      </div>
    </>
  );
};

export default GeneralInfoFormWrapper;
