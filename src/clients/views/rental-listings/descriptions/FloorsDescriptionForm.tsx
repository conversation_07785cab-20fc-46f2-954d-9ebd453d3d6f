"use client";

import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Textarea from "@/clients/ui/textarea";
import useForm from "@/hooks/useForm";
import { ProgressStatus } from "@/types/common";
import { Property } from "@/types/property";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  property: Property;
  areas?: any;
};

type FormValues = {
  description: string;
  first_floor: string;
  second_floor: string;
};

const FloorDescriptionForm = ({ property, areas = [] }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      description: property?.description ?? "",
      first_floor: property.first_floor ?? "",
      second_floor: property?.second_floor ?? "",
    },
    {
      description: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Main description is required.`;
        }
      },
    }
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    updatePropertyDetails(property.listing_id, formState)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id]);

  return (
    <>
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Floor Descriptions</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs">Main Description</p>
        <div className="w-[70%]">
          <Textarea
            placeholder="Main Description"
            className="text-xs p-2 w-full"
            name="description"
            value={formState.description}
            onChange={onChangeTextInput}
            helperText={errors?.description ?? ""}
            error={!!errors?.description?.length}
            rows={6}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs">First Floor</p>
        <div className="w-[70%]">
          <Textarea
            placeholder="First floor description"
            className="text-xs p-2 w-full"
            name="first_floor"
            value={formState.first_floor}
            onChange={onChangeTextInput}
            helperText={errors?.first_floor ?? ""}
            error={!!errors?.first_floor?.length}
            rows={4}
          />
        </div>
      </div>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[30%] text-xs">Second Floor</p>
        <div className="w-[70%]">
          <Textarea
            placeholder="Second floor description"
            className="text-xs p-2 w-full"
            name="second_floor"
            value={formState.second_floor}
            onChange={onChangeTextInput}
            helperText={errors?.second_floor ?? ""}
            error={!!errors?.second_floor?.length}
            rows={4}
          />
        </div>
      </div>
    </>
  );
};

export default FloorDescriptionForm;
