"use client";

import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import Button from "@/clients/ui/button";
import Input from "@/clients/ui/input";
import Textarea from "@/clients/ui/textarea";
import useForm from "@/hooks/useForm";
import { ProgressStatus } from "@/types/common";
import { Property } from "@/types/property";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

type Props = {
  property: Property;
  isAdmin?: boolean;
  permaLink: React.ReactNode;
};

type FormValues = {
  headline: string;
  permalink: string;
  meta_description: string;
};

const DescriptionForm = ({ property, isAdmin, permaLink }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      headline: property?.headline ?? "",
      permalink: "",
      meta_description: property.meta_description ?? "",
    },
    {
      headline: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Headline is required.`;
        }
      },
    }
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== "")) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    updatePropertyDetails(property.listing_id, {
      headline: formState.headline,
      meta_description: formState.meta_description,
    })
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, preSubmitCheck, property.listing_id]);

  return (
    <>
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Descriptions</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Headline</p>
        <div className="w-[70%]">
          <Input
            name="headline"
            className="text-xs p-2 w-full"
            placeholder="Property headline"
            value={formState.headline}
            onChange={onChangeTextInput}
            helperText={errors?.headline ?? ""}
            error={!!errors?.headline?.length}
          />
        </div>
      </div>
      {isAdmin && (
        <>
          <div className="py-2 flex items-center gap-2 w-full">
            <p className="w-[30%] text-xs">SEO</p>
            <div className="w-[70%]">
              <Textarea
                name="meta_description"
                className="text-xs p-2 w-full"
                placeholder="Property Meta description"
                value={formState.meta_description}
                onChange={onChangeTextInput}
                helperText={errors?.meta_description ?? ""}
                error={!!errors?.meta_description?.length}
                rows={4}
              />
            </div>
          </div>
        </>
      )}

      {permaLink}
    </>
  );
};

export default DescriptionForm;
