"use client";

import { Property } from "@/types/property";
import {
  ReactNode,
  createContext,
  useEffect,
  useMemo,
  useOptimistic,
  useState,
} from "react";

type Context = {
  optimisticProperty: Property;
  onSuccessfullyUpdateProperty: (action: Property) => void;
};

export const ListingsContext = createContext<Context>({} as Context);

type ContextProps = {
  children: ReactNode;
  propertyDetails: Property;
};

const ListingsContextContextContainer = ({
  children,
  propertyDetails,
}: ContextProps) => {
  const [optimisticProperty, onSuccessfullyUpdateProperty] = useOptimistic<
    Property,
    Property
  >(propertyDetails, (state, updatedDetails) => ({
    ...propertyDetails,
    ...updatedDetails,
  }));
  return (
    <ListingsContext.Provider
      value={{ optimisticProperty, onSuccessfullyUpdateProperty }}
    >
      {children}
    </ListingsContext.Provider>
  );
};

export default ListingsContextContextContainer;
