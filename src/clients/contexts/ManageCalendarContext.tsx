"use client";

import {
  ReactNode,
  createContext,
  useEffect,
  useMemo,
  useOptimistic,
  useState,
} from "react";

import {
  CalendarViewType,
  PropertyRentalRates,
  RtlSlidePane,
} from "@/types/calendar";

import dayjs, { Dayjs } from "dayjs";
import { Nullable } from "@/types/common";

type Context = {
  currentMonth: number;
  setCurrentMonth: (val: number) => void;
  currentYear: number;
  setCurrentYear: (val: number) => void;
  selected: [null | Dayjs, null | Dayjs];
  setSelected: (_v: [null | Dayjs, null | Dayjs]) => void;
  rtlSlidePane: RtlSlidePane | null;
  setRtlSlidePane: (_v: RtlSlidePane | null) => void;
  view: CalendarViewType;
  setView: (_v: CalendarViewType) => void;
  optimisticRates: PropertyRentalRates[];
  onSuccessfullyUpdateRates: (action: PropertyRentalRates[]) => void;
  selectedAvailabilityId: Nullable<string>;
  setSelectedAvailabilityId: (_id: Nullable<string>) => void;
};

export const ManageCalendarContext = createContext<Context>({} as Context);

type ContextProps = {};

const ManageCalendarContextContainer = ({
  children,
  defaultYear,
  rentalRates,
}: {
  children: ReactNode;
  endpointPath?: string;
  defaultYear: string;
  rentalRates: PropertyRentalRates[];
}) => {
  const year = useMemo(() => dayjs(defaultYear, "YYYY"), [defaultYear]);
  const [view, setView] = useState<CalendarViewType>(CalendarViewType.YEAR);
  const [optimisticRates, onSuccessfullyUpdateRates] = useOptimistic<
    PropertyRentalRates[],
    PropertyRentalRates[]
  >(rentalRates, (state, newRates) => [...newRates]);
  const [rtlSlidePane, setRtlSlidePane] = useState<RtlSlidePane | null>(null);
  const [currentMonth, setCurrentMonth] = useState<number>(
    year.year() !== dayjs().year() ? 0 : dayjs().month()
  );
  const [currentYear, setCurrentYear] = useState<number>(year.year());
  const [selected, setSelected] = useState<[null | Dayjs, null | Dayjs]>([
    null,
    null,
  ]);
  const [selectedAvailabilityId, setSelectedAvailabilityId] =
    useState<Nullable<string>>(null);

  useEffect(() => {
    if (year) {
      setCurrentMonth(year.year() !== dayjs().year() ? 0 : dayjs().month());
      setCurrentYear(year.year());
    }
  }, [year]);

  return (
    <ManageCalendarContext.Provider
      value={{
        currentMonth,
        currentYear,
        selected,
        setCurrentMonth,
        setCurrentYear,
        setSelected,
        rtlSlidePane,
        setRtlSlidePane,
        optimisticRates,
        onSuccessfullyUpdateRates,
        view,
        setView,
        selectedAvailabilityId,
        setSelectedAvailabilityId,
      }}
    >
      {children}
    </ManageCalendarContext.Provider>
  );
};

export default ManageCalendarContextContainer;
