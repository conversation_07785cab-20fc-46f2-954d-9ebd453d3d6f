import * as React from 'react';

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { DayPicker, DayPickerProps } from 'react-day-picker';

import { cn } from '@/lib/utils';

import { format } from 'date-fns';
import { buttonVariants } from './button';

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: DayPickerProps & {
  onAdditionalDayMouseEnter?: (day: Date) => void;
}) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-3', className)}
      classNames={{
        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
        month: 'space-y-4',
        caption: 'flex justify-center pt-1 relative items-center',
        caption_label: 'text-sm font-medium',
        nav: 'space-x-1 flex items-center',
        nav_button: cn(
          buttonVariants({ intent: 'outline' }),
          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 border-none cursor-pointer'
        ),
        nav_button_previous: 'absolute left-1',
        nav_button_next: 'absolute right-1',
        table: 'w-full border-collapse space-y-1',
        head_row: 'flex',
        head_cell:
          'text-muted-foreground rounded-md w-9 h-10 font-normal text-[0.8rem] flex items-center justify-center flex-1',
        row: 'flex w-full my-[2px]',
        cell: cn(
          'relative h-10 w-10  p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-carolina-blue-20 [&:has(>.day-outside)]:invisible [&:has([data-hover=true]):not(:has([disabled]))]:bg-carolina-blue-20 rounded-full'
          // props.mode === 'range'
          //   ? '[&:has(>.day-range-end)]:rounded-r-[50%] [&:has(>.day-range-start)]:rounded-l-[50%] first:[&:has([aria-selected])]:rounded-l-[50%] last:[&:has([aria-selected])]:rounded-r-[50%] first:[&:has([data-hover=true])]:rounded-l-[50%] last:[&:has([data-hover=true])]:rounded-r-[50%] [&:has([data-hover-last=true])]:rounded-r-[50%]'
          //   : '[&:has([aria-selected])]:rounded-[50%]',
        ),
        day: cn(
          buttonVariants({ intent: 'ghost' }),
          'cursor-pointer h-10 w-10 p-0 aria-selected:opacity-100 rounded-full border border-transparent border-solid hover:border-[#6D7380] hover:rounded-full font-bold disabled:cursor-not-allowed'
        ),
        day_range_start: 'day-range-start text-white !bg-primary-blue',
        day_range_end: 'day-range-end text-white !bg-primary-blue',
        day_today: 'border border-solid text-accent-foreground',
        day_outside:
          'day-outside text-muted-foreground opacity-50 aria-selected:text-muted-foreground aria-selected:opacity-30',
        day_disabled:
          'text-muted-foreground opacity-50 font-normal line-through',
        day_range_middle: 'aria-selected:text-accent-foreground',
        day_hidden: 'invisible',
        ...classNames,
      }}
      onDayMouseEnter={(day, __, event) => {
        if (props.onAdditionalDayMouseEnter) {
          props?.onAdditionalDayMouseEnter(day);
        }
        // if more then 1 selected then no need of hover effect
        const selected = props.mode === 'single' && (props.selected as any);

        // Reset data-hover if more then 1 selected or if none are selected
        if (
          (selected?.from && selected?.to) ||
          (!selected?.from && !selected?.to)
        ) {
          const resetButtons = Array.from(
            document.querySelectorAll('[data-hover]')
          );

          resetButtons.forEach((item: any) => {
            item.setAttribute('data-hover', 'false');
          });
          return;
        }

        // Get the current target button
        const currentButton = event.target as HTMLButtonElement;

        // Find the closest ancestor react-date picker wrapper element
        const rdpWrapper = currentButton.closest('.rdp-multiple_months') as any;

        // Find all buttons within the <rdpWrapper> element
        const buttons = Array.from(rdpWrapper.querySelectorAll('button'));

        // Find the index of the current button in the buttons array
        const currentIndex = buttons.indexOf(currentButton);

        // Find the index of the button with aria-selected="true"
        const selectedIndex = buttons.findIndex(
          (button: any) => button.getAttribute('aria-selected') === 'true'
        );

        // Create an array to store the selected buttons
        const selectedButtons = [] as any[];

        // Determine the range of buttons to select
        const start = Math.min(currentIndex, selectedIndex);
        const end = Math.max(currentIndex, selectedIndex);

        // Select the buttons between the current button and the button with aria-selected="true" (inclusive)
        // eslint-disable-next-line no-plusplus
        for (let i = start; i <= end; i++) {
          selectedButtons.push(buttons[i]);
        }

        buttons.forEach((item: any) => {
          // run over all buttons and set data-hover true to those who in range
          item.setAttribute(
            'data-hover',
            selectedButtons.includes(item) ? 'true' : 'false'
          );
          if (
            selectedButtons.includes(item) &&
            selectedButtons.findIndex((_i) => _i === item) ===
              selectedButtons.length - 1
          ) {
            item.setAttribute('data-hover-last', 'true');
          } else {
            item.removeAttribute('data-hover-last');
          }
        });
      }}
      formatters={{ formatWeekdayName: (weekday) => format(weekday, 'EEEEE') }}
      components={{
        IconLeft: ({ ...props }) => <ChevronLeftIcon className="h-4 w-4" />,
        IconRight: ({ ...props }) => <ChevronRightIcon className="h-4 w-4" />,
      }}
      disabled={props.disabled}
      {...props}
    />
  );
}
Calendar.displayName = 'Calendar';

export { Calendar };
