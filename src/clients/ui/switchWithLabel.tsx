'use client';

import { memo } from 'react';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  label1: string;
  label2: string;
  className?: string;
  isOn?: boolean;
  onToggle?: () => void;
  highlightClassName?: string;
  disabled?: boolean;
};
const SwitchWithLabel = ({
  label1,
  label2,
  className = '',
  onToggle,
  isOn,
  highlightClassName = '',
  disabled,
}: Props) => {
  return (
    <div
      className={twMerge(
        classNames('relative flex items-center border rounded-2xl p-2 cursor-pointer', {
          'cursor-not-allowed opacity-70': disabled,
        }),
        className,
      )}
      onClick={disabled ? undefined : onToggle}
    >
      <span
        className={twMerge(
          classNames(
            'bg-carolina-blue w-[calc(50%-8px)] h-[calc(100%-16px)] absolute rounded-2xl transition duration-200 ease-in-out',
            {
              'translate-x-[100%]': isOn,
            },
          ),
          highlightClassName,
        )}
      />
      <div
        className={classNames('rounded-2xl p-2 z-[1] w-[50%] text-center text-xs', {
          'text-white font-bold': !isOn,
        })}
      >
        {label1}
      </div>
      <div
        className={classNames('rounded-2xl p-2 z-[1] w-[50%] text-center text-xs', {
          'text-white font-bold': isOn,
        })}
      >
        {label2}
      </div>
    </div>
  );
};

export default memo(SwitchWithLabel);
