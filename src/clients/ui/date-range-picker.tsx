'use client';

import { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { CalendarIcon } from '@heroicons/react/24/outline';

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
}

const DateRangePicker = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  placeholder = "Date Range",
  className = ""
}: DateRangePickerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDateRange = (start: Date, end: Date): string => {
    const startYear = start.getFullYear();
    const endYear = end.getFullYear();
    
    if (startYear === endYear) {
      // Same year: "Aug 21 - Sep 12, 2025"
      const startFormatted = start.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
      const endFormatted = end.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
      return `${startFormatted} - ${endFormatted}`;
    } else {
      // Different years: "Aug 21, 2024 - Sep 12, 2025"
      return `${formatDate(start)} - ${formatDate(end)}`;
    }
  };

  const displayText = startDate && endDate 
    ? formatDateRange(startDate, endDate)
    : placeholder;

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    onStartDateChange(start);
    onEndDateChange(end);
  };

  const clearDates = () => {
    onStartDateChange(null);
    onEndDateChange(null);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Single Date Range Field - matches Element UI styling */}
      <div 
        className="w-full h-12 border-2 border-gray-800 focus:border-gray-900 focus:ring-gray-900 rounded px-3 py-2 flex items-center justify-between cursor-pointer text-gray-500 hover:border-gray-900"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={startDate && endDate ? 'text-gray-900' : 'text-gray-500'}>
          {displayText}
        </span>
        <CalendarIcon className="h-4 w-4 text-gray-400" />
      </div>
      
      {isOpen && (
        <div className="absolute z-50 mt-1 bg-white border border-gray-300 rounded shadow-lg">
          {/* Element UI Style Calendar */}
          <DatePicker
            selected={startDate}
            onChange={handleDateChange}
            startDate={startDate}
            endDate={endDate}
            selectsRange={true}
            selectsStart={true}
            selectsEnd={true}
            dateFormat="MM/dd/yyyy"
            placeholderText="Select date range"
            monthsShown={2}
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            inline
            className="w-full"
            calendarClassName="border-0 shadow-none [&_.react-datepicker__header]:bg-white [&_.react-datepicker__header]:border-b [&_.react-datepicker__header]:border-gray-200 [&_.react-datepicker__month-container]:border-0 [&_.react-datepicker__day]:text-gray-900 [&_.react-datepicker__day]:hover:bg-blue-50 [&_.react-datepicker__day]:rounded [&_.react-datepicker__day]:text-sm [&_.react-datepicker__day]:w-8 [&_.react-datepicker__day]:h-8 [&_.react-datepicker__day]:flex [&_.react-datepicker__day]:items-center [&_.react-datepicker__day]:justify-center [&_.react-datepicker__day--outside-month]:text-gray-300 [&_.react-datepicker__day--selected]:bg-blue-500 [&_.react-datepicker__day--selected]:text-white [&_.react-datepicker__day--in-range]:bg-blue-100 [&_.react-datepicker__day--in-range]:text-blue-900 [&_.react-datepicker__day--keyboard-selected]:bg-blue-500 [&_.react-datepicker__day--keyboard-selected]:text-white [&_.react-datepicker__navigation]:text-gray-600 [&_.react-datepicker__navigation]:hover:text-gray-900 [&_.react-datepicker__current-month]:text-gray-900 [&_.react-datepicker__current-month]:font-semibold [&_.react-datepicker__current-month]:text-base [&_.react-datepicker__day-name]:text-gray-600 [&_.react-datepicker__day-name]:font-medium [&_.react-datepicker__day-name]:text-xs [&_.react-datepicker__day-name]:w-8 [&_.react-datepicker__day-name]:h-8 [&_.react-datepicker__day-name]:flex [&_.react-datepicker__day-name]:items-center [&_.react-datepicker__day-name]:justify-center [&_.react-datepicker__month]:p-4 [&_.react-datepicker__month-container]:p-0 [&_.react-datepicker__month-container]:mr-4 [&_.react-datepicker__month-container:last-child]:mr-0 [&_.react-datepicker__week]:flex [&_.react-datepicker__week]:justify-center [&_.react-datepicker__week]:items-center"
            dayClassName={(date) => {
              if (startDate && endDate && date >= startDate && date <= endDate) {
                return 'bg-blue-100 text-blue-900';
              }
              if (startDate && date.getTime() === startDate.getTime()) {
                return 'bg-blue-500 text-white';
              }
              if (endDate && date.getTime() === endDate.getTime()) {
                return 'bg-blue-500 text-white';
              }
              return '';
            }}
            onCalendarOpen={() => setIsOpen(true)}
            onCalendarClose={() => setIsOpen(false)}
            onClickOutside={() => setIsOpen(false)}
            onInputClick={() => setIsOpen(true)}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                setIsOpen(false);
              }
            }}
          />
          
          {/* Element UI Style Footer */}
          <div className="p-4 border-t border-gray-200 flex justify-between items-center">
            <button
              onClick={clearDates}
              className="text-gray-600 underline text-sm hover:text-gray-900"
            >
              Clear dates
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
