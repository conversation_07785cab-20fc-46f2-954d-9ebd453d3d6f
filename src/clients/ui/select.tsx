'use client';
import { Nullable } from '@/types/common';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';

type Props = {
  placeholder?: string;
  className?: string;
  bodyClassName?: string;
  name?: string;
  options?: {
    id: number | string;
    name: string;
  }[];
  value?: Nullable<string | number>;
  onChange?: (
    value: { id: string | number; name: string },
    name?: string
  ) => void;
  disabled?: boolean;
  text?: Nullable<ReactNode>;
};

const Select = ({
  className = '',
  bodyClassName = '',
  options = [],
  placeholder = '',
  value,
  onChange,
  name,
  disabled = false,
  text,
}: Props) => {
  const selectedRef = useRef<null | HTMLDivElement>(null);
  const containerRef = useRef<null | HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const selected = useMemo(
    () => options?.find((_o) => _o.id === value),
    [options, value]
  );

  useEffect(() => {
    if (selectedRef && containerRef && isOpen) {
      containerRef?.current?.scrollTo(0, selectedRef.current?.offsetTop ?? 0);
    }
  }, [isOpen]);

  return (
    <Popover open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <PopoverTrigger asChild>
        <div
          className={classNames(
            twMerge(
              'relative px-4 py-2 border rounded-md flex items-center justify-between cursor-pointer',
              disabled ? 'bg-gray-100 cursor-not-allowed opacity-70' : '',
              className
            )
          )}
        >
          {text ? (
            text
          ) : (
            <p className="w-[90%]">
              {selected?.name ?? placeholder ?? 'Select'}
            </p>
          )}
          {!disabled &&
            (isOpen ? (
              <ChevronUpIcon className="w-auto h-[14px] transition-all" />
            ) : (
              <ChevronDownIcon className="w-auto h-[14px] transition-all" />
            ))}
        </div>
      </PopoverTrigger>
      <PopoverContent
        className={twMerge(
          'w-[var(--radix-popover-trigger-width)] p-0 shadow-card',
          bodyClassName
        )}
        align="start"
      >
        <div ref={containerRef} className="py-2">
          {options?.map((_option, index) => (
            <div
              ref={selected?.id === _option.id ? selectedRef : null}
              key={index}
              onClick={() => {
                onChange?.(_option, name);
                setIsOpen(false);
              }}
              className={classNames(
                'hover:bg-gray-100 py-2 px-4 text-left font-normal cursor-pointer text-sm',
                {
                  'text-carolina-blue font-medium': selected?.id === _option.id,
                }
              )}
            >
              {_option.name}
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default Select;
