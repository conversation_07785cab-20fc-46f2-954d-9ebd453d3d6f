'use client';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { ReactNode, useCallback, useMemo, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { Popover, PopoverContent, PopoverTrigger } from './popover';

type Props = {
  placeholder?: string | ReactNode;
  className?: string;
  bodyClassName?: string;
  options?: {
    id: number | string;
    name: string;
  }[];
  value?: (string | number)[];
  onChange?: (value: { id: string | number; name: string }) => void;
  isClearable?: boolean;
  onRemove?: (id: string | number) => void;
};

const MultiSelect = ({
  className = '',
  bodyClassName = '',
  options = [],
  placeholder = '',
  value,
  onChange,
  isClearable,
  onRemove,
}: Props) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const selected = useMemo(
    () => options?.filter((_o) => value?.includes(_o.id)) ?? [],
    [options, value]
  );

  const onRemoveItem = useCallback(
    (e: any, id: any) => {
      e.preventDefault();
      e.stopPropagation();
      onRemove?.(id);
    },
    [onRemove]
  );

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          className={classNames(
            twMerge(
              'relative px-4 py-2 border rounded-md flex items-center justify-between cursor-pointer',
              className
            )
          )}
        >
          {selected.length > 0 ? (
            <div className="w-[90%] flex flex-wrap gap-2">
              <div className="border px-2 rounded cursor-default flex items-center">
                {selected[0].name}
                {isClearable && (
                  <XCircleIcon
                    onClick={(e) => onRemoveItem(e, selected[0].id)}
                    className="ml-2 w-5 h-5 cursor-pointer"
                  />
                )}
              </div>
              {selected.length > 1 && (
                <div className="border px-2 rounded cursor-default flex items-center">
                  +{selected.length - 1}
                </div>
              )}
            </div>
          ) : (
            <>
              {typeof placeholder === 'string' && (
                <p className="w-[90%]">{placeholder}</p>
              )}
              {typeof placeholder === 'object' && placeholder}
            </>
          )}
          {isOpen ? (
            <ChevronUpIcon className="w-auto h-[14px]" />
          ) : (
            <ChevronDownIcon className="w-auto h-[14px]" />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        className={twMerge(
          'w-[var(--radix-popover-trigger-width)] p-0 shadow-card',
          bodyClassName
        )}
        align="start"
      >
        <div className="py-2">
          {options?.map((_option, index) => (
            <div
              key={index}
              onClick={() => onChange?.(_option)}
              className={classNames(
                'hover:bg-gray-100 py-2 pl-4 text-left font-normal cursor-pointer',
                {
                  'text-carolina-blue font-medium': value?.includes(_option.id),
                }
              )}
            >
              {_option.name}
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default MultiSelect;
