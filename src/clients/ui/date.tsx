import { ReactNode, memo } from 'react';

import FormHelperText from '@/app/ui/form-helper-text';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  wrapperclassName?: string;
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
  icon?: ReactNode;
  borderless?: boolean;
} & React.HTMLProps<HTMLInputElement>;

const DateInput = ({
  label = '',
  required,
  error,
  helperText,
  className,
  wrapperclassName = '',
  placeholder,
  borderless,
  icon,
  ...props
}: Props) => {
  return (
    <div className={twMerge('relative', wrapperclassName)}>
      {icon}
      <input
        {...props}
        type="date"
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'p-2 text-xs input input-bordered h-min min-h-min rounded focus:outline-none md:text-base md:p-4',
            error && 'border-error',
            { 'border-none disabled:bg-white cursor-auto': borderless }
          ),
          className
        )}
      />
      {helperText && (
        <div className="ml-2 absolute">
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default memo(DateInput);
