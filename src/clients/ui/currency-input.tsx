'use client';

import { memo, useCallback, useState, useEffect } from 'react';
import FormHelperText from '@/app/ui/form-helper-text';
import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  wrapperclassName?: string;
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
  iconClassName?: string;
  isStringValue?: boolean;
  name?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
} & React.HTMLProps<HTMLInputElement>;

// Format number with 2 decimal places and comma separation
const formatCurrency = (val: string | number | readonly string[]) => {
  if (isNaN(Number(val)) && !val) {
    return '';
  }
  const stringVal = Array.isArray(val) ? val[0] : val.toString();
  const num = parseFloat(stringVal);
  if (isNaN(num)) return '';
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const CurrencyInput = ({
  label = '',
  required,
  error,
  helperText,
  className,
  wrapperclassName = '',
  iconClassName = '',
  placeholder,
  value,
  isStringValue,
  name,
  onChange,
  ...props
}: Props) => {
  const [displayValue, setDisplayValue] = useState(formatCurrency(value ?? ''));
  const [isFocused, setIsFocused] = useState(false);

  // Update display value when value prop changes
  useEffect(() => {
    if (!isFocused) {
      setDisplayValue(formatCurrency(value ?? ''));
    }
  }, [value, isFocused]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value.replace(/,/g, '');

      // Allow empty string or valid decimal numbers
      if (rawValue === '' || /^(\d+\.?\d*|\.\d*)$/.test(rawValue)) {
        setDisplayValue(e.target.value);

        // Create synthetic event with raw numeric value
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            name: e.target.name,
            value: rawValue,
          },
          currentTarget: {
            ...e.currentTarget,
            name: e.currentTarget.name,
            value: rawValue,
          },
        };

        onChange?.(syntheticEvent as any);
      }
    },
    [onChange]
  );

  const handleFocus = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      // Show raw value without formatting when focused
      const rawValue = (value || '').toString().replace(/,/g, '');
      setDisplayValue(rawValue);
      props.onFocus?.(e);
    },
    [value, props]
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      // Format the value when losing focus
      setDisplayValue(formatCurrency(value || ''));
      props.onBlur?.(e);
    },
    [value, props]
  );

  return (
    <div
      className={twMerge('relative', wrapperclassName, error && 'border-error')}
    >
      {!isStringValue && (
        <span
          className={twMerge(
            classNames('text-sm font-bold', {
              'input-disabled': props?.disabled,
            }),
            iconClassName
          )}
        >
          $
        </span>
      )}

      <input
        {...props}
        name={name}
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4 text-right',
            error && '!border-error'
          ),
          className
        )}
        inputMode="decimal"
        autoComplete="off"
      />
      {helperText && (
        <div className="ml-2 absolute">
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default memo(CurrencyInput);
