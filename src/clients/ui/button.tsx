import { memo } from 'react';

import { cva } from 'class-variance-authority';
import { twMerge } from 'tailwind-merge';

type Props = {
  title?: string;
  onClick?: () => void;
  isSubmit?: boolean;
  intent?: 'primary' | 'secondary' | 'outline' | 'disabled' | 'ghost' | 'link';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
  children?: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

export const buttonVariants = cva('button', {
  variants: {
    intent: {
      primary: [
        'bg-olive',
        'text-white',
        'border-transparent',
        'hover:bg-olive/80',
        'hover:border-transparent',
      ],
      secondary: ['bg-white', 'text-[#0E1717]', 'border', 'hover:bg-gray-100'],
      disabled: ['bg-gray-300 hover:bg-gray-400'],
      outline: [
        'bg-transparent',
        'text-[#0E1717]',
        'border',
        'hover:bg-transparent',
        'hover:text-[#0E1717]',
      ],
      ghost: 'bg-transparent border-none text-primary-text',
      link: 'text-primary-text underline-offset-4 hover:underline',
    },
    size: {
      small: ['text-sm', 'py-1', 'px-2', 'min-h-min', 'h-9'],
      medium: ['text-base', 'py-2', 'px-4', 'font-bold'],
      large: ['text-base', 'py-3', 'px-4', 'font-bold'],
    },
  },
  compoundVariants: [{ intent: 'primary', size: 'large', class: '' }],
  defaultVariants: {
    intent: 'primary',
    size: 'medium',
  },
});

const Button = ({
  title,
  onClick,
  isSubmit,
  intent,
  className = '',
  size,
  icon,
  children,
  isLoading,
  ...rest
}: Props) => (
  <button
    {...rest}
    type={isSubmit ? 'submit' : 'button'}
    className={buttonVariants({
      intent,
      size,
      className: twMerge(
        'inline-flex items-center justify-center rounded focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed',
        className
      ),
    })}
    onClick={onClick}
  >
    {icon && <span className={title && 'mr-2'}>{icon}</span>}
    {title ?? isLoading ? (
      <span className="loading loading-spinner loading-sm"></span>
    ) : (
      children
    )}
  </button>
);

export default memo(Button);
