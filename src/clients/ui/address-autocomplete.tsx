'use client';

import React, { useState, useEffect, useRef } from 'react';
import { getListingAddresses } from '@/app/actions/property';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface AddressOption {
  listing_id: number;
  address: string;
  nr_listing_id: number;
}

interface AddressAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  icon?: React.ReactNode;
}

const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Search addresses...",
  className = "",
  icon
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<AddressOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const containerRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (inputValue.trim().length >= 2) {
        setLoading(true);
        try {
          console.log('Searching for addresses with query:', inputValue.trim());
          const response = await getListingAddresses<{ results: AddressOption[] }>(inputValue.trim());
          console.log('Address search response:', response);
          setOptions(response.results || []);
        } catch (error) {
          console.error('Failed to fetch addresses:', error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      } else {
        setOptions([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [inputValue]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setIsOpen(true);
    
    // If user clears the input, also clear the selected value
    if (!newValue) {
      onChange('');
    }
  };

  const handleOptionSelect = (option: AddressOption) => {
    setInputValue(option.address);
    onChange(option.address);
    setIsOpen(false);
  };

  const handleInputFocus = () => {
    if (inputValue.trim().length >= 2) {
      setIsOpen(true);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Input field */}
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
            {icon}
          </div>
        )}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full h-12 !border !border-gray-800 focus:border-gray-900 focus:ring-gray-900 rounded text-gray-500 placeholder-gray-500 ${
            icon ? 'pl-10' : 'pl-3'
          } pr-10`}
        />
        <ChevronDownIcon 
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {loading ? (
            <div className="px-4 py-2 text-sm text-gray-500">Loading...</div>
          ) : options.length > 0 ? (
            <div>
              {options.map((option) => (
                <div
                  key={option.listing_id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                  onClick={() => handleOptionSelect(option)}
                >
                  <div className="font-medium text-gray-900">{option.address}</div>
                  <div className="text-xs text-gray-500">ID: {option.nr_listing_id}</div>
                </div>
              ))}
            </div>
          ) : inputValue.trim().length >= 2 ? (
            <div className="px-4 py-2 text-sm text-gray-500">No addresses found</div>
          ) : (
            <div className="px-4 py-2 text-sm text-gray-500">Type at least 2 characters to search</div>
          )}
        </div>
      )}
    </div>
  );
};

export default AddressAutocomplete;
