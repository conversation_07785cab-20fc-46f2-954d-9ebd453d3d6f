'use client';

import { ReactNode, useEffect } from 'react';

import { Toaster } from 'react-hot-toast';

import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { createCookie } from '@/app/actions/cookies';

dayjs.extend(localeData);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(isBetween);
dayjs.extend(customParseFormat);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs().localeData();

type Props = {
  children: ReactNode;
};

const GlobalProviders = ({ children }: Props) => {
  // useEffect(() => {
  //   console.log('GlobalProviders');
  //   createCookie(
  //     'token',
  //     'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.-bAXyYfHIFxSddbP07CS6wPL1qgIv9DUxjTQYDtPC-E'
  //   );
  // }, []);

  return (
    <>
      {children}
      <Toaster position="top-right" />
    </>
  );
};

export default GlobalProviders;
