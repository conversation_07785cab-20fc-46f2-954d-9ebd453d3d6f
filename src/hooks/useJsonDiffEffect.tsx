import { useEffect, useRef } from 'react';
import _ from 'lodash';

function useJsonDiffEffect(
  effect: (diff: Record<string, { from: any; to: any }>) => void,
  deps: Record<string, any>
) {
  const prevDeps = useRef<Record<string, any> | null>(null);

  useEffect(() => {
    if (prevDeps.current) {
      const diff: Record<string, { from: any; to: any }> = {};

      _.forEach(_.keys({ ...prevDeps.current, ...deps }), (key) => {
        if (!_.isEqual(prevDeps.current?.[key], deps[key])) {
          diff[key] = { from: prevDeps.current?.[key], to: deps[key] };
        }
      });

      if (!_.isEmpty(diff)) {
        effect(diff);
      }
    }
    prevDeps.current = _.cloneDeep(deps); // keep a safe copy
  }, [deps, effect]);
}

export default useJsonDiffEffect;
