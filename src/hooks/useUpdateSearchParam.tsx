'use client';

import { useRouter, useSearchParams } from 'next/navigation';

export function useUpdateSearchParam() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const updateParam = (key: string, value: string) => {
    const entries = Array.from(searchParams.entries());

    let found = false;
    const updated = entries.map(([k, v]) => {
      if (k === key) {
        found = true;
        return [k, value];
      }
      return [k, v];
    });

    if (!found) {
      updated.push([key, value]);
    }

    const queryString = updated
      .map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
      .join('&');

    router.replace(`?${queryString}`);
  };

  return updateParam;
}
