"use client";

import { useCallback, useEffect, useState } from "react";

import { PropertyRentalRates } from "@/types/calendar";
import { getPropertyDetails } from "@/app/actions/property";
import { Property } from "@/types/property";
import dayjs from "dayjs";

const usePropertyRatesAndRules = (
  propertyId: number,
  selectionStart: string,
  selectionEnd: string
) => {
  const [data, setData] = useState<PropertyRentalRates[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchData = useCallback(() => {
    setIsLoading(true);
    getPropertyDetails<Property>(propertyId)
      .then((_data) => {
        const minDate = dayjs(selectionStart, "YYYY-MM-DD").subtract(1, "day");
        const maxDate = dayjs(selectionEnd, "YYYY-MM-DD");
        const rates = (_data?.rates ?? [])?.filter((_a) =>
          dayjs(_a.from_date).isBetween(minDate, maxDate)
        );
        setData(rates);
        setIsLoading(false);
      })
      .catch((_error) => {
        console.log("failed to fetch Rates and Rules", _error);
        setIsLoading(false);
      });
  }, [propertyId, selectionEnd, selectionStart]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, setData, isLoading, fetchData };
};

export default usePropertyRatesAndRules;
