import { Nullable } from "./common";

export enum ServiceProviderType {
  ALARM_COMPANY = "alarm company",
  CARETAKER = "caretaker",
  CLEANER = "cleaner",
  ELECTRICIAN = "electrician",
  LANDSCAPER = "landscaper",
  PLUMBER = "plumber",
  TRASH_SERVICE = "trash service",
  OTHER = "other",
}

export type PropertyServiceProvider = {
  sp_uuid: string;
  listing_address: string;
  comments: Comment[];
  type: ServiceProviderType;
  company: number;
  company_name: string;
  alarm_code: string;
  contact_name: string;
  email: string;
  cell_phone: string;
  work_phone: Nullable<string>;
  pickup_days: any[];
  listing: number;
};

export type ServiceProviderPayload = {
  sp_uuid?: string;
  type: ServiceProviderType;
  company: Nullable<number>;
  alarm_code?: Nullable<string>;
  contact_name: Nullable<string>;
  email: Nullable<string>;
  cell_phone: Nullable<string>;
  work_phone: Nullable<string>;
  pickup_days?: any[];
  listing: number;
};

export type Comment = {
  comment_uuid: string;
  content: string;
  date: string;
  posted_by: string;
  user: number;
  user_avatar?: Nullable<string>;
};

export type ServiceProviderCompany = {
  id: number;
  type: ServiceProviderType;
  company_name: Nullable<string>;
  contact_name: Nullable<string>;
  email: Nullable<string>;
  cell_phone: Nullable<string>;
  work_phone: Nullable<string>;
  alarm_code: Nullable<string>;
};

export type ServiceProviderCompanyPayload = {
  id?: number;
  type: ServiceProviderType;
  company_name: Nullable<string>;
  contact_name: Nullable<string>;
  email: string;
  cell_phone: string;
  work_phone: Nullable<string>;
  alarm_code: Nullable<string>;
};
