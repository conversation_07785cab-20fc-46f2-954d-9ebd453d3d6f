import {
  AvailabilityType,
  CalendarLeasedType,
  PropertyAvailability,
  PropertyRentalRates,
} from './calendar';
import { Nullable } from './common';

export type PropertyArea = {
  id: number;
  name: string;
};

export type ItemType = {
  id: number;
  name: string;
};

export type Bathroom = {
  bathroom_uuid: string;
  bidet: boolean;
  toilet: boolean;
  tub: boolean;
  combination_tub: boolean;
  jetted_tub: boolean;
  shower: boolean;
  outdoor_shower: boolean;
  bedroom: null;
  type: ItemType;
};

export type Bed = {
  bed_uuid: string;
  number: number;
  type: ItemType;
};

export type Bedroom = {
  bedroom_uuid: string;
  beds: Bed[];
  en_suite: boolean;
  floor_level: ItemType;
};

export type PropertyImage = {
  image_uuid: string;
  url: string;
  small_url: string;
  alt_text: Nullable<string>;
  order: number;
  caption: string;
  tags: string[];
  object: string;
};

export interface Brokerage {
  id: number;
  name: string;
  email: string;
  logo_desktop: Nullable<string>;
  logo_mobile: Nullable<string>;
  created_at: Nullable<string>;
  updated_at: Nullable<string>;
  deleted_at: Nullable<string>;
  order: number;
}

export type PropertyRequirement = {
  pet_allow_label: string;
  min_night_stay: number;
  cleaning_fee: Nullable<number>;
  cleaning_hours: number;
  pet_allow: string;
  pet_fee: string;
  security_deposit_percentage: Nullable<number>;
  min_security_deposit: Nullable<number>;
  commission_percentage: Nullable<number>;
  payment_number: number;
  turnover_day: Nullable<string>;
  checkin_time: string;
  checkout_time: string;
  charge_community_impact_fee: boolean;
};

export type PropertyPaymentInformation = {
  id: number;
  first_name: string;
  last_name: string;
  street1: string;
  street2: string;
  city: string;
  state: string;
  country: string;
  zip_code: Nullable<string>;
  qb_vendor_id: Nullable<string | number>;
  listing: number;
  contact: Nullable<string | number>;
};

export type PropertyMetadata = {
  title_tag: string;
  meta_description: string;
  meta_robots: string;
  meta_canonical: string;
  slug: string;
};

export type Property = {
  listing_id: number;
  nr_listing_id: number;
  bathrooms: Bathroom[];
  bedrooms: Bedroom[];
  images: PropertyImage[];
  rates: PropertyRentalRates[];
  availabilities: PropertyAvailability[];
  requirement: PropertyRequirement;
  payment_information: PropertyPaymentInformation;
  homeowner: Nullable<{
    contact_id: number;
    name: string;
    cell_phone: string;
    email1: string;
  }>;
  peak_rate: Nullable<string | number>;
  shoulder_rate: Nullable<string | number>;
  avg_rating: Nullable<string | number>;
  ratings_count: Nullable<number>;
  key_number: Nullable<string>;
  publish: boolean;
  other_rental_firms: any[];
  latest_leased_info: {
    lease_id: number;
    leased_at: string;
    leased_by: string;
  };
  deactivated_reason: Nullable<string>;
  house_phone: Nullable<string | number>;
  guidebook_link: Nullable<string>;
  virtual_tour_link: Nullable<string>;
  occupancy_tax_number: Nullable<string>;
  short_term_rental_permit_number: Nullable<string>;
  massachusetts_roc_number: Nullable<string>;
  address: string;
  zip_code: string;
  headline: string;
  description: string;
  short_description: string;
  first_floor: Nullable<string>;
  second_floor: Nullable<string>;
  third_floor: Nullable<string>;
  lower_level: Nullable<string>;
  other_structure: Nullable<string>;
  bedroom_number: number;
  bathroom_number: number;
  half_bathroom_number: number;
  capacity: number;
  air_conditioning: boolean;
  ac_types: ItemType[];
  clothes_dryer: boolean;
  fan: boolean;
  fan_quantity: number;
  fireplace: boolean;
  fireplace_quantity: number;
  gym: boolean;
  hair_dryer: boolean;
  heating: boolean;
  iron: boolean;
  ironing_board: boolean;
  jacuzzi: boolean;
  linen: boolean;
  parking: boolean;
  parking_quantity: number;
  safe: boolean;
  towel: boolean;
  washing_machine: boolean;
  wifi: boolean;
  wifi_network_name: Nullable<string>;
  wifi_password: Nullable<string>;
  disposal: boolean;
  counter_seating: boolean;
  dining_area: boolean;
  dining_room: boolean;
  outdoor_dining_area: boolean;
  seating: number;
  dining_description: '';
  bluetooth_speaker: boolean;
  book: boolean;
  dvd_player: boolean;
  tv: boolean;
  tv_quantity: number;
  bbq_tool: boolean;
  blender: boolean;
  coffee_maker: boolean;
  dish_or_cup_or_utensil: boolean;
  dishwasher: boolean;
  food_processor: boolean;
  ice_maker: boolean;
  ice_tray: boolean;
  lobster_pot: boolean;
  lobster_utensil: boolean;
  microwave: boolean;
  oven: boolean;
  pot_or_pan: boolean;
  refrigerator: boolean;
  stove: boolean;
  tea_kettle: boolean;
  toaster: boolean;
  beach_chair: boolean;
  beach_chair_quantity: number;
  beach_towel: boolean;
  beach_towel_quantity: number;
  beach_umbrella: boolean;
  bicycle: boolean;
  bicycle_quantity: number;
  cooler: boolean;
  deck: boolean;
  grill: boolean;
  hot_tub_or_spa: boolean;
  lawn_or_garden: boolean;
  outdoor_furniture: boolean;
  outdoor_shower: boolean;
  patio: boolean;
  pool: boolean;
  porch: boolean;
  tennis_court: boolean;
  distance_to_beach: string;
  distance_to_the_hub: string;
  year_built: Nullable<number>;
  year_renovated: Nullable<number>;
  waterfront: boolean;
  water_view: boolean;
  not_renting_year: Nullable<number>;
  walk_to_beach: boolean;
  calendar_update_email: boolean;
  longitude: number;
  latitude: number;
  coordinates: {
    type: string;
    coordinates: [number, number];
  };

  user: number;
  share_listing_with: Nullable<any>;
  priority: ItemType;
  occupancy_tax_file: Nullable<any>;
  short_term_rental_permit_file: Nullable<any>;
  massachusetts_roc_file: Nullable<any>;
  area: ItemType;
  heating_type: ItemType;
  tv_service: ItemType;
  coffee_maker_type: ItemType;
  stove_type: Nullable<string>;
  toaster_type: Nullable<ItemType>;
  grill_type: ItemType;
  pool_type: ItemType;
  tennis_court_type: number;
  min_price: number;
  max_price: number;
  featured_amenities: string[];
  deleted_at: Nullable<string>;
} & PropertyMetadata;

export type PropertyNote = {
  comment_uuid: string;
  content: string;
  date: string;
  posted_by: string;
  user: number;
  user_avatar: Nullable<string>;
};

export type ListingAddressPayload = {
  listing_id: number;
  key_number: Nullable<string>;
  address: string;
  area_name: string;
  url: string;
  small_url: string;
  slug: string;
  owner_email: Nullable<string>;
  owner_first_name: Nullable<string>;
  owner_last_name: Nullable<string>;
};

export type ImageUploaded = {
  file_name: string;
  object_uuid: string;
  path: string;
  small_url: string;
  url: string;
};

export type FileUploaded = {
  id: number;
  image_url: string;
  created_at: string;
  updated_at: string;
  title: string;
  link: string;
  is_active: boolean;
};

export type PropertySeasonalPolicy = {
  start_date: string;
  end_date: string;
  status: AvailabilityType;
  rate: number;
};

export type ShareListingPayload = {
  listings: string[];
  recipents: number[];
  other_email?: string;
  arrival_date: Nullable<string>;
  departure_date: Nullable<string>;
  agent_comments: string;
};

export type EmailContactPayload = {
  to_emails: string[];
  cc_emails: string[];
  email_subject: string;
  email_body: string;
};

export type Rent = {
  rent: number;
  fee: number;
  tax: number;
  adjusted_checkin_date: string;
  adjusted_checkout_date: string;
  discount: number;
  discount_type: string;
};

export type ListingShort = {
  availabilities: PropertyAvailability[];
  checkin_time: string;
  checkout_time: string;
  security_deposit_percentage: number;
  min_security_deposit: Nullable<number>;
  commission_percentage: number;
  charge_community_impact_fee: boolean;
};

export type Listing = {
  listing_id: number;
  key_number: string;
  address: string;
  bedroom_number: number;
  bathroom_number: number;
  half_bathroom_number: number;
  capacity: number;
  area_name: string;
  owner_first_name: string;
  owner_last_name: string;
  owner_email: string;
  turnover_day: string;
  priority: number;
  not_renting_year: Nullable<number>;
  url: string;
  small_url: string;
  calendar_updated_at: string;
  latest_leased_info: {
    lease_id: number;
    leased_at: string;
    leased_by: string;
  };
  slug: string;
  avg_rating: Nullable<number>;
  ratings_count: number;
  virtual_tour_link: Nullable<string>;
  peak_rate: Nullable<number>;
  min_price: number;
  max_price: number;
  year_min_price: number;
  year_max_price: number;
  rates: PropertyRentalRates[];
};
