import { Nullable } from './common';

export const NOT_AVAILABLE_RETURNING_TENANT_INFO = 'Not Available';

export type OtherFees = {
  amount: string;
  reason: string;
  taxable: boolean;
  custom?: boolean;
};

export type SignInfo = {
  url: string;
  file_name: string;
  signed_at: number;
};

export type BillInfo = {
  processing_fee: string;
  tax: string;
  rent: string;
  security_deposit: string;
  other_fee: string;
};

export type DisbursementInfo = {
  disbursement_form_uuid: string;
  processing_fee: string;
  tax: string;
  agent_commission: string;
  office_commission: string;
  co_broke_commission: string;
  rent_to_owner: string;
  security_deposit: string;
  other_fee: string;
  submit_at: Nullable<string>;
};

export type Transaction = {
  transaction_uuid: string;
  amount: string;
  payment_date: string;
  payment_method: string;
  reference: Nullable<string>;
  balance_after_transaction: string;
  user: Nullable<number>;
  import_id?: number;
};

export type Adjustment = {
  adjustment_uuid: string;
  amount: string;
  reference: string;
  user: number;
  created_at: string;
  payment_date?: string;
};

export type Payments = {
  payment_uuid: string;
  status: 'Paid' | 'Current' | string;
  bill: BillInfo;
  disbursement_form: DisbursementInfo;
  created_at: string;
  updated_at: string;
  deleted_at: Nullable<string>;
  period: number;
  due_date: string;
  total_amount: string;
  amount_received: string;
  balance: string;
  payment_date: string;
  payment_method: string;
  reference: Nullable<string>;
  check_request_at: string;
  qb_info: Record<string, string>;
  import_id: number;
  lease: number;
  transactions: Transaction[];
  outgoing_adjustments: Adjustment[];
  incoming_adjustments: Adjustment[];
  comments: any[];
};

export type SecurityDepositReturnInfo = {
  tenant: string;
  homeowner: string;
  other_pay?: string;
  amount_held?: number;
  sd_tenant_bill_id?: string;
  sd_tenant_bill_payment_ref?: string;
  sd_homeowner_bill_id?: string;
  sd_tenant_bill_doc_number?: string;
  sd_homeowner_bill_doc_number?: string;
  sd_homeowner_bill_payment_ref?: string;
  sd_tenant_bill_payment_date?: string;
  sd_homeowner_bill_payment_date?: string;
};

export type AttachFile = {
  object_uuid: string;
  url: string;
  small_url: Nullable<string>;
  path: string;
  file_name: string;
  ext: string;
};

export type SecurityDeposit = {
  lease: number;
  comments: string[];
  created_at: string;
  updated_at: string;
  deleted_at: Nullable<string>;
  amount: string;
  amount_received: string;
  balance: string;
  return_date: Nullable<string>;
  return_info: SecurityDepositReturnInfo;
  attach_files: AttachFile[];
  bill_com_info: Record<string, any>;
  status: string;
  return_deadline: string;
};

export type DamageClaim = {
  lease: number;
  comments: string[];
  created_at: string;
  updated_at: string;
  deleted_at: Nullable<string>;
  amount: string;
  amount_paid: string;
  balance: string;
  payment_date: Nullable<string>;
  status: string;
  qb_info: Record<string, any>;
  reference: Nullable<string>;
  attach_files: AttachFile[];
  claim_deadline: string;
};

export type Lease = {
  lease_id: number;
  user: number;
  tenant: number;
  listing: number;
  other_fees: OtherFees[];
  payments: Payments[];
  security_deposit: Nullable<SecurityDeposit>;
  damage_claim: Nullable<DamageClaim>;
  security_deposit_amount: string;
  total_amount: number;
  tax_exempt_file_name: Nullable<string>;
  sign_info: {
    signature_request: {
      signatures: {
        signed_at: number;
        sent_timestamps?: number[];
      }[];
    };
  };
  created_at: string;
  updated_at: string;
  deleted_at: string;
  old_lease_id: Nullable<string>;
  listing_address: string;
  is_co_broke_lease: boolean;
  co_broke_agency: Nullable<string>;
  represent: Nullable<string>;
  commission_percentage: number;
  agent_commission: string;
  office_commission: string;
  checkin_time: string;
  checkout_time: string;
  arrival_date: string;
  departure_date: string;
  tax_exempt: boolean;
  rent: string;
  processing_fee: string;
  tax: string;
  other_fee: string;
  clauses: string[];
  comment: Nullable<string>;
  is_block_date: boolean;
  sign_file_info: SignInfo;
  past_lease_info: Nullable<string>;
  user_email: string;
  user_first_name: string;
  user_last_name: string;
  tenant_email: string;
  tenant_first_name: string;
  tenant_last_name: string;
  owner_email: string;
  owner_first_name: string;
  owner_last_name: string;
  payee_name: string;
  import_id: number;
  akia_reservation_id: Nullable<string>;
  owner: number;
  tax_exempt_file: Nullable<string>;
  status: string;
  returning_tenant_info: Nullable<string>;
};

export type LeasePayload = {
  tenant?: number;
  listing: number;
  listing_address: string;
  arrival_date: string;
  departure_date: string;
  checkin_time: string;
  checkout_time: string;
  rent: number;
  processing_fee: number;
  tax: number;
  security_deposit_amount?: number;
  commission_percentage: number;
  payments: {
    period: number;
    due_date: string;
    total_amount: string;
    bill: {
      rent: number;
      processing_fee?: number;
      tax?: number;
      security_deposit?: number;
      other_fee?: number;
    };
  }[];
  other_fees: {
    amount: number;
    reason: string;
    taxable: boolean;
    custom?: boolean;
  }[];
  other_fee: number;
  clauses: string[];
  comment: string | null;
  user?: number;
  total_amount: number;
  tax_exempt?: boolean;
};

export type LeaseComment = {
  comment_uuid: string;
  date: string;
  posted_by: string;
  content: string;
  user: number;
  user_avatar?: string;
};

export enum LeaseQbInfo {
  Occupancy_Tax_Date = 'occupancy_tax_date',
  Occupancy_Tax_Ref = 'occupancy_tax_ref',
  Processing_fee_Date = 'processing_fee_date',
  Processing_fee_Ref = 'processing_fee_ref',
  Agent_Commission_Date = 'agent_bill_payment_date',
  Agent_Commission_Ref = 'agent_bill_payment_ref',
  Rent_to_Owner_Date = 'homeowner_bill_payment_date',
  Rent_to_Owner_Ref = 'homeowner_bill_payment_ref',
  Other_fee_Date = 'other_fee_date',
  Other_fee_Ref = 'other_fee_ref',
  co_broke_commission_date = 'co_broke_commission_date',
  co_broke_commission_ref = 'co_broke_commission_ref',
  office_commission_date = 'office_commission_date',
  office_commission_ref = 'office_commission_ref',
  security_deposit_date = 'security_deposit_date',
  security_deposit_ref = 'security_deposit_ref',
}
