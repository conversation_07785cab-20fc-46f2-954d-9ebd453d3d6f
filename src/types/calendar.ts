import { Nullable } from "./common";
import { Brokerage } from "./property";

export enum CalendarViewType {
  MONTH = "month",
  YEAR = "year",
}

export enum AvailabilityType {
  AVAILABLE = "Available",
  UNAVAILABLE = "Unavailable",
  PENDING = "Pending",
}

export enum CalendarLeasedType {
  LEASED = "LEASED",
  PENDING = "PENDING",
  BLOCKED = "BLOCKED",
}

export enum BlockedType {
  OWNER_TIME = "owner",
  LEASED = "leased",
  PENDING = "draft",
  OTHER = "other",
}

export enum ManageDatesStep {
  AVAILABILITY = "AVAILABILITY",
  RATES = "RATES",
  BOOKING_RULES = "BOOKING_RULES",
}

export enum RtlSlidePane {
  VIEW_TYPE_SELECTOR = "VIEW_TYPE_SELECTOR",
  MOBILE_YEAR_VIEW = "MOBILE_YEAR_VIEW",
}

export interface BookingSourceID {
  profilePartnerId: number;
  profilePartner: string;
  email: string;
  logoDesktop: Nullable<string>;
  logoMobile: Nullable<string>;
}

export interface NrAgentID {
  nrAgentId: number;
  firstname: string;
  lastname: string;
  userLoginEmail: string;
  blocked: boolean;
  nrUserId: number;
  photoUrl: Nullable<string>;
  externalId: Nullable<string>;
}

export type PropertyAvailability = {
  availability_uuid: string;
  rent_amount: number;
  from_date: string;
  to_date: string;
  custom_agent: Nullable<string>;
  custom_source: Nullable<string>;
  renter_name: Nullable<string>;
  agreement_url: string;
  brokerage: Nullable<Brokerage>;
  type: BlockedType;
  availability: boolean;
  lease: Nullable<string>;
};

export type PropertyAvailabilityPayload = {
  availability_id?: string;
  rent_amount: Nullable<number>;
  from_date: string;
  to_date: string;
  custom_agent: Nullable<string>;
  custom_source: Nullable<string>;
  renter_name: Nullable<string>;
  agreement_url: Nullable<string>;
  brokerage: Nullable<number>;
  type: BlockedType;
  availability: boolean;
  lease: Nullable<string>;
};

export type PropertyRentalRates = {
  rate_uuid: string;
  from_date: string;
  to_date: string;
  amount: string;
  weekly_amount: string;
  is_nightly_rate: boolean;
  weekly_discount: Nullable<number>;
  monthly_discount: Nullable<number>;
  minimum_booking_amount: Nullable<number>;
  minimum_nights_stay: Nullable<number>;
  allow_checkin: boolean;
  allow_checkout: boolean;
  early_bird_discount: Nullable<number>;
  early_bird_discount_months: Nullable<number>;
  custom_discount: Nullable<number>;
  custom_discount_days: Nullable<number>;
  apply_last_minute_rule: Nullable<boolean>;
  last_minute_rule_days: Nullable<number>;
  last_minute_discount: Nullable<number>;
  last_minute_minimum_nights_stay: Nullable<number>;
  last_minute_allow_checkin: Nullable<boolean>;
  last_minute_allow_checkout: Nullable<boolean>;
  last_minute_minimum_booking_amount: Nullable<number>;
};

export type PropertyRentalRatePayload = {
  amount?: number;
  weekly_amount?: number;
  from_date: string;
  to_date: string;
  minimum_booking_amount?: Nullable<number>;
  minimum_nights_stay?: Nullable<number>;
  note?: Nullable<string>;
  weekly_discount?: Nullable<number>;
  monthly_discount?: Nullable<number>;
  early_bird_discount?: Nullable<number>;
  early_bird_discount_months?: Nullable<number>;
  custom_discount?: Nullable<number>;
  custom_discount_days?: Nullable<number>;
  apply_last_minute_rule?: Nullable<boolean>;
  last_minute_discount?: Nullable<number>;
  last_minute_rule_days?: Nullable<number>;

  last_minute_options_by_weekday?: {
    monday: LastMinuteOptions;
    tuesday: LastMinuteOptions;
    wednesday: LastMinuteOptions;
    thursday: LastMinuteOptions;
    friday: LastMinuteOptions;
    saturday: LastMinuteOptions;
    sunday: LastMinuteOptions;
  };
  options_by_weekday: OptionsByWeekday;
};

export type OptionsByWeekday = {
  monday?: WeekDayOptions;
  tuesday?: WeekDayOptions;
  wednesday?: WeekDayOptions;
  thursday?: WeekDayOptions;
  friday?: WeekDayOptions;
  saturday?: WeekDayOptions;
  sunday?: WeekDayOptions;
};

export type LastMinuteOptions = {
  lastMinuteDiscount: number;
  lastMinuteRuleDays: number;
  lastMinuteMinimumNightsStay: number;
  last_minute_allow_checkin: boolean;
  last_minute_allow_checkout: boolean;
  lastMinuteMinimumBookingAmount: Nullable<number>;
};

export type WeekDayOptions = {
  allow_checkin?: boolean;
  allow_checkout?: boolean;
  rate?: number | string;
  update_rate?: boolean;
};

export type WeekDaysChecked = {
  [x: string]: boolean;
};

export type ShareCalendarPayload = {
  nrPropertyId: number;
  emails: string[];
  publishProfileIds: number[];
};

export type UpdateLog = {
  content: UpdateLogContent | UpdateLogContent[];
  created_at: string;
  type: UpdateLogType;
  user_name: string;
};

export type UpdateLogContent = {
  from_date: string;
  to_date: string;
  new_value: string | boolean;
  old_value: string | boolean;
  type?: BlockedType;
};

export enum UpdateLogType {
  VALIDATE = "validate",
  AVAILABILITY = "availability",
  RATES = "rates",
  AVAILABILITY_AND_RATES = "availability and rates",
}

export enum NoteActionType {
  EDIT = "EDIT",
  DELETE = "DELETE",
}
