export enum ProgressStatus {
  LOADING = "LOADING",
  UPDATING = "UPDATING",
  DELETING = "DELETING",
  SUCCESSFUL = "SUCCESSFUL",
  FAILED = "FAILED",
}

export type IPagination = {
  count?: number;
  currentPage?: number;
  next?: any;
  previous?: any;
};

export type Nullable<T> = T | null;

export type EntityID = string | number;

export type ProfilePartner = {
  id: number;
  name: string;
  email: string;
  logo_desktop: Nullable<string>;
  logo_mobile: Nullable<string>;
  created_at: Nullable<string>;
  updated_at: Nullable<string>;
  deleted_at: Nullable<string>;
  order: number;
};
