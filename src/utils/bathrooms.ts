import { Nullable } from "@/types/common";
import { Bathroom } from "@/types/property";

export type BathroomPayload = {
  bathroom_uuid?: string;
  bidet: boolean;
  toilet: boolean;
  tub: boolean;
  combination_tub: boolean;
  jetted_tub: boolean;
  shower: boolean;
  outdoor_shower: boolean;
  bedroom: Nullable<string>;
  type: Nullable<number>;
  checked?: boolean;
};

export const formatBathroomsForForm = (
  bathrooms: Bathroom[]
): BathroomPayload[] => {
  return bathrooms.map((_b) => ({
    bathroom_uuid: _b.bathroom_uuid,
    bidet: _b.bidet,
    toilet: _b.toilet,
    tub: _b.tub,
    combination_tub: _b.combination_tub,
    jetted_tub: _b.jetted_tub,
    shower: _b.shower,
    outdoor_shower: _b.outdoor_shower,
    bedroom: _b.bedroom,
    type: _b.type.id,
    checked: !!_b.bedroom,
  }));
};
