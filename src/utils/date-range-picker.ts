import { PropertyAvailability, PropertyRentalRates } from '@/types/calendar';
import { parseDateString } from './common';
import {
  addMonths,
  format,
  isSameDay,
  isSameMonth,
  isWithinInterval,
  parse,
  startOfDay,
  startOfMonth,
} from 'date-fns';
import { DateRange } from 'react-day-picker';

export const isDateBetweenTwoDates = (
  startDate: string,
  endDate: string,
  date: Date
): boolean => {
  const start = parse(startDate, 'yyyy-MM-dd', new Date());
  const end = parse(endDate, 'yyyy-MM-dd', new Date());

  return (
    isWithinInterval(date, {
      start: start,
      end: end,
    }) ||
    isSameDay(date, start) ||
    isSameDay(date, end)
  );
};

export const getBlockedStartAndEndDates = (
  availabilityRanges: PropertyAvailability[] = []
) => {
  const start: Date[] = [];
  const end: Date[] = [];

  availabilityRanges.map((_r) => {
    const { from_date, to_date } = _r;
    start.push(parseDateString(from_date));
    end.push(parseDateString(to_date));
  });

  return {
    start,
    end,
  };
};

export const getBlockedRangesForDate = (
  availabilityRanges: PropertyAvailability[] = [],
  date: Date
) =>
  availabilityRanges.filter((_r) =>
    isDateBetweenTwoDates(_r.from_date, _r.to_date, date)
  );

export const formatDateRangePickerRentalRates = (
  rentalRates: PropertyRentalRates[]
) =>
  [...Array(24).keys()].reduce((acc, num) => {
    const currentDt = addMonths(startOfMonth(startOfDay(new Date())), num);
    return {
      ...acc,
      [format(currentDt, 'MMM-yyyy')]: rentalRates.filter((_r) =>
        isSameMonth(currentDt, parseDateString(_r.from_date))
      ),
    };
  }, {} as { [_s: string]: PropertyRentalRates[] });

export const checkIfDateIsStartDateOfRange = (
  dt: Date,
  blockedStartDates: Date[]
) => {
  if (blockedStartDates.length === 0) {
    return false;
  }

  return blockedStartDates.some((_d) =>
    isSameDay(startOfDay(dt), startOfDay(_d))
  );
};

export const checkIfDateIsEndDateOfRange = (
  dt: Date,
  blockedEndDates: Date[]
) => {
  if (blockedEndDates.length === 0) {
    return false;
  }

  return blockedEndDates.some((_d) =>
    isSameDay(startOfDay(dt), startOfDay(_d))
  );
};
