import { LeadStatus } from '@/types/leads';

export const getLeadsStatus = (status: LeadStatus) => {
  switch (status) {
    case LeadStatus.NOT_STARTED:
      return 'New';

    case LeadStatus.DISCUSSION:
    case LeadStatus.PROPOSAL_SENT:
    case LeadStatus.CLOSED_WON:
      return 'Contacted';

    case LeadStatus.CLOSED_LOST:
      return 'Lost';
  }
};

export const getLeadFlexibilityString = (flexibility: string) => {
  switch (flexibility) {
    case 'flexible_dates':
      return 'Flexible Dates';

    case 'exact_dates':
      return 'Exact Dates';

    case 'uncertain':
      return 'Dates Uncertain';
  }
};
