const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';
import { parse } from 'date-fns';
import isArray from 'lodash/isArray';
export const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const currencyFormatterRound = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
});

export const currencyFormatterOneDecimal = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 1,
  minimumFractionDigits: 0,
});

export const isHover = (e: HTMLElement) =>
  e?.parentElement?.querySelector(':hover') === e;

export const getSearchParams = (
  searchParams: Record<string, string | number>
) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (isArray(value)) value.forEach((x) => params.push(key + '=' + x));
    else params.push(key + '=' + value);
  }
  const str = params.join('&');
  return str;
};

export const isJsonString = (str: string) => {
  try {
    const o = JSON.parse(str);
    if (o && typeof o === 'object') {
      return true;
    }
  } catch (e) {
    return false;
  }
  return false;
};

export const aRandomString = (length = 10): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array(length)
    .fill('')
    .reduce(
      (prev) =>
        prev + characters.charAt(Math.floor(Math.random() * characters.length)),
      ''
    );
};

export const checkIfDevelopment = () => BASE_URL.includes('dev');

export const isLocalhost = (): boolean => {
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1';
  }
  return false;
};

export const parseDateString = (dateString: string, format = 'yyyy-MM-dd') =>
  parse(dateString, format, new Date());

export const getFullName = (f_name?: string, l_name?: string) => {
  if (!f_name && !l_name) {
    return '';
  }

  return `${f_name ?? ''} ${l_name ?? ''}`;
};

export function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export function parseStringPrice(s: string) {
  return Number(parseFloat(s).toFixed(2));
}

export function ensureNumberHas2Decimals(num: number) {
  return Number(num.toFixed(2));
}

export const truncate = (str: string, length: number) => {
  return str.length > length ? str.substring(0, length) + '...' : str;
};

export function formatPhoneNumber(num: string) {
  // Remove non-digit characters
  const digits = num.replace(/\D/g, '');

  // Match 10 digits and format
  if (digits.length === 10) {
    return digits.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
  }

  return num; // return as-is if not 10 digits
}
