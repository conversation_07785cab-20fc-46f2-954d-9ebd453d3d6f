import {
  OptionsByWeekday,
  PropertyRentalRatePayload,
  PropertyRentalRates,
  WeekDaysChecked,
} from "@/types/calendar";
import { Nullable } from "@/types/common";

import dayjs from "dayjs";
import { uniqBy, mean } from "rambda";

import { currencyFormatterOneDecimal, currencyFormatterRound } from "./common";

const WEEKDAY_KEYS = [
  "sunday",
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
];

export const formatCheckinCheckoutOptionsByWeekday = (
  checkin: WeekDaysChecked,
  checkout: WeekDaysChecked,
  turnoverDay?: Nullable<string>
): OptionsByWeekday => {
  const isEmptyCheckout = Object.keys(checkout).length === 0;
  const isEmptyCheckin = Object.keys(checkin).length === 0;
  const options = WEEKDAY_KEYS.reduce((acc, curr) => {
    const allow_checkin = checkin[curr] ? !!checkin[curr] : isEmptyCheckin;
    const allow_checkout = checkout[curr] ? !!checkout[curr] : isEmptyCheckout;
    return (acc = {
      ...acc,
      [curr]: {
        allow_checkin,
        allow_checkout,
        update_rate: false,
      },
    });
  }, {});

  return options;
};

export const formatOptionsByWeekdayForRates = (
  fe_options: OptionsByWeekday,
  be_options: OptionsByWeekday,
  rate?: number,
  turnoverDay?: string,
  isDayWiseRate?: boolean
) => {
  const weekdayOptions = WEEKDAY_KEYS.reduce((acc, curr) => {
    const dayOptions = {
      ...be_options[curr as keyof OptionsByWeekday],
      ...fe_options[curr as keyof OptionsByWeekday],
    };

    if (
      isNaN(Number(fe_options[curr as keyof OptionsByWeekday]?.rate)) &&
      isDayWiseRate
    ) {
      dayOptions.update_rate = false;
      delete dayOptions.rate;
    } else if (
      fe_options[curr as keyof OptionsByWeekday]?.rate !== "" &&
      !isNaN(Number(fe_options[curr as keyof OptionsByWeekday]?.rate)) &&
      isDayWiseRate
    ) {
      dayOptions.rate = Number(
        fe_options[curr as keyof OptionsByWeekday]?.rate
      );
    } else {
      dayOptions.rate = rate;
    }

    if (turnoverDay && turnoverDay === curr) {
      dayOptions.allow_checkin = true;
    }

    return (acc = {
      ...acc,
      [curr]: dayOptions,
    });
  }, {});

  return weekdayOptions;
};

export const formatRateAndRules = (
  data: PropertyRentalRates[],
  minimum_nights_stay: number
): Nullable<PropertyRentalRatePayload> => {
  if (data.length === 0) {
    return null;
  }
  const sundays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 0
  );
  const mondays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 1
  );
  const tuesdays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 2
  );
  const wednesdays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 3
  );
  const thursdays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 4
  );
  const fridays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 5
  );
  const saturdays = data.filter(
    (_date) => dayjs(_date.from_date).get("day") === 6
  );
  const sundayRate =
    sundays.length > 0
      ? uniqBy((_x) => Number(_x.amount), sundays).length === 1
        ? Number(sundays[0].amount)
        : `$${Number(
            Math.min(...sundays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...sundays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const mondayRate =
    mondays.length > 0
      ? uniqBy((_x) => Number(_x.amount), mondays).length === 1
        ? mondays[0].amount
        : `$${Number(
            Math.min(...mondays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...mondays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const tuesdayRate =
    tuesdays.length > 0
      ? uniqBy((_x) => Number(_x.amount), tuesdays).length === 1
        ? tuesdays[0].amount
        : `$${Number(
            Math.min(...tuesdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...tuesdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const wednesdayRate =
    wednesdays.length > 0
      ? uniqBy((_x) => Number(_x.amount), wednesdays).length === 1
        ? wednesdays[0].amount
        : `$${Number(
            Math.min(...wednesdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...wednesdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const thursdayRate =
    thursdays.length > 0
      ? uniqBy((_x) => Number(_x.amount), thursdays).length === 1
        ? thursdays[0].amount
        : `$${Number(
            Math.min(...thursdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...thursdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const fridayRate =
    fridays.length > 0
      ? uniqBy((_x) => Number(_x.amount), fridays).length === 1
        ? fridays[0].amount
        : `$${Number(
            Math.min(...fridays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...fridays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const saturdayRate =
    saturdays.length > 0
      ? uniqBy((_x) => Number(_x.amount), saturdays).length === 1
        ? saturdays[0].amount
        : `$${Number(
            Math.min(...saturdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}-$${Number(
            Math.max(...saturdays.map((_x) => Number(_x.amount)))
          ).toLocaleString("en-us")}`
      : undefined;
  const areWeeklyRentsSame =
    uniqBy((_x) => _x.weekly_amount, data).length === 1;
  const weeklyRent = areWeeklyRentsSame
    ? Number(data[0].weekly_amount)
    : mean(
        uniqBy((_x) => _x.weekly_amount, data).map((_d) =>
          Number(_d.weekly_amount)
        )
      );

  let options_by_weekday: OptionsByWeekday = getWeekdayKeys(
    data[0].from_date,
    data[data.length - 1].from_date
  ).reduce((acc, curr) => (acc = { ...acc, [curr]: { rate: "" } }), {});

  if (sundayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      sunday: {
        allow_checkin: !!sundays[0]?.allow_checkin,
        allow_checkout: !!sundays[0]?.allow_checkout,
        rate: sundayRate,
      },
    };
  }

  if (mondayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      monday: {
        allow_checkin: !!mondays[0]?.allow_checkin,
        allow_checkout: !!mondays[0]?.allow_checkout,
        rate: mondayRate,
      },
    };
  }

  if (tuesdayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      tuesday: {
        allow_checkin: !!tuesdays[0]?.allow_checkin,
        allow_checkout: !!tuesdays[0]?.allow_checkout,
        rate: tuesdayRate,
      },
    };
  }

  if (wednesdayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      wednesday: {
        allow_checkin: !!wednesdays[0]?.allow_checkin,
        allow_checkout: !!wednesdays[0]?.allow_checkout,
        rate: wednesdayRate,
      },
    };
  }

  if (thursdayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      thursday: {
        allow_checkin: !!thursdays[0]?.allow_checkin,
        allow_checkout: !!thursdays[0]?.allow_checkout,
        rate: thursdayRate,
      },
    };
  }

  if (fridayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      friday: {
        allow_checkin: !!fridays[0]?.allow_checkin,
        allow_checkout: !!fridays[0]?.allow_checkout,
        rate: fridayRate,
      },
    };
  }

  if (saturdayRate) {
    options_by_weekday = {
      ...options_by_weekday,
      saturday: {
        allow_checkin: !!saturdays[0]?.allow_checkin,
        allow_checkout: !!saturdays[0]?.allow_checkout,
        rate: saturdayRate,
      },
    };
  }
  const areRatesSame = uniqBy((_x) => Number(_x.amount), data).length === 1;
  const rate = areRatesSame
    ? Number(data[0].amount)
    : Math.round(
        data.reduce((acc, curr) => (acc = Number(curr.amount) + acc), 0) /
          data.length
      );
  return {
    amount: Number((weeklyRent / 7).toFixed(2)),
    weekly_amount: Number(weeklyRent.toFixed(2)),
    from_date: data?.[0].from_date,
    to_date: data[data.length - 1].to_date,
    minimum_booking_amount: data[0].minimum_booking_amount,
    minimum_nights_stay: data[0]?.minimum_nights_stay ?? minimum_nights_stay,
    early_bird_discount: !!data[0]?.early_bird_discount
      ? Number(data[0]?.early_bird_discount)
      : null,
    early_bird_discount_months: data[0]?.early_bird_discount_months,
    custom_discount: !!data[0]?.custom_discount
      ? Number(data[0]?.custom_discount)
      : null,
    custom_discount_days: data[0]?.custom_discount_days,
    last_minute_discount: !!data[0]?.last_minute_discount
      ? Number(data[0]?.last_minute_discount)
      : null,
    last_minute_rule_days: data[0]?.last_minute_rule_days,
    options_by_weekday,
  };
};

export const formatNullableRate = (rate?: Nullable<string>) =>
  rate ? Number(rate) : null;

export const formatDateItemRate = (rate: number) => {
  if (rate < 1000) {
    return `${currencyFormatterRound.format(rate)}`;
  } else {
    return `${currencyFormatterOneDecimal.format(rate / 1000)}k`;
  }
};

export const getCheckinDaysFromRates = (weekdayOptions: OptionsByWeekday) => {
  const keys = Object.keys(weekdayOptions);
  return keys.reduce((acc: { [x: string]: boolean }, curr) => {
    if ((weekdayOptions as any)[curr].allow_checkin) {
      return { ...acc, [curr]: true };
    }
    return acc;
  }, {});
};

export const getCheckoutDaysFromRates = (weekdayOptions: OptionsByWeekday) => {
  const keys = Object.keys(weekdayOptions);
  return keys.reduce((acc: { [x: string]: boolean }, curr) => {
    if ((weekdayOptions as any)[curr].allow_checkout) {
      return { ...acc, [curr]: true };
    }
    return acc;
  }, {});
};

// Get the weekday keys for the given range
export const getWeekdayKeys = (
  selectionStart: string,
  selectionEnd: string
) => {
  if (!selectionStart || !selectionEnd) {
    return [];
  }
  const start = dayjs(selectionStart, "YYYY-MM-DD").subtract(1, "day");
  const end = dayjs(selectionEnd, "YYYY-MM-DD");
  const daysInRange = end.diff(start, "day");
  const array = daysInRange >= 7 ? 7 : daysInRange;
  const weekdayKeys = Array(array)
    .fill(0)
    .map((_x, index) => {
      return WEEKDAY_KEYS[start.add(index + 1, "day").get("day")];
    });
  return weekdayKeys;
};

export const getPeakSeasonRate = (rates: PropertyRentalRates[]) => {
  if (!rates || rates.length === 0) {
    return { min: null, max: null, average: null };
  }

  const currentYear = dayjs().year();
  const peakSeasonStart = dayjs(`${currentYear}-06-15`, "YYYY-MM-DD");
  const peakSeasonEnd = dayjs(`${currentYear}-09-15`, "YYYY-MM-DD");

  // Filter rates that fall within peak season
  const peakSeasonRates = rates.filter((rate) => {
    const rateStart = dayjs(rate.from_date, "YYYY-MM-DD");
    const rateEnd = dayjs(rate.to_date, "YYYY-MM-DD");

    // Check if rate period overlaps with peak season
    return (
      (rateStart.isBefore(peakSeasonEnd) || rateStart.isSame(peakSeasonEnd)) &&
      (rateEnd.isAfter(peakSeasonStart) || rateEnd.isSame(peakSeasonStart))
    );
  });

  if (peakSeasonRates.length === 0) {
    return { min: null, max: null, average: null };
  }

  // Extract amounts and convert to numbers
  const amounts = peakSeasonRates.map((rate) => Number(rate.weekly_amount));

  const min = Math.min(...amounts);
  const max = Math.max(...amounts);
  const average =
    amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;

  return {
    min,
    max,
    average: Number(average),
  };
};

export const getPeakSeasonText = ({
  min,
  max,
  average,
}: {
  min: Nullable<number>;
  max: Nullable<number>;
  average: Nullable<number>;
}) => {
  if (min && max) {
    if (min === max) {
      return `${currencyFormatterRound.format(max ?? 0)}/wk`;
    }
    return `${currencyFormatterRound.format(
      min ?? 0
    )} - ${currencyFormatterRound.format(max ?? 0)}/wk`;
  } else if (average) {
    return `${currencyFormatterRound.format(average ?? 0)}/wk`;
  } else {
    return "No rates found";
  }
};
