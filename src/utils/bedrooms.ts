import { Nullable } from "@/types/common";
import { Bedroom } from "@/types/property";

export type BedroomPayload = {
  bedroom_uuid?: string;
  beds: {
    number: number;
    type: Nullable<number>;
  }[];
  en_suite?: boolean;
  floor_level: Nullable<number>;
};

export const formatBedroomsForForm = (
  bedrooms: Bedroom[]
): BedroomPayload[] => {
  return bedrooms.map((_b) => ({
    bedroom_uuid: _b.bedroom_uuid,
    en_suite: _b.en_suite,
    floor_level: _b.floor_level.id,
    beds: _b.beds.map((_bed) => ({
      number: _bed.number,
      type: _bed.type.id,
    })),
  }));
};
