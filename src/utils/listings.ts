import { AvailabilityType } from "@/types/calendar";
import { Nullable } from "@/types/common";

export const getAvailabilityText = (status: Nullable<AvailabilityType>) => {
  if (status === AvailabilityType.AVAILABLE) {
    return "Available";
  }

  if (status === AvailabilityType.PENDING) {
    return "Pending";
  }

  if (status === AvailabilityType.UNAVAILABLE) {
    return "Leased";
  }

  return "Available";
};
