import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/clients/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  future: {
    hoverOnlyWhenSupported: true,
  },
  theme: {
    extend: {
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          xs: '1rem',
          sm: '1.5rem',
        },
        screens: {
          sm: '600px',
          md: '728px',
          lg: '1024px',
          xl: '1200px',
          '2xl': '1200px',
        },
      },
      colors: {
        'roman-silver': '#828A95',
        'carolina-blue': '#15A5E5',
        'carolina-blue-20': '#D0EDFA',
        'carolina-blue-40': '#A1DBF5',
        'carolina-blue-60': '#ecf5ff',
        success: '#34A853',
        error: '#EB1648',
        info: '#FBBC04',
        outline: '#CCC',
        'gray-20': '#828A9533',
        pending: '#FEE9AE',
        'pending-dark': '#FFC41F',
        'black-80': '#333',
        'black-60': '#666',
        'black-40': '#999',
        'gray-blocked': '#E5E5E5',
        'medium-dark-gray': '#606266',
        navy: '#2C3E50',
        leased: 'rgba(212, 175, 55, 0.5)',
        'owner-time': 'rgba(103, 137, 147, 0.5)',
        other: 'rgba(222, 27, 75, 0.5)',
        'english-manor': '#6D83A3',
        'primary-text': '#09182C',
        label: '#43555A',
        'default-font': '#09182C',
        'primary-slate': '#678993',
        'light-slate': '#d9eaf0',
        'navy-blue': '#1C4A5E',
        'blue-main': '#15A5E5',
        'gray-main': '#6D7380',
        'red-main': '#EB1648',
        'light-gray': '#F5F9FF',
        'medium-gray': '#6D8994',
        'black-3': '#6E7E83',
        olive: '#4C737F',
        'olive-variant': '#5E7B87',
        disabled: '#DEE1EB',
        primary: '#E5F1F4',
        'dark-blue': '#074059',
        'dark-3': '#6E7E83',
        'dark-4': '#B1C4C9',
        platinium: '#E7E7E9',
        'gray-80': '#5E6774',
        'metal-gray': '#6D7380',
      },
      fontFamily: {
        poppins: 'var(--font-poppins)',
      },
      backgroundColor: {
        backdrop: 'rgba(0, 0, 0, 0.20)',
        'backdrop-40': 'rgba(0, 0, 0, 0.40)',
      },
      boxShadow: {
        header: '0px 2px 2px rgba(130, 138, 149, 0.2)',
        'mobile-header': '0px 0px 2px 2px rgba(130, 138, 149, 0.2)',
        card: '0px 4px 10px 0px rgba(0, 0, 0, 0.15)',
        'card-15': '0px 4px 4px 0 rgba(0, 0, 0, 0.15)',
        'card-25': '0px 4px 4px 0px rgba(0, 0, 0, 0.25)',
        'sidebar-mobile': '0px -5px 4px 0px rgba(0, 0, 0, 0.15)',
        modal: '0px 10px 20px 0px rgba(0, 0, 0, 0.40)',
        dropdown: '0 2px 12px 0 rgba(0,0,0,.1)',
        inner:
          'inset 0px 4px 4px 0px rgba(130, 138, 149, 0.2), inset 0 0px 0px -4px rgba(130, 138, 149, 0.2)',
      },
    },
  },
  daisyui: {
    themes: ['light'],
  },
  plugins: [require('daisyui')],
};
export default config;
